const db = require('../config/db');

const Ambiente = {
  async create({ titulo, id_usuario, id_inspecao, id_ambiente_pai, observacoes }) {
    const query = `
      INSERT INTO ambientes (titulo, id_usuario, id_inspecao, id_ambiente_pai, observacoes, criado_em)
      VALUES ($1, $2, $3, $4, $5, NOW())
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [titulo, id_usuario, id_inspecao, id_ambiente_pai, observacoes]);
      return rows[0];
    } catch (error) {
      throw error;
    }
  },

  async findAllByInspecaoId(id_inspecao) {
    const query = 'SELECT * FROM ambientes WHERE id_inspecao = $1 ORDER BY titulo;';
    try {
      const { rows } = await db.query(query, [id_inspecao]);
      return rows;
    } catch (error) {
      throw error;
    }
  },

  async findById(id) {
    const query = 'SELECT * FROM ambientes WHERE id = $1;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      throw error;
    }
  },

  async update(id, { titulo, id_usuario, id_ambiente_pai, observacoes }) {
    const query = `
      UPDATE ambientes
      SET titulo = $1, id_usuario = $2, id_ambiente_pai = $3, observacoes = $4
      WHERE id = $5
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [titulo, id_usuario, id_ambiente_pai, observacoes, id]);
      return rows[0];
    } catch (error) {
      throw error;
    }
  },

  async findAllByAmbientePai(id_ambiente_pai) {
    const query = 'SELECT * FROM ambientes WHERE id_ambiente_pai = $1 ORDER BY titulo;';
    try {
      const { rows } = await db.query(query, [id_ambiente_pai]);
      return rows;
    } catch (error) {
      throw error;
    }
  },

  async remove(id) {

    const query = 'DELETE FROM ambientes WHERE id = $1 RETURNING id;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      throw error;
    }
  }
};

module.exports = Ambiente;