function showToast(message, type = 'success', duration = 4000) {
    let container = document.getElementById('toast-container');
    
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container';
        document.body.appendChild(container);
    }
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icons = {
        success: '✓',
        error: '✕',
        warning: '⚠',
        info: 'ℹ'
    };
    
    const icon = icons[type] || icons.success;
    
    toast.innerHTML = `
        <span class="toast-icon">${icon}</span>
        <span class="toast-content">${message}</span>
        <button class="toast-close" onclick="removeToast(this.parentElement)">×</button>
        <div class="toast-progress"></div>
    `;
    
    container.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    const progressBar = toast.querySelector('.toast-progress');
    progressBar.style.width = '100%';
    progressBar.style.transition = `width ${duration}ms linear`;
    
    setTimeout(() => {
        progressBar.style.width = '0%';
    }, 100);
    
    setTimeout(() => {
        removeToast(toast);
    }, duration);
}

function removeToast(toast) {
    toast.classList.remove('show');
    setTimeout(() => {
        if (toast.parentElement) {
            toast.parentElement.removeChild(toast);
        }
    }, 300);
}

function showSuccessToast(message, duration = 4000) {
    showToast(message, 'success', duration);
}

function showErrorToast(message, duration = 4000) {
    showToast(message, 'error', duration);
}

function showWarningToast(message, duration = 4000) {
    showToast(message, 'warning', duration);
}

function showInfoToast(message, duration = 4000) {
    showToast(message, 'info', duration);
}
