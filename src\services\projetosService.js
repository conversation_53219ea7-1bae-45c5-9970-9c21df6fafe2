const ProjetoModel = require('../models/projetosModel');

const projetosService = {
  async criarProjeto({ codigo_projeto, nome, descricao }) {
    try {
      // Verificar se já existe um projeto com o mesmo código
      const projetoExistente = await ProjetoModel.findByCodigoProjeto(codigo_projeto);
      if (projetoExistente) {
        throw new Error(`Já existe um projeto com o código: ${codigo_projeto}`);
      }

      // Criar o projeto
      const novoProjeto = await ProjetoModel.create({
        codigo_projeto,
        nome,
        descricao
      });

      return novoProjeto;
    } catch (error) {
      console.error('[SERVICE_ERROR] criarProjeto:', error);
      throw error;
    }
  },

  async obterProjetoPorCodigo(codigo_projeto) {
    try {
      const projeto = await ProjetoModel.findByCodigoProjeto(codigo_projeto);
      if (!projeto) {
        throw new Error(`Projeto com código ${codigo_projeto} não encontrado`);
      }
      return projeto;
    } catch (error) {
      console.error('[SERVICE_ERROR] obterProjetoPorCodigo:', error);
      throw error;
    }
  },

  async obterProjetoPorId(id) {
    try {
      const projeto = await ProjetoModel.findById(id);
      if (!projeto) {
        throw new Error(`Projeto com ID ${id} não encontrado`);
      }
      return projeto;
    } catch (error) {
      console.error('[SERVICE_ERROR] obterProjetoPorId:', error);
      throw error;
    }
  },

  async listarProjetos() {
    try {
      const projetos = await ProjetoModel.findAll();
      return projetos;
    } catch (error) {
      console.error('[SERVICE_ERROR] listarProjetos:', error);
      throw error;
    }
  },

  async atualizarProjeto(id, dadosProjeto) {
    try {
      // Verificar se o projeto existe
      await this.obterProjetoPorId(id);

      // Se está mudando o código, verificar se não existe outro projeto com o mesmo código
      if (dadosProjeto.codigo_projeto) {
        const projetoExistente = await ProjetoModel.findByCodigoProjeto(dadosProjeto.codigo_projeto);
        if (projetoExistente && projetoExistente.id !== parseInt(id)) {
          throw new Error(`Já existe um projeto com o código: ${dadosProjeto.codigo_projeto}`);
        }
      }

      const projetoAtualizado = await ProjetoModel.update(id, dadosProjeto);
      return projetoAtualizado;
    } catch (error) {
      console.error('[SERVICE_ERROR] atualizarProjeto:', error);
      throw error;
    }
  },

  async deletarProjeto(id) {
    try {
      // Verificar se o projeto existe
      await this.obterProjetoPorId(id);

      const projetoDeletado = await ProjetoModel.delete(id);
      return projetoDeletado;
    } catch (error) {
      console.error('[SERVICE_ERROR] deletarProjeto:', error);
      throw error;
    }
  },

  async obterEstatisticasProjetos() {
    try {
      const totalProjetos = await ProjetoModel.count();
      return {
        totalProjetos
      };
    } catch (error) {
      console.error('[SERVICE_ERROR] obterEstatisticasProjetos:', error);
      throw error;
    }
  }
};

module.exports = projetosService;
