<link rel="stylesheet" href="/css/adm-dashboard.css" />

<div class="dashboard-container">
    <!-- Header do Dashboard -->
    <div class="dashboard-header">
        <div class="header-content">
            <h1 class="dashboard-title">
                <span class="title-icon">⚙️</span>
                Painel Administrativo
            </h1>
            <p class="dashboard-subtitle">Gerencie usuários e inspeções do sistema</p>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
                <h3 class="stat-number" id="total-users">-</h3>
                <p class="stat-label">Usuários Ativos</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">📋</div>
            <div class="stat-content">
                <h3 class="stat-number" id="total-inspections">-</h3>
                <p class="stat-label">Inspeções Criadas</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">🏢</div>
            <div class="stat-content">
                <h3 class="stat-number" id="total-environments">-</h3>
                <p class="stat-label">Ambientes</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">⚠️</div>
            <div class="stat-content">
                <h3 class="stat-number" id="total-occurrences">-</h3>
                <p class="stat-label">Ocorrências</p>
            </div>
        </div>
    </div>

    <!-- Ações Principais -->
    <div class="actions-section">
        <h2 class="section-title">Ações Principais</h2>
        
        <div class="actions-grid">
            <!-- Botão Criar Usuário -->
            <div class="action-card" onclick="navigateToCreateUser()">
                <div class="action-icon">
                    <span>👤</span>
                </div>
                <div class="action-content">
                    <h3 class="action-title">Criar Usuário</h3>
                    <p class="action-description">Adicione novos usuários ao sistema</p>
                </div>
                <div class="action-arrow">
                    <span>→</span>
                </div>
            </div>

            <!-- Botão Criar Inspeção -->
            <div class="action-card" onclick="navigateToCreateInspection()">
                <div class="action-icon">
                    <span>📝</span>
                </div>
                <div class="action-content">
                    <h3 class="action-title">Criar Inspeção</h3>
                    <p class="action-description">Configure uma nova inspeção</p>
                </div>
                <div class="action-arrow">
                    <span>→</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos Interativos -->
    <div class="charts-section">
        <h2 class="section-title">Análise de Dados</h2>
        
        <!-- Controles dos Gráficos -->
        <div class="chart-controls">
            <div class="control-group">
                <label for="chart-type">Tipo de Gráfico:</label>
                <select id="chart-type" onchange="updateChartType()">
                    <option value="bar">Barras</option>
                    <option value="line">Linha</option>
                    <option value="pie">Pizza</option>
                    <option value="doughnut">Rosca</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="chart-data">Dados:</label>
                <select id="chart-data" onchange="updateChartData()">
                    <option value="status">Status das Inspeções</option>
                    <option value="monthly">Inspeções por Mês</option>
                    <option value="users">Usuários por Tipo</option>
                    <option value="environments">Ambientes por Inspeção</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="chart-period">Período:</label>
                <select id="chart-period" onchange="updateChartPeriod()">
                    <option value="all">Todos os Tempos</option>
                    <option value="last30">Últimos 30 Dias</option>
                    <option value="last7">Últimos 7 Dias</option>
                    <option value="today">Hoje</option>
                </select>
            </div>
        </div>
        
        <!-- Container do Gráfico -->
        <div class="chart-container">
            <canvas id="interactive-chart"></canvas>
        </div>
    </div>

    <!-- Atividade Recente -->
    <div class="recent-activity">
        <h2 class="section-title">Atividade Recente</h2>
        
        <div class="activity-list" id="recent-activity-list">
            <div class="activity-item">
                <div class="activity-icon">📋</div>
                <div class="activity-content">
                    <p class="activity-text">Carregando atividades recentes...</p>
                    <span class="activity-time">-</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/adm-dashboard.js"></script>
