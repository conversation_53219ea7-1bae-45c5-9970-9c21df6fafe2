**(PULL REQUEST)**  
`feat: adicionar *resuma brevemente a funcionalidade ou o conteúdo adicionado/modificado*`

---

###  Descrição  
Explique claramente o que foi feito neste pull request: 

Exemplo:  
> Este pull request adiciona/modifica/remove *explique o que foi alterado*, incluindo:  
> - *Liste os principais pontos ou componentes envolvidos*  
> - *Adicione qualquer detalhe relevante sobre como foi implementado*

---

###  Motivação  
Justifique por que essa alteração foi necessária:


Exemplo:  
> Essa mudança visa *explicar o objetivo geral*, como melhorar a usabilidade, corrigir um erro, otimizar uma funcionalidade, ou adicionar valor ao projeto de alguma forma.

---

###  Como testar  
Liste instruções para quem vai revisar e testar sua pull request:

Exemplo:  
> - Navegue até *especifique o caminho ou funcionalidade*  
> - Verifique se *o que o revisor deve observar ou testar*  
> - Certifique-se de que *algum critério importante esteja sendo respeitado (ex: limites, regras, padrões)*

---

### 🔗 Referências (opcional)  
Inclua aqui links ou referências bibliográficas usadas como base para a implementação: 

Exemplo:  
> - [Nome do autor], *título do artigo/livro*, ano  
> - Link para documentação ou artigo técnico (se aplicável)
