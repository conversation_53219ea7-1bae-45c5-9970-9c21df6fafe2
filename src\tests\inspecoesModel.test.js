const db = require('../config/db');
jest.mock('../config/db'); 

const Inspecao = require('../models/inspecoesModel');

describe('Model: Inspecao', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar uma inspeção', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Inspecao 1' }] });
    const result = await Inspecao.create({
      nome: 'Inspecao 1',
      url_planta: 'url',
      id_tipo_edificacao: 1,
      id_equipe: 1,
      id_endereco: 1
    });
    expect(result.nome).toBe('Inspecao 1');
  });

  it('deve tratar erro ao criar inspeção', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao criar inspeção:'));
    await expect(Inspecao.create({
      nome: 'Inspecao 1',
      url_planta: 'url',
      id_tipo_edificacao: 1,
      id_equipe: 1,
      id_endereco: 1
    })).rejects.toThrow('Erro ao criar inspeção:');
  });

  it('deve buscar todas as inspeções', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Inspecao 1' }] });
    const result = await Inspecao.findAll();
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].nome).toBe('Inspecao 1');
  });

  it('deve tratar erro ao buscar inspeções', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar inspeções:'));
    await expect(Inspecao.findAll()).rejects.toThrow('Erro ao buscar inspeções:');
  });

  it('deve buscar inspeção por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Inspecao 1' }] });
    const result = await Inspecao.findById(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar undefined se inspeção não encontrada', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Inspecao.findById(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao buscar inspeção por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar inspeção por ID:'));
    await expect(Inspecao.findById(1)).rejects.toThrow('Erro ao buscar inspeção por ID:');
  });

  it('deve atualizar uma inspeção', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Nova Inspecao' }] });
    const result = await Inspecao.update(1, {
      nome: 'Nova Inspecao',
      url_planta: 'novaurl',
      id_tipo_edificacao: 2,
      id_equipe: 2,
      id_endereco: 2
    });
    expect(result.nome).toBe('Nova Inspecao');
  });

  it('deve tratar erro ao atualizar inspeção', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar inspeção:'));
    await expect(Inspecao.update(1, {
      nome: 'Nova Inspecao',
      url_planta: 'novaurl',
      id_tipo_edificacao: 2,
      id_equipe: 2,
      id_endereco: 2
    })).rejects.toThrow('Erro ao atualizar inspeção:');
  });

  it('deve remover uma inspeção', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Inspecao.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve tratar erro ao remover inspeção', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover inspeção:'));
    await expect(Inspecao.remove(1)).rejects.toThrow('Erro ao remover inspeção:');
  });
});