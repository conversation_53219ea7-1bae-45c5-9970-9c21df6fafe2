* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Inter, Arial, sans-serif;
}

:root {
  --muted-color: #E4E4E7;
  --primary-color: #00558C;
  --secondary-color: #00B5E2;
  --background-color: #FEFEFE;
  --foreground-color: #1C1C1C;
  --muted-foreground-color: #71717A;
  --border-color: #E4E4E7;
  --destructive-color: #E53935;
  --ongoing-color: #FFB246;
  --success-color: #56A757;
  --neutral-color: #4C718B;
}

/* Tema Escuro */
[data-theme="dark"] {
  --muted-color: #4B5563;
  --primary-color: #3B82F6;
  --secondary-color: #06B6D4;
  --background-color: #111827;
  --foreground-color: #FFFFFF;
  --muted-foreground-color: #D1D5DB;
  --border-color: #4B5563;
  --destructive-color: #F87171;
  --ongoing-color: #FBBF24;
  --success-color: #34D399;
  --neutral-color: #9CA3AF;
}

body {
  line-height: 1.6;
  background-color: var(--background-color);
  color: var(--foreground-color);
  height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
  margin: 0;
  padding: 0;
}

/* Garantir que o body ocupe toda a tela sem barras laterais */
html, body {
  width: 100%;
  overflow-x: hidden;
}

header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #004070 100%);
  color: var(--background-color);
  padding: 16px 20px;
  height: 64px;
  gap: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Header no modo escuro */
[data-theme="dark"] header {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.1);
  border-bottom: 1px solid #374151;
}

/* Efeito hover no header claro */
header:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 20px rgba(0, 85, 140, 0.1);
}

/* Efeito hover no header escuro */
[data-theme="dark"] header:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4), 0 0 30px rgba(59, 130, 246, 0.15);
}

header .logoIPT {
  height: 36px;
  transition: all 0.3s ease;
}

/* Logo com filtro no modo escuro para melhor visibilidade */
[data-theme="dark"] header .logoIPT {
  filter: brightness(1.2) contrast(1.1) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Texto do header no modo escuro */
[data-theme="dark"] header h1,
[data-theme="dark"] header .title {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

header .left {
  display: flex;
  align-items: center;
  gap: 16px;
}

header .right {
  display: flex;
  align-items: center;
  gap: 16px;
}

header button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: var(--background-color);
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 8px;
}

header button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

/* Botões do header no modo escuro */
[data-theme="dark"] header button {
  color: #ffffff;
}

[data-theme="dark"] header button:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

/* Botão de Toggle de Tema */
.theme-toggle-btn {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: scale(1.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-icon-global {
  font-size: 1.3rem;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.theme-toggle-btn:hover .theme-icon-global {
  transform: rotate(20deg) scale(1.1);
  animation: themeIconSpin 2s ease-in-out infinite;
}

/* Animação de rotação para ícone de tema */
@keyframes themeIconSpin {
  0%, 100% {
    transform: rotate(20deg) scale(1.1);
  }
  25% {
    transform: rotate(25deg) scale(1.15);
  }
  75% {
    transform: rotate(15deg) scale(1.05);
  }
}

/* Toggle de tema no modo escuro */
[data-theme="dark"] .theme-toggle-btn {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .theme-toggle-btn:hover {
  background: rgba(59, 130, 246, 0.3) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Ícones do header com efeitos no modo escuro */
[data-theme="dark"] header button img {
  filter: brightness(1.2) drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3));
  transition: filter 0.3s ease;
}

[data-theme="dark"] header button:hover img {
  filter: brightness(1.4) drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
}

/* Efeito de brilho sutil no header claro */
header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  pointer-events: none;
}

/* Efeito de brilho sutil no header escuro */
[data-theme="dark"] header::before {
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

/* Efeito de gradiente animado no header escuro */
[data-theme="dark"] header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

/* Efeito de gradiente animado no header claro */
header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  animation: shimmer 4s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

main {
  height: calc(100% - 64px);
  transition: background-color 0.3s ease, color 0.3s ease;
  width: 100%;
  background-color: var(--background-color);
  overflow-x: hidden;
}

/* Transições suaves para elementos que mudam com o tema */
header {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

header * {
  transition: all 0.3s ease;
}

* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Melhorias de contraste para modo escuro */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
  background-color: #374151;
  border-color: #6b7280;
  color: #ffffff;
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .card,
[data-theme="dark"] .container {
  background-color: #1f2937;
  border-color: #4b5563;
}

[data-theme="dark"] button:not(.theme-toggle-btn) {
  background-color: #374151;
  color: #ffffff;
  border-color: #6b7280;
}

[data-theme="dark"] button:not(.theme-toggle-btn):hover {
  background-color: #4b5563;
}

/* Melhorias para elementos específicos no modo escuro */
[data-theme="dark"] .sidebar-container {
  background-color: #1f2937;
  border-color: #4b5563;
}

[data-theme="dark"] .sidebar-container a {
  color: #d1d5db;
}

[data-theme="dark"] .sidebar-container a:hover {
  background-color: #374151;
  color: #ffffff;
}

[data-theme="dark"] .team-card,
[data-theme="dark"] .inspection-card,
[data-theme="dark"] .environment-card {
  background-color: #1f2937;
  border-color: #4b5563;
  color: #ffffff;
}

[data-theme="dark"] .team-card:hover,
[data-theme="dark"] .inspection-card:hover,
[data-theme="dark"] .environment-card:hover {
  background-color: #374151;
}

[data-theme="dark"] .text-muted,
[data-theme="dark"] .text-secondary {
  color: #d1d5db !important;
}

.text-muted-foreground { color: var(--muted-foreground-color); }

.text-muted { color: var(--muted-color); }

.text-foreground { color: var(--foreground-color); }

.text-background { color: var(--background-color); }

.text-primary { color: var(--primary-color); }

.text-secondary { color: var(--secondary-color); }

.text-ongoing { color: var(--ongoing-color); }

.text-success { color: var(--success-color); }

.text-neutral { color: var(--neutral-color); }

.text-destructive { color: var(--destructive-color); }

.bg-muted-foreground { background-color: var(--muted-foreground-color); }

.bg-muted { background-color: var(--muted-color); }

.bg-foreground { background-color: var(--foreground-color); }

.bg-background { background-color: var(--background-color); }

.bg-primary { background-color: var(--primary-color); }

.bg-secondary { background-color: var(--secondary-color); }

.bg-ongoing { background-color: var(--ongoing-color); }

.bg-success { background-color: var(--success-color); }

.bg-neutral { background-color: var(--neutral-color); }

.bg-destructive { background-color: var(--destructive-color); }

.h1 { font-size: 48px; font-weight: 800; }

.h2 { font-size: 40px; font-weight: 800; }

.h3 { font-size: 32px; font-weight: 800; }

.h4 { font-size: 24px; font-weight: 800; }

.h5 { font-size: 20px; font-weight: 800; }

.h6 { font-size: 16px; font-weight: 800; }

.subtitle1 { font-size: 16px; font-weight: 600; }

.subtitle2 { font-size: 14px; font-weight: 600; }

.subtitle3 { font-size: 12px; font-weight: 600; }

.body1 { font-size: 16px; }

.body2 { font-size: 14px;}

.body3 { font-size: 12px; }

.btn-primary {
  background-color: var(--primary-color);
  color: var(--background-color);
  border-radius: 10px;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--background-color);
  border-radius: 10px;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.btn-outline {
  background-color: var(--background-color);
  color: var(--primary-color);
  border-radius: 10px;
  padding: 10px 20px;
  border: 2px solid var(--primary-color);
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.btn-destructive {
  background-color: var(--background-color);
  border: 2px solid var(--destructive-color);
  color: var(--destructive-color);
  border-radius: 10px;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.tag {
  background-color: var(--background-color);
  color: var(--primary-color);
  border-radius: 24px;
  padding: 4px 16px;
  border: 2px solid var(--primary-color);
  cursor: pointer;
  font-weight: bold;
  font-size: 12px;
  height: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  text-wrap: nowrap;
}

.tag.primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color)
}

.tag.secondary {
  border: 1px solid var(--primary-color);
  color: var(--primary-color)
}

.tag.ongoing {
  border: 2px solid var(--ongoing-color);
  color: var(--ongoing-color)
}

.tag.success {
  border: 2px solid var(--success-color);
  color: var(--success-color)
}

.tag.neutral {
  border: 2px solid var(--neutral-color);
  color: var(--neutral-color)
}

.tag.destructive {
  border: 2px solid var(--destructive-color);
  color: var(--destructive-color)
}

.tag.active {
  background-color: var(--primary-color);
  color: var(--background-color);
}

.tag.primary.active {
  background-color: var(--primary-color);
  color: var(--background-color);
}

.tag.secondary.active {
  background-color: var(--secondary-color);
  color: var(--background-color);
}

.tag.ongoing.active {
  background-color: var(--ongoing-color);
  color: var(--background-color);
}

.tag.success.active {
  background-color: var(--success-color);
  color: var(--background-color);
}

.tag.neutral.active {
  background-color: var(--neutral-color);
  color: var(--background-color);
}

.tag.destructive.active {
  background-color: var(--destructive-color);
  color: var(--background-color);
}
