<link rel="stylesheet" href="/css/profile.css" />

<div class="profile-container">
  <h2 id="profile-title">Seu perfil</h2>

  <div class="profile-photo">
    <img
      src="/assets/icons/user-primary.svg"
      class="profilePhoto"
      alt="Foto do usuário"
      id="profileImage"
    />
    <button type="button" class="edit-photo" id="changePhotoBtn">
      <img src="/assets/icons/edit-muted.svg" alt="Editar foto" />
    </button>
    <input
      type="file"
      id="fileInput"
      accept="image/*"
      capture="user"
      style="display: none"
    />
  </div>

  <form class="profile-form">
    <label>
      <b>Nome de usuário</b>
      <div class="input-container">
        <input
          type="text"
          name="nome"
          placeholder="Nome Sobrenome"
          value=""
          disabled
        />
        <img
          src="/assets/icons/edit-muted.svg"
          class="edit-icon"
          alt="Editar nome"
        />
      </div>
    </label>

    <label>
      <b>Email</b>
      <div class="input-container">
        <input
          type="email"
          name="email"
          placeholder="<EMAIL>"
          value=""
          disabled
        />
        <img
          src="/assets/icons/edit-muted.svg"
          class="edit-icon input-iconMail"
          alt="Editar email"
        />
      </div>
    </label>

    <label>
      <b>Senha</b>
      <div class="input-container">
        <input
          type="password"
          name="senha"
          placeholder="••••••••"
          value=""
          disabled
        />
        <img
          src="/assets/icons/eye-slash-muted.svg"
          class="input-iconSenha"
          alt="Mostrar/ocultar senha"
        />
      </div>
    </label>

    <div class="buttons-edit-mode hidden">
      <button type="button" class="btn-outline" id="cancel">Cancelar</button>
      <button type="submit" class="btn-primary" id="save">Salvar</button>
    </div>
  </form>

  <button type="button" class="btn-outline" id="edit">Editar</button>
  <button type="button" class="btn-destructive">Sair</button>
</div>

<script src="/js/profile.js"></script>
