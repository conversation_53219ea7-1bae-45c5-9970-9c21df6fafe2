const express = require("express");
const router = express.Router();
const ocorrenciaController = require("../controllers/ocorrenciasController");

router.get("/ambiente/:id_ambiente", ocorrenciaController.listarOcorrenciasPorAmbiente);
router.post("/ambiente/:id_ambiente", ocorrenciaController.criarOcorrenciaNoAmbiente);
router.get("/:id_ocorrencia", ocorrenciaController.obterOcorrenciaPorId);
router.put("/:id_ocorrencia", ocorrenciaController.atualizarOcorrencia);
router.delete("/:id_ocorrencia", ocorrenciaController.deletarOcorrencia);

module.exports = router;