const db = require("../config/db");

const Inspecao = {
  async create({
    nome,
    url_planta,
    id_tipo_edificacao,
    id_equipe,
    id_endereco,
    id_projeto,
  }) {
    const query = `
      INSERT INTO inspecoes (nome, url_planta, id_tipo_edificacao, id_equipe, id_endereco, id_projeto, criado_em)
      VALUES ($1, $2, $3, $4, $5, $6, NOW())
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [
        nome,
        url_planta,
        id_tipo_edificacao,
        id_equipe,
        id_endereco,
        id_projeto,
      ]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao criar inspeção:", error);
      throw error;
    }
  },

  async findAll() {
    const query = `
      SELECT
        i.*,
        COALESCE(s.nome, 'Aberta') as status,
        s.cor as status_cor
      FROM
        inspecoes i
      LEFT JOIN
        (
          SELECT
            id_inspecao,
            status_id,
            ROW_NUMBER() OVER(PARTITION BY id_inspecao ORDER BY adicionado_em DESC) as rn
          FROM
            historicos
        ) h ON i.id = h.id_inspecao AND h.rn = 1
      LEFT JOIN
        status s ON h.status_id = s.nome
      ORDER BY
        i.criado_em DESC;
    `;
    try {
      const { rows } = await db.query(query);
      return rows;
    } catch (error) {
      console.error("Erro ao buscar inspeções:", error);
      throw error;
    }
  },

  async findById(id) {
    const query = "SELECT * FROM inspecoes WHERE id = $1;";
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao buscar inspeção por ID:", error);
      throw error;
    }
  },

  async update(
    id,
    { nome, url_planta, id_tipo_edificacao, id_equipe, id_endereco }
  ) {
    const query = `
      UPDATE inspecoes
      SET nome = $1, url_planta = $2, id_tipo_edificacao = $3, id_equipe = $4, id_endereco = $5
      WHERE id = $6
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [
        nome,
        url_planta,
        id_tipo_edificacao,
        id_equipe,
        id_endereco,
        id,
      ]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao atualizar inspeção:", error);
      throw error;
    }
  },

  async updateStatus(id, status) {
    const query = `
      UPDATE inspecoes
      SET status = $1
      WHERE id = $2
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [status, id]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao atualizar status da inspeção:", error);
      throw error;
    }
  },

  async remove(id) {
    const query = "DELETE FROM inspecoes WHERE id = $1 RETURNING id;";
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao remover inspeção:", error);
      throw error;
    }
  },
};

module.exports = Inspecao;