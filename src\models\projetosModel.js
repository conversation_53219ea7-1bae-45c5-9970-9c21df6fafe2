const db = require('../config/db');

const Projeto = {
  async create({ codigo_projeto, nome, descricao }) {
    const query = `
      INSERT INTO projetos (codigo_projeto, nome, descricao)
      VALUES ($1, $2, $3)
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [codigo_projeto, nome, descricao]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao criar projeto:', error);
      throw error;
    }
  },

  async findByCodigoProjeto(codigo_projeto) {
    const query = 'SELECT * FROM projetos WHERE codigo_projeto = $1;';
    try {
      const { rows } = await db.query(query, [codigo_projeto]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar projeto por código:', error);
      throw error;
    }
  },

  async findById(id) {
    const query = 'SELECT * FROM projetos WHERE id = $1;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar projeto por ID:', error);
      throw error;
    }
  },

  async findAll() {
    const query = 'SELECT * FROM projetos ORDER BY codigo_projeto;';
    try {
      const { rows } = await db.query(query);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar projetos:', error);
      throw error;
    }
  },

  async update(id, { codigo_projeto, nome, descricao }) {
    const query = `
      UPDATE projetos
      SET codigo_projeto = $1, nome = $2, descricao = $3
      WHERE id = $4
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [codigo_projeto, nome, descricao, id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao atualizar projeto:', error);
      throw error;
    }
  },

  async delete(id) {
    const query = 'DELETE FROM projetos WHERE id = $1 RETURNING *;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao deletar projeto:', error);
      throw error;
    }
  },

  async count() {
    const query = 'SELECT COUNT(*) as total FROM projetos;';
    try {
      const { rows } = await db.query(query);
      return parseInt(rows[0].total);
    } catch (error) {
      console.error('Erro ao contar projetos:', error);
      throw error;
    }
  }
};

module.exports = Projeto;
