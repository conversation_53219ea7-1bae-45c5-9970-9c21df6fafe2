document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem('authToken');
    if (!token) {
        alert("Acesso negado. Por favor, faça o login.");
        window.location.href = '/';
        return;
    }
    const apiHeaders = { 'Authorization': `Bearer ${token}` };

    const form = document.getElementById('addAmbientForm');
    const inspecaoSelect = document.getElementById('inspecaoSelect');
    const ambientePaiGroup = document.getElementById('ambientePaiGroup');
    const ambientePaiSelect = document.getElementById('ambientePaiSelect');
    const afazeresContainer = document.getElementById('afazeres-list');
    const imageInput = document.getElementById('imageUpload');
    const uploadArea = document.querySelector('.upload-area');
    const initialUploadHTML = uploadArea.innerHTML;

    imageInput.addEventListener('change', () => {
        if (imageInput.files.length > 0) {
            const firstFile = imageInput.files[0];
            const reader = new FileReader();
            reader.onload = (e) => {
                const fileCountText = imageInput.files.length > 1 ? `${imageInput.files.length} imagens selecionadas` : firstFile.name;
                uploadArea.innerHTML = `
                    <img src="${e.target.result}" alt="Pré-visualização" class="image-preview">
                    <p class="preview-info">${fileCountText}</p>
                    <button type="button" class="remove-selection-btn">Remover seleção</button>
                `;
                uploadArea.querySelector('.remove-selection-btn').addEventListener('click', (event) => {
                    event.preventDefault();
                    imageInput.value = '';
                    uploadArea.innerHTML = initialUploadHTML;
                });
            };
            reader.readAsDataURL(firstFile);
        } else {
            uploadArea.innerHTML = initialUploadHTML;
        }
    });

    async function fetchData(url) {
        try {
            const response = await fetch(url, { headers: apiHeaders });
            if (!response.ok) throw new Error(`Erro HTTP ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error(`Erro ao buscar dados de ${url}:`, error);
            return [];
        }
    }

    function populateSelect(selectElement, items, placeholderText, valueField = 'id', textField = 'nome') {
        selectElement.innerHTML = `<option value="">${placeholderText}</option>`;
        if (items && items.length > 0) {
            const urlParams = new URLSearchParams(window.location.search);
            const inspectionId = urlParams.get('inspectionId');
            const ambientId = urlParams.get('ambientId');

            items.forEach(item => {
                const option = document.createElement('option');
                option.value = item[valueField];
                option.textContent = item[textField];
                if ((inspectionId && item[valueField] == inspectionId) || 
                    (ambientId && item[valueField] == ambientId)) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
        }
    }

    function populateChecklist(container, items) {
        container.innerHTML = '';
        if (items && items.length > 0) {
            items.forEach(item => {
                const label = document.createElement('label');
                label.className = 'checklist-item';
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = 'afazeres';
                checkbox.value = item.id;
                const span = document.createElement('span');
                span.textContent = item.titulo;
                label.appendChild(checkbox);
                label.appendChild(span);
                container.appendChild(label);
            });
        } else {
            container.innerHTML = '<p class="selection-prompt">Nenhum afazer cadastrado.</p>';
        }
    }

    async function initializePage() {
        const [inspecoes, afazeres] = await Promise.all([
            fetchData('/api/inspecoes'),
            fetchData('/api/lookup/afazeres')
        ]);
        populateSelect(inspecaoSelect, inspecoes, 'Selecione uma inspeção');
        populateChecklist(afazeresContainer, afazeres);

        const urlParams = new URLSearchParams(window.location.search);
        const ambientId = urlParams.get('ambientId');
        if (ambientId) {
            const id_inspecao = inspecaoSelect.value;
            if (id_inspecao) {
                ambientePaiGroup.style.display = 'block';
                const ambientes = await fetchData(`/api/inspecoes/${id_inspecao}/ambientes`);
                populateSelect(ambientePaiSelect, ambientes, 'Nenhum (ambiente principal)', 'id', 'titulo');
                ambientePaiSelect.disabled = false;
            }
        }
    }

    inspecaoSelect.addEventListener('change', async () => {
        const id_inspecao = inspecaoSelect.value;
        ambientePaiSelect.innerHTML = '<option value="">Carregando...</option>';
        ambientePaiSelect.disabled = true;
        ambientePaiGroup.style.display = 'none';
        if (id_inspecao) {
            ambientePaiGroup.style.display = 'block';
            const ambientes = await fetchData(`/api/inspecoes/${id_inspecao}/ambientes`);
            populateSelect(ambientePaiSelect, ambientes, 'Nenhum (ambiente principal)', 'id', 'titulo');
            ambientePaiSelect.disabled = false;
        }
    });

    form.addEventListener('submit', async (event) => {
        event.preventDefault();
        const id_inspecao = inspecaoSelect.value;
        if (!id_inspecao) {
            showWarningToast('Selecione uma inspeção para continuar.');
            return;
        }

        const afazeresSelecionados = Array.from(document.querySelectorAll('input[name="afazeres"]:checked')).map(cb => cb.value);
        const ambienteData = {
            titulo: document.getElementById('title').value,
            observacoes: document.getElementById('observation').value,
            id_inspecao: parseInt(id_inspecao),
            id_ambiente_pai: ambientePaiSelect.value ? parseInt(ambientePaiSelect.value) : null,
            afazeres: afazeresSelecionados
        };

        try {
            const responseAmbiente = await fetch(`/api/inspecoes/${id_inspecao}/ambientes`, {
                method: 'POST',
                headers: { ...apiHeaders, 'Content-Type': 'application/json' },
                body: JSON.stringify(ambienteData)
            });
            const ambienteCriado = await responseAmbiente.json();
            if (!responseAmbiente.ok) throw new Error(ambienteCriado.message || "Erro ao criar ambiente.");

            if (imageInput.files.length > 0) {
                const fotosFormData = new FormData();
                for (const file of imageInput.files) fotosFormData.append('fotos', file);
                fotosFormData.append('id_ambiente', ambienteCriado.id);
                fotosFormData.append('descricao', `Fotos do ambiente ${ambienteCriado.titulo}`);
                const responseFotos = await fetch(`/api/fotos/upload/ambiente-ocorrencia`, {
                    method: 'POST',
                    headers: apiHeaders,
                    body: fotosFormData
                });
                if (!responseFotos.ok) {
                    const errorFotoData = await responseFotos.json();
                    throw new Error(errorFotoData.message || "Ambiente criado, mas falha ao enviar fotos.");
                }
            }
            showSuccessToast("Ambiente criado com sucesso! Redirecionando...", 2000);
            setTimeout(() => {
                window.location.href = `/inspection-details/${id_inspecao}`;
            }, 2000);
        } catch (error) {
            showErrorToast(`Falha no processo: ${error.message}`);
        }
    });

    // ===== FUNÇÕES DO MODAL DE AFAZERES =====

    // Modal de Afazer
    function openAfazerModal() {
        document.getElementById('afazerModal').classList.add('active');
        document.getElementById('afazerTitulo').focus();
    }

    function closeAfazerModal() {
        document.getElementById('afazerModal').classList.remove('active');
        document.getElementById('afazerForm').reset();
    }

    async function saveAfazer() {
        const titulo = document.getElementById('afazerTitulo').value.trim();

        if (!titulo) {
            showWarningToast('Por favor, insira o título do afazer.');
            return;
        }

        try {
            if (!token) {
                showErrorToast('Token de autenticação não encontrado. Faça login novamente.');
                window.location.href = '/';
                return;
            }

            console.log('Enviando requisição para criar afazer:', { titulo });

            const response = await fetch('/api/lookup/afazeres', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ titulo })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('Erro na resposta:', response.status, errorData);
                throw new Error(errorData.message || `Erro ${response.status} ao criar afazer`);
            }

            const newAfazer = await response.json();

            // Recarregar os afazeres
            const afazeresResponse = await fetch('/api/lookup/afazeres', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            const afazeres = await afazeresResponse.json();
            populateChecklist(afazeresContainer, afazeres);

            // Selecionar automaticamente o novo afazer criado
            setTimeout(() => {
                const newCheckbox = document.querySelector(`input[name="afazeres"][value="${newAfazer.id}"]`);
                if (newCheckbox) {
                    newCheckbox.checked = true;
                }
            }, 100);

            closeAfazerModal();
            showSuccessToast('Afazer criado com sucesso!');
        } catch (error) {
            showErrorToast('Erro ao criar afazer: ' + error.message);
        }
    }

    // Tornar as funções globais para serem acessíveis pelos botões HTML
    window.openAfazerModal = openAfazerModal;
    window.closeAfazerModal = closeAfazerModal;
    window.saveAfazer = saveAfazer;

    initializePage();
});

// Fechar modal ao clicar fora
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal-overlay')) {
        if (event.target.id === 'afazerModal') {
            window.closeAfazerModal();
        }
    }
});

// Fechar modal com ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        window.closeAfazerModal();
    }
});