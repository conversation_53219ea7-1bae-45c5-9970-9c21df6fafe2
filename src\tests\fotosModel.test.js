const db = require('../config/db');
jest.mock('../config/db'); 

const Foto = require('../models/fotosModel');

describe('Model: Foto', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar uma foto', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, url: 'url', id_ambiente: 1 }] });
    const result = await Foto.create({ url: 'url', id_ambiente: 1, id_ocorrencia: 1 });
    expect(result.url).toBe('url');
  });

  it('deve buscar fotos por ambiente', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, url: 'url', id_ambiente: 1 }] });
    const result = await Foto.findAllByAmbienteId(1);
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].url).toBe('url');
  });

  it('deve buscar fotos por ambiente com filtro de ocorrência', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, url: 'url', id_ambiente: 1, id_ocorrencia: 2 }] });
    const result = await Foto.findAllByAmbienteId(1, 2);
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].id_ocorrencia).toBe(2);
  });

  it('deve buscar fotos por ocorrência', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, url: 'url', id_ocorrencia: 1 }] });
    const result = await Foto.findAllByOcorrenciaId(1);
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].url).toBe('url');
  });

  it('deve buscar foto por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, url: 'url' }] });
    const result = await Foto.findById(1);
    expect(result.id).toBe(1);
  });

  it('deve atualizar uma foto', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, id_ocorrencia: 2 }] });
    const result = await Foto.update(1, { id_ocorrencia: 2 });
    expect(result.id_ocorrencia).toBe(2);
  });

  it('deve remover uma foto', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Foto.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar array vazio ao buscar fotos por ambiente inexistente', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Foto.findAllByAmbienteId(999);
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(0);
  });

  it('deve retornar array vazio ao buscar fotos por ocorrência inexistente', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Foto.findAllByOcorrenciaId(999);
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(0);
  });

  it('deve retornar undefined ao buscar foto por id inexistente', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Foto.findById(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao criar foto', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao criar foto'));
    await expect(Foto.create({ url: 'url', id_ambiente: 1, id_ocorrencia: 1 })).rejects.toThrow('Erro ao criar foto');
  });

  it('deve tratar erro ao buscar fotos por ambiente', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar fotos por ambiente'));
    await expect(Foto.findAllByAmbienteId(1)).rejects.toThrow('Erro ao buscar fotos por ambiente');
  });

  it('deve tratar erro ao buscar fotos por ocorrência', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar fotos por ocorrência'));
    await expect(Foto.findAllByOcorrenciaId(1)).rejects.toThrow('Erro ao buscar fotos por ocorrência');
  });

  it('deve tratar erro ao buscar foto por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar foto por id'));
    await expect(Foto.findById(1)).rejects.toThrow('Erro ao buscar foto por id');
  });

  it('deve tratar erro ao atualizar foto', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar foto'));
    await expect(Foto.update(1, { id_ocorrencia: 2 })).rejects.toThrow('Erro ao atualizar foto');
  });

  it('deve tratar erro ao remover foto', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover foto'));
    await expect(Foto.remove(1)).rejects.toThrow('Erro ao remover foto');
  });
});