.conteudo {
  min-height: 100vh;
  background: var(--background-color);
}

.image-banner {
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--muted-color);
}

.image-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-container {
  padding: 20px 16px;
}

.info {
  margin-bottom: 24px;
}

.info h2 {
  font-size: 24px;
  font-weight: 800;
  color: var(--foreground-color);
  margin-bottom: 8px;
}

.info p {
  color: var(--muted-foreground-color);
  font-size: 16px;
  margin-bottom: 12px;
}

.view-team {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
}

.view-team:hover {
  text-decoration: underline;
}

.filters {
  margin-bottom: 20px;
}

.filters h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--foreground-color);
}

.buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn {
  background-color: var(--background-color);
  color: var(--primary-color);
  border-radius: 24px;
  padding: 8px 16px;
  border: 2px solid var(--primary-color);
  cursor: pointer;
  font-weight: 600;
  font-size: 12px;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.btn:hover {
  background-color: var(--muted-color);
}

.btn.active {
  background-color: var(--primary-color);
  color: var(--background-color);
}

.search-filter {
    display: flex;
    gap: 12px;
    margin-top: 12px;
    margin-bottom: 24px;
}

.search-input {
  flex: 1;
  position: relative;
}

.search-input input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 40px;
  border: 2px solid var(--muted-color);
  border-radius: 10px;
  font-size: 16px;
  background: var(--background-color);
  color: var(--foreground-color);
}

.search-input input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.search-input input::placeholder {
  color: var(--muted-foreground-color);
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--muted-foreground-color);
}

.filter-btn {
  padding: 12px;
  border: 2px solid var(--muted-color);
  background: var(--background-color);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: var(--muted-color);
}

.filter-btn img {
  width: 20px;
  height: 20px;
}

.cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.card {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-image {
  width: 110%;
  height: 110px;
  object-fit: cover;
  flex-shrink: 0;
  background: var(--muted-color);
}

.card-image-placeholder {
  width: 110%;
  height: 110px;
  background: linear-gradient(135deg, var(--muted-color) 0%, var(--background-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--muted-foreground-color);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.card-image-placeholder svg {
  width: 32px;
  height: 32px;
  stroke: var(--muted-foreground-color);
  opacity: 0.6;
  transition: stroke 0.3s ease, opacity 0.3s ease;
}

/* Modo escuro - placeholder dos cards */
[data-theme="dark"] .card-image-placeholder {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  color: #e5e7eb;
}

[data-theme="dark"] .card-image-placeholder svg {
  stroke: #e5e7eb;
  opacity: 0.8;
}

.card-content {
  flex: 1;
  padding: 14px 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 110px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
  gap: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--foreground-color);
  margin: 0;
  flex: 1;
  line-height: 1.3;
  display: -webkit-box;
  overflow: hidden;
}

.card-date {
  font-size: 11px;
  color: var(--muted-foreground-color);
  background: var(--muted-color);
  padding: 3px 8px;
  border-radius: 12px;
  flex-shrink: 0;
  font-weight: 500;
}

.card-details {
  flex: 1;
  margin: 4px 0;
}

.card-details p {
  margin: 2px 0;
  font-size: 13px;
  color: var(--muted-foreground-color);
  line-height: 1.4;
}

.card-details p strong {
  color: var(--foreground-color);
  font-weight: 600;
}

.card-observations,
.card-analysis,
.card-occurrences {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  margin-bottom: 2px;
}

.card-observations {
  display: -webkit-box;
  overflow: hidden;
}

.analysis-status {
  color: var(--ongoing-color);
  font-weight: 600;
  font-size: 12px;
}

.card-creator {
  font-size: 11px;
  color: var(--muted-foreground-color);
  margin: 6px 0 0 0;
  text-align: right;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 6px;
}

.card-creator a {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 500;
}

.card-creator a:hover {
  text-decoration: underline;
}

.status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid;
}

.status.ongoing {
  color: var(--ongoing-color);
  border-color: var(--ongoing-color);
  background: var(--background-color);
}

.status.success {
  color: var(--success-color);
  border-color: var(--success-color);
  background: var(--background-color);
}

.status.neutral {
  color: var(--neutral-color);
  border-color: var(--neutral-color);
  background: var(--background-color);
}

.creator {
  margin-top: 12px !important;
  padding-top: 12px;
  border-top: 1px solid var(--muted-color);
  font-size: 13px;
  color: var(--muted-foreground-color);
}

.creator a {
  color: var(--secondary-color);
  text-decoration: none;
  font-weight: 600;
}

.creator a:hover {
  text-decoration: underline;
}

.loading {
  text-align: center;
  padding: 40px 20px;
  color: var(--muted-foreground-color);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--muted-foreground-color);
}

.empty-state h3 {
  margin-bottom: 8px;
  color: var(--muted-foreground-color);
}

.fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--secondary-color);
  color: var(--background-color);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(0, 181, 226, 0.3);
  transition: all 0.2s ease;
  z-index: 1000;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 181, 226, 0.4);
}

@media (min-width: 480px) {
    .card {
        flex-direction: row;
    }

    .card-image {
        width: 150px;
        height: 130%;
    }

    .card-image-placeholder {
        width: 150px;
        height: 130%;
    }

    .card-content {
        padding: 16px 18px;
        min-height: 130px;
    }

    .card-title {
        font-size: 17px;
    }

    .card-details p {
        font-size: 14px;
    }
}

@media (min-width: 768px) {
  .image-banner {
    height: 250px;
  }

    .image-banner img {
        width: auto;
        height: 100%;
    }

    .info-container {
        padding: 32px 24px;
    }

  .info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
  }

  .info-left h2 {
    font-size: 28px;
  }

  .cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

    .card {
        flex-direction: column;
        height: auto;
        min-height: 280px;
    }

    .card-image {
        width: 100%;
        height: 140px;
        object-fit: cover;
    }

    .card-image-placeholder {
        width: 100%;
        height: 140px;
    }

    .card-content {
        padding: 18px 20px;
        min-height: auto;
        flex: 1;
    }

    .card-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .card-title {
        font-size: 18px;
    }

    .card-date {
        font-size: 12px;
        padding: 4px 10px;
    }

    .card-details {
        margin: 8px 0 12px 0;
    }

    .card-details p {
        font-size: 14px;
        margin: 4px 0;
    }

    .card-creator {
        font-size: 12px;
        margin-top: 10px;
        padding-top: 10px;
    }

  .filters-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;
    margin-bottom: 24px;
  }

  .filters {
    margin-bottom: 0;
  }

  .search-filter {
    margin-bottom: 0;
    max-width: 400px;
  }
}

@media (min-width: 1024px) {
  .conteudo {
    max-width: 1200px;
    margin: 0 auto;
  }

  .image-banner {
    height: 300px;
  }

  .info-container {
    padding: 40px 32px;
  }

  .info h2 {
    font-size: 32px;
  }

  .cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  .card {
    min-height: 320px;
  }

    .card-image {
        height: 160px;
    }

    .card-image-placeholder {
        height: 160px;
    }

    .card-content {
        padding: 20px 22px;
    }

    .card-title {
        font-size: 19px;
    }

    .card-details p {
        font-size: 15px;
    }
}

@media (min-width: 1280px) {
    .cards {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }

    .card {
        min-height: 300px;
    }

    .card-image {
        height: 150px;
    }

    .card-image-placeholder {
        height: 150px;
    }

    .card-content {
        padding: 18px 20px;
    }

    .card-title {
        font-size: 17px;
    }

    .card-details p {
        font-size: 14px;
    }
}

@media (min-width: 1440px) {
    .conteudo {
        max-width: 1400px;
    }

    .cards {
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
    }

    .card {
        min-height: 320px;
    }

    .card-image {
        height: 160px;
    }

    .card-image-placeholder {
        height: 160px;
    }

    .card-content {
        padding: 20px 22px;
    }

    .card-title {
        font-size: 18px;
    }

    .card-details p {
        font-size: 15px;
    }
}

@media (min-width: 1920px) {
    .conteudo {
        max-width: 1600px;
    }

    .cards {
        grid-template-columns: repeat(5, 1fr);
        gap: 28px;
    }
}

/* Estilo para botões de inspeção */
.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--background-color);
  border-radius: 10px;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--secondary-color);
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}
