const relatoriosService = require('../services/relatoriosService');
const pdfService = require('../services/pdfService');

exports.listarRelatoriosPorInspecao = async (req, res, next) => {
    try {
        const { id_inspecao } = req.params;
        const relatorios = await relatoriosService.listarRelatoriosPorInspecao(id_inspecao);
        res.status(200).json(relatorios);
    } catch (error) {
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.adicionarRelatorioNaInspecao = async (req, res, next) => {
    try {
        const { id_inspecao } = req.params;
        const dadosRelatorio = req.body;
        const novoRelatorio = await relatoriosService.adicionarRelatorioNaInspecao(id_inspecao, dadosRelatorio);
        res.status(201).json(novoRelatorio);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] adicionarRelatorioNaInspecao:', error);
        const status = error.message.includes('não encontrada') ? 404 :
                      error.message.includes('obrigatória') || error.message.includes('inválida') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterRelatorioPorId = async (req, res, next) => {
    try {
        const { id_relatorio } = req.params;
        const relatorio = await relatoriosService.obterRelatorioPorId(id_relatorio);
        res.status(200).json(relatorio);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterRelatorioPorId:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.gerarPreviewRelatorio = async (req, res, next) => {
    try {
        const { id_relatorio } = req.params;
        const preview = await relatoriosService.gerarPreviewRelatorio(id_relatorio);
        res.status(200).json(preview);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] gerarPreviewRelatorio:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.downloadRelatorioPDF = async (req, res, next) => {
    try {
        const { id_relatorio } = req.params;

        console.log(`📄 Iniciando download PDF para relatório ${id_relatorio}`);

        const relatorio = await relatoriosService.obterRelatorioPorId(id_relatorio);
        console.log(`✅ Relatório encontrado: ${relatorio.nome_inspecao}`);

        const baseUrl = `${req.protocol}://${req.get('host')}`;
        const reportUrl = `${baseUrl}/report-details/${id_relatorio}?pdf=true`;

        console.log(`🔗 URL do relatório: ${reportUrl}`);
        console.log(`🚀 Iniciando geração de PDF com Puppeteer...`);

        const pdfBuffer = await pdfService.generatePDFFromURL(reportUrl, {
            format: 'A4',
            printBackground: true,
            preferCSSPageSize: false,
            margin: {
                top: '5mm',
                right: '5mm',
                bottom: '5mm',
                left: '5mm'
            },
            displayHeaderFooter: false,
            scale: 0.9
        });

        console.log(`📦 PDF gerado com sucesso, tamanho: ${pdfBuffer.length} bytes`);

        if (!pdfBuffer || pdfBuffer.length === 0) {
            throw new Error('PDF buffer está vazio');
        }

        const fs = require('fs');
        const path = require('path');
        const testPath = path.join(__dirname, '..', 'temp', `test_${id_relatorio}.pdf`);

        const tempDir = path.dirname(testPath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        fs.writeFileSync(testPath, pdfBuffer);
        console.log(`🔍 PDF salvo para teste em: ${testPath}`);

        const nomeSeguro = (relatorio.nome_inspecao || `relatorio_${id_relatorio}`).replace(/[^a-zA-Z0-9]/g, '_');
        const filename = `${nomeSeguro}_${new Date().toISOString().split('T')[0]}.pdf`;

        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Length', pdfBuffer.length);
        res.setHeader('Cache-Control', 'no-cache');

        res.send(pdfBuffer);

        console.log(`✅ PDF enviado com sucesso: ${filename}`);

    } catch (error) {
        if (error.message === 'Relatório não encontrado.') {
            return res.status(404).json({ message: error.message });
        }

        res.status(500).json({
            message: "Erro ao gerar PDF do relatório.",
            error: error.message
        });
    }
};

exports.generateSimplePDF = async (relatorio, id_relatorio) => {
    const PDFDocument = require('pdfkit');

    return new Promise(async (resolve, reject) => {
        try {
            const doc = new PDFDocument({
                size: 'A4',
                margin: 50
            });

            const buffers = [];
            doc.on('data', buffers.push.bind(buffers));
            doc.on('end', () => {
                const pdfBuffer = Buffer.concat(buffers);
                resolve(pdfBuffer);
            });

            const dadosCompletos = await relatoriosService.obterRelatorioPorId(id_relatorio);

            doc.fontSize(20)
               .fillColor('#00558C')
               .text('Relatório de Inspeção Predial', { align: 'center' });

            doc.moveDown();

            doc.fontSize(14)
               .fillColor('#333333')
               .text(`ID do Relatório: ${id_relatorio}`, 50, doc.y);

            if (dadosCompletos.nome_inspecao) {
                doc.text(`Inspeção: ${dadosCompletos.nome_inspecao}`, 50, doc.y + 20);
            }

            doc.text(`Data de Geração: ${new Date().toLocaleDateString('pt-BR')}`, 50, doc.y + 20);

            doc.moveDown(2);

            if (dadosCompletos.ambientes && dadosCompletos.ambientes.length > 0) {
                doc.fontSize(16)
                   .fillColor('#00558C')
                   .text('Ambientes Inspecionados', 50, doc.y);

                doc.moveDown();

                dadosCompletos.ambientes.forEach((ambiente, index) => {
                    doc.fontSize(12)
                       .fillColor('#333333')
                       .text(`${index + 1}. ${ambiente.nome || 'Ambiente sem nome'}`, 50, doc.y);

                    if (ambiente.descricao) {
                        doc.fontSize(10)
                           .fillColor('#666666')
                           .text(`   Descrição: ${ambiente.descricao}`, 50, doc.y + 15);
                    }

                    if (ambiente.fotos && ambiente.fotos.length > 0) {
                        doc.fontSize(10)
                           .text(`   Fotos: ${ambiente.fotos.length} encontradas`, 50, doc.y + 15);
                    }

                    doc.moveDown();
                });
            }

            if (dadosCompletos.ocorrencias && dadosCompletos.ocorrencias.length > 0) {
                doc.addPage();

                doc.fontSize(16)
                   .fillColor('#00558C')
                   .text('Ocorrências Identificadas', 50, 50);

                doc.moveDown();

                dadosCompletos.ocorrencias.forEach((ocorrencia, index) => {
                    doc.fontSize(12)
                       .fillColor('#333333')
                       .text(`${index + 1}. ${ocorrencia.descricao || 'Ocorrência sem descrição'}`, 50, doc.y);

                    if (ocorrencia.grau_urgencia) {
                        doc.fontSize(10)
                           .fillColor('#666666')
                           .text(`   Urgência: ${ocorrencia.grau_urgencia}`, 50, doc.y + 15);
                    }

                    if (ocorrencia.fotos && ocorrencia.fotos.length > 0) {
                        doc.fontSize(10)
                           .text(`   Fotos: ${ocorrencia.fotos.length} encontradas`, 50, doc.y + 15);
                    }

                    doc.moveDown();
                });
            }

            doc.fontSize(8)
               .fillColor('#999999')
               .text('Relatório gerado automaticamente pelo sistema de inspeção predial', 50, doc.page.height - 50, {
                   align: 'center'
               });

            doc.end();

        } catch (error) {
            reject(error);
        }
    });
};

exports.debugFotos = async (req, res) => {
    try {
        const { id } = req.params;
        const FotoModel = require('../models/fotosModel');
        const AmbienteModel = require('../models/ambientesModel');

        const ambientes = await AmbienteModel.findAllByInspecaoId(id);

        const resultado = {
            inspecao_id: id,
            ambientes: [],
            total_fotos: 0
        };

        for (const ambiente of ambientes) {
            const fotos = await FotoModel.findAllByAmbienteId(ambiente.id);

            resultado.ambientes.push({
                id: ambiente.id,
                titulo: ambiente.titulo,
                fotos: fotos,
                total_fotos: fotos.length
            });

            resultado.total_fotos += fotos.length;
        }

        res.json(resultado);

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.deletarRelatorio = async (req, res, next) => {
    try {
        const { id_relatorio } = req.params;
        await relatoriosService.deletarRelatorio(id_relatorio);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarRelatorio:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.listarRelatorios = async (req, res, next) => {
    try {
        const relatorios = await relatoriosService.listarRelatorios();
        res.status(200).json(relatorios);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarRelatorios:', error);
        res.status(500).json({ message: "Erro ao listar relatórios." });
    }
};