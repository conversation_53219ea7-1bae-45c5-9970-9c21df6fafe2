# Seção 4.2: Descrição do Desenvolvimento do Projeto 

Durante o ciclo de desenvolvimento do InsPecT na sua segunda versão, o foco esteve na ampliação das funcionalidades da aplicação, bem como no aprimoramento da integração entre as camadas do sistema. Avançou-se na comunicação entre frontend e backend, assegurando o consumo de dados reais do banco de dados. Além disso, a estrutura do backend foi expandida com a criação de novos arquivos de controller, models e rotas. Do ponto de vista visual, refinamentos sutis foram aplicados ao design da interface para garantir melhor usabilidade e consistência estética.

---
###  Código



Na segunda versão da aplicação web, diversas melhorias estruturais e funcionais foram implementadas com foco na integração entre frontend e backend. As informações previamente armazenadas no banco de dados passaram a ser consumidas e exibidas dinamicamente na interface do usuário. Essa comunicação foi viabilizada por meio de chamadas à API, sendo o tratamento dos dados realizado em arquivos como:

- **[add-ambient.js](../../src/views/js/add-ambient.js):** Envio e recebimento das informações para adicionar novos ambientes
- **[add-occurrence.js](../../src/views/js/add-occurrence.js):** Envio e recebimento das informações para adicionar novas ocorrências
- **[ambient-details.js](../../src/views/js/ambient-details.js):** Recebimento das informações para visualizar detalhes dos ambientes
- **[login.js](../../src/views/js/login.js):** Envio e recebimento das informações para realizar os logins
- **[occurrence-details.js](../../src/views/js/occurrence-details.js):** E Recebimento das informações para visualizar detalhes das ocorrências
- **[profile.js](../../src/views/js/profile.js):** Envio e recebimento das informações para visualizar e atualizar os perfis
- **[reports.js](../../src/views/js/reports.js):** Envio e recebimento das informações para adicionar novos relatórios
- **[verification-code.js](../../src/views/js/verification-code.js):** Código de verificação de integridades dos dados para cada usuário.

Além disso, foram adicionados novos arquivos de controller, models,rotase middlewares, com o objetivo de expandir a cobertura da aplicação sobre os módulos existentes e futuros. A criação dessas camadas seguiu os princípios de separação de responsabilidades, favorecendo a manutenção e escalabilidade da aplicação. Como exemplo, podem ser citados os arquivos:

- **[lookupController.js](../../src/controllers/js/lookupController.js):** Controller responsável pelo manuseio dos tipos de sistemas,patologias,edificações e afazeres.
- **[authMiddleware.js](../../src/middlewares/authMiddleware.js):** Middleware responsável pela autenticação de usuários
- **[afazeresModel.js](../../src/models/ambientesModel.js.js):** Model responsável pelas listagem dos afazeres
- **[tipoEdificacaoModel.js](../../src/models/tipoEdificacaoModel.js):** Model responsável pelas listagem dos tipos de Edificações
- **[tipoPatologiaModel.js](../../src/models/tipoPatologiaModel.js):** Model responsável pelas listagem dos tipos de Edificações
- **[tipoSistemaModel.js](../../src/models/tipoSistemaModel.js):** Model responsável pelas listagem dos tipos de sistemas

Ainda em termos de qualidade do código, testes automatizados foram desenvolvidos com o intuito de validar os principais fluxos da aplicação, reduzindo a ocorrência de erros em produção. Os testes foram construídos utilizando a bibliotecas Jest, e encontram-se em arquivos como:

- **[tipoSistemaModel.test.js](../../src/tests/tipoSistemaModel.test.js)**
- **[afazeresModel.test.js](../../src/tests/afazeresModel.test.js)**
- **[ambienteModel.test.js](../../src/tests/ambienteModel.test.js)**
- **[fotosModel.test.js](../../src/tests/fotosModel.test.js)**
- **[inspecoesModel.test.js](../../src/tests/inspecoesModel.test.js)**
- **[ocorrenciasModel.test.js](../../src/tests/ocorrencias.test.js)**
- **[relatoriosModel.test.js](../../src/tests/relatorios.test.js)**
- **[tipoEdificacaoModel.test.js](../../src/tests/tipoEdificacaoModel.test.js)**
- **[tipoPatologiaModel.test.js](../../src/tests/tipoPatologiaModel.test.js)**
- **[usuariosModel.test.js](../../src/tests/usuariosModel.test.js)**



###  Design

Do ponto de vista visual, optou-se por ajustes sutis com foco na melhoria da experiência do usuário e na consistência da identidade visual da aplicação. Foram refinados espaçamentos, paleta de cores e hierarquia tipográfica, além da responsividade dos elementos em diferentes resoluções de tela.Essas modificações podem ser vistas no seguinte figma,que ilustra o prótotipo de alta fidelidade atualizado,que correponde  ao design atual da aplicação web

[Clique aqui para ver o Figma](https://www.figma.com/design/W3xYJ74stos7MqISMewOTQ/Defined?node-id=310-1590&t=UNqRnD8d5eyvJFRK-1)


---

## Dificuldades Encontradas e Próximos Passos

Durante o desenvolvimento da segunda versão da aplicação InsPecT, algumas dificuldades técnicas e operacionais foram identificadas. A principal delas esteve relacionada à integração dos dados oriundos do banco de dados com o frontend da aplicação. Embora as rotas e controllers tenham sido configurados corretamente no backend, a manipulação assíncrona das requisições e o mapeamento adequado das respostas no frontend exigiram ajustes e testes adicionais. Em diversos momentos, enfrentaram-se inconsistências na exibição dos dados ou falhas na renderização dos componentes que dependiam dessas informações.

Outra dificuldade enfrentada foi a manutenção da coesão entre as estruturas de dados criadas no banco e os componentes da interface. Isso exigiu revisões nos models e validações adicionais nas rotas e controllers, além de modificações no formato dos dados manipulados no frontend para garantir a correta exibição.

Também foram observados desafios na implementação de testes automatizados, principalmente na simulação de estados reais da aplicação e na validação de componentes dependentes de dados externos. A escrita de testes eficazes para cenários assíncronos e interações com a API exigiu aprendizado contínuo e refatorações no código.

### Próximos Passos

Para a versão final, o foco será direcionado ao refinamento das funcionalidades já implementadas, buscando corrigir comportamentos inesperados e melhorar a performance da aplicação. Além disso, serão realizadas adaptações em funções ainda não finalizadas, com destaque para ajustes em formulários, validação de entradas e melhorias na experiência do usuário durante o fluxo de inspeção.

Espera-se também avançar na padronização dos componentes visuais e na documentação técnica da aplicação, de forma a consolidar a base para futuras iterações e facilitar o processo de manutenção evolutiva.
