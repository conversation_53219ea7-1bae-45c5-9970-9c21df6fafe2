async function toggleInspections(teamId, event) {
  event.preventDefault();
  const card = document.getElementById(teamId);
  const inspectionsList = card.querySelector('.inspections');
  const toggle = card.querySelector('.toggle');
  const isOpen = inspectionsList.style.display === 'block';

  if (!isOpen && inspectionsList.childElementCount === 0) {
    try {
      const token = localStorage.getItem('authToken');
      const res = await fetch(`/api/inspecoes/por-equipe/${teamId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const inspections = await res.json();
      inspectionsList.innerHTML = inspections.length
        ? inspections.map(inspection => `
            <li class="inspection-link" onclick="window.location.href='/inspection-details/${inspection.id}'" style="cursor:pointer;">
                <div>
                    <strong>${inspection.nome || inspection.place || inspection.titulo}</strong>
                    <span>${inspection.endereco || inspection.address || ''}</span>
                </div>
                <span class="status">${inspection.status || ''} <span class="dot"></span></span>
            </li>
        `).join('')
        : '<li>Nenhuma inspeção encontrada.</li>';
    } catch (e) {
      inspectionsList.innerHTML = '<li>Erro ao carregar inspeções.</li>';
    }
  }

  inspectionsList.style.display = isOpen ? 'none' : 'block';
  toggle.textContent = isOpen ? 'Ver inspeções ▼' : 'Ocultar inspeções ▲';
}