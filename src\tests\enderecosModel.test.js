const db = require('../config/db');
jest.mock('../config/db'); 

const Endereco = require('../models/enderecosModel');

describe('Model: Endereco', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar um endereço', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, cep: '12345-000', numero: '10' }] });
    const result = await Endereco.create({ cep: '12345-000', numero: '10', complemento: '', referencia: '' });
    expect(result.cep).toBe('12345-000');
  });

  it('deve tratar erro ao criar endereço', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao criar endereço:'));
    await expect(Endereco.create({ cep: '12345-000', numero: '10', complemento: '', referencia: '' }))
      .rejects.toThrow('Erro ao criar endereço:');
  });

  it('deve buscar todos os endereços', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, cep: '12345-000' }] });
    const result = await Endereco.findAll();
    expect(Array.isArray(result)).toBe(true);
  });

  it('deve tratar erro ao buscar endereços', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar endereços:'));
    await expect(Endereco.findAll()).rejects.toThrow('Erro ao buscar endereços:');
  });

  it('deve buscar endereço por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, cep: '12345-000' }] });
    const result = await Endereco.findById(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar undefined se endereço não encontrado', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Endereco.findById(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao buscar endereço por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar endereço por ID:'));
    await expect(Endereco.findById(1)).rejects.toThrow('Erro ao buscar endereço por ID:');
  });

  it('deve atualizar um endereço', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, cep: '54321-000' }] });
    const result = await Endereco.update(1, { cep: '54321-000', numero: '20', complemento: 'A', referencia: 'B' });
    expect(result.cep).toBe('54321-000');
  });

  it('deve tratar erro ao atualizar endereço', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar endereço:'));
    await expect(Endereco.update(1, { cep: '54321-000', numero: '20', complemento: 'A', referencia: 'B' }))
      .rejects.toThrow('Erro ao atualizar endereço:');
  });

  it('deve remover um endereço', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Endereco.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve tratar erro ao remover endereço', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover endereço:'));
    await expect(Endereco.remove(1)).rejects.toThrow('Erro ao remover endereço:');
  });
});