const ocorrenciasService = require('../services/ocorrenciasService');

exports.listarOcorrenciasPorAmbiente = async (req, res, next) => {
    try {
        const { id_ambiente } = req.params;
        const ocorrencias = await ocorrenciasService.listarOcorrenciasPorAmbiente(id_ambiente);
        res.status(200).json(ocorrencias);
    } catch (error) {
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.criarOcorrenciaNoAmbiente = async (req, res, next) => {
    try {
        const { id_ambiente } = req.params;
        const novaOcorrencia = await ocorrenciasService.criarOcorrenciaNoAmbiente(id_ambiente, req.body);
        res.status(201).json(novaOcorrencia);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarOcorrenciaNoAmbiente:', error);
        const status = error.message.includes('não encontrado') || error.message.includes('não encontrada') ? 404 :
                      error.message.includes('obrigatórios') || error.message.includes('inválido') || error.message.includes('não pertence') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterOcorrenciaPorId = async (req, res, next) => {
    try {
        const { id_ocorrencia } = req.params;
        const ocorrencia = await ocorrenciasService.obterOcorrenciaPorId(id_ocorrencia);
        res.status(200).json(ocorrencia);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterOcorrenciaPorId:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.atualizarOcorrencia = async (req, res, next) => {
    try {
        const { id_ocorrencia } = req.params;
        const ocorrenciaAtualizada = await ocorrenciasService.atualizarOcorrencia(id_ocorrencia, req.body);
        res.status(200).json(ocorrenciaAtualizada);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] atualizarOcorrencia:', error);
        const status = error.message.includes('não encontrada') || error.message.includes('não encontrado') ? 404 :
                      error.message.includes('obrigatório') || error.message.includes('inválido') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.deletarOcorrencia = async (req, res, next) => {
    try {
        const { id_ocorrencia } = req.params;
        await ocorrenciasService.deletarOcorrencia(id_ocorrencia);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarOcorrencia:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};