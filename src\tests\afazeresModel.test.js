const db = require('../config/db');
jest.mock('../config/db'); 

const Afazer = require('../models/afazeresModel');

describe('Model: Afazer', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve buscar todos os afazeres', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Limpar' }] });
    const result = await Afazer.findAll();
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].titulo).toBe('Limpar');
  });

  it('deve tratar erro ao buscar todos os afazeres', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar todos os afazeres:'));
    await expect(Afazer.findAll()).rejects.toThrow('Erro ao buscar todos os afazeres:');
  });

  it('deve criar um afazer', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Novo Afazer' }] });
    const result = await Afazer.create({ titulo: 'Novo Afazer' });
    expect(result.titulo).toBe('Novo Afazer');
  });

  it('deve tratar erro ao criar afazer', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao criar afazer:'));
    await expect(Afazer.create({ titulo: 'Novo Afazer' })).rejects.toThrow('Erro ao criar afazer:');
  });
});