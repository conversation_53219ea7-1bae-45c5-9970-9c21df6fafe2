const db = require('../config/db');
jest.mock('../config/db'); 

const TipoEdificacao = require('../models/tipoEdificacaoModel');
const TipoPatologia = require('../models/tipoPatologiaModel');
const TipoSistema = require('../models/tipoSistemaModel');

describe('Model: TipoEdificacao', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve buscar todos os tipos de edificação', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ nome: 'Residencial' }] });
    const result = await TipoEdificacao.findAll();
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].nome).toBe('Residencial');
  });

  it('deve tratar erro ao buscar tipos de edificação', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro DB'));
    await expect(TipoEdificacao.findAll()).rejects.toThrow('Erro DB');
  });
});

describe('Model: TipoPatologia', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve buscar todos os tipos de patologia', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ nome: 'Fissura', id_tipo_sistema: 1 }] });
    const result = await TipoPatologia.findAll();
    expect(result[0].nome).toBe('Fissura');
  });

  it('deve tratar erro ao buscar tipos de patologia', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro DB'));
    await expect(TipoPatologia.findAll()).rejects.toThrow('Erro DB');
  });
});

describe('Model: TipoSistema', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve buscar todos os tipos de sistema', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ nome: 'Estrutural' }] });
    const result = await TipoSistema.findAll();
    expect(result[0].nome).toBe('Estrutural');
  });

  it('deve tratar erro ao buscar tipos de sistema', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro DB'));
    await expect(TipoSistema.findAll()).rejects.toThrow('Erro DB');
  });
});