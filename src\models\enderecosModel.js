const db = require('../config/db');

const Endereco = {
  async create({ cep, numero, complemento, referencia }) {
    const query = `
      INSERT INTO enderecos (cep, numero, complemento, referencia)
      VALUES ($1, $2, $3, $4)
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [cep, numero, complemento, referencia]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao criar endereço:', error);
      throw error;
    }
  },

  async findAll() {
    const query = 'SELECT * FROM enderecos;';
    try {
      const { rows } = await db.query(query);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar endereços:', error);
      throw error;
    }
  },

  async findById(id) {
    const query = 'SELECT * FROM enderecos WHERE id = $1;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar endereço por ID:', error);
      throw error;
    }
  },

  async update(id, { cep, numero, complemento, referencia }) {
    const query = `
      UPDATE enderecos
      SET cep = $1, numero = $2, complemento = $3, referencia = $4
      WHERE id = $5
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [cep, numero, complemento, referencia, id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao atualizar endereço:', error);
      throw error;
    }
  },

  async remove(id) {
    const query = 'DELETE FROM enderecos WHERE id = $1 RETURNING id;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao remover endereço:', error);
      throw error;
    }
  },

  async findByCep(cep) {
    const query = 'SELECT * FROM enderecos WHERE cep = $1;';
    try {
      const { rows } = await db.query(query, [cep]);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar endereços por CEP:', error);
      throw error;
    }
  },

  async findAllPaginated(limite, offset) {
    const query = 'SELECT * FROM enderecos ORDER BY id LIMIT $1 OFFSET $2;';
    try {
      const { rows } = await db.query(query, [limite, offset]);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar endereços paginados:', error);
      throw error;
    }
  },

  async count() {
    const query = 'SELECT COUNT(*) as total FROM enderecos;';
    try {
      const { rows } = await db.query(query);
      return parseInt(rows[0].total);
    } catch (error) {
      console.error('Erro ao contar endereços:', error);
      throw error;
    }
  }
};

module.exports = Endereco;