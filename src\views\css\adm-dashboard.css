* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
}

/* Variáveis CSS para temas */
:root {
    --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --bg-card: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    --bg-header: linear-gradient(135deg, #00558C 0%, #004070 100%);
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-white: #ffffff;
    --border-color: rgba(255, 255, 255, 0.8);
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 25px -5px rgba(0, 0, 0, 0.15);
    --accent-blue: #00558C;
    --accent-light: #3b82f6;
}

/* Tema escuro */
[data-theme="dark"] {
    --bg-primary: linear-gradient(135deg, #111827 0%, #1f2937 100%);
    --bg-card: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    --bg-header: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-white: #ffffff;
    --border-color: rgba(75, 85, 99, 0.6);
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-medium: 0 10px 25px -5px rgba(0, 0, 0, 0.5);
    --accent-blue: #3b82f6;
    --accent-light: #60a5fa;
}

body {
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    transition: all 0.3s ease;
}

.dashboard-container {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Header do Dashboard */
.dashboard-header {
    background: var(--bg-header);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-medium);
    transition: all 0.3s ease;
}

.header-content {
    text-align: center;
    color: var(--text-white);
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.title-icon {
    font-size: 2.5rem;
}

.dashboard-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    font-weight: 400;
}

/* Grid de Estatísticas */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    animation: statGlow 2s ease-in-out infinite alternate;
}

/* Animação de brilho para cards de estatística */
@keyframes statGlow {
    from {
        box-shadow: var(--shadow-medium);
    }
    to {
        box-shadow: var(--shadow-medium), 0 0 20px rgba(0, 85, 140, 0.2);
    }
}

.stat-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--accent-light) 0%, var(--accent-blue) 100%);
    width: 64px;
    height: 64px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
    animation: iconPulse 3s ease-in-out infinite;
}

/* Animação de pulsação para ícones */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
    }
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Seções */
.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 24px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-light));
    border-radius: 2px;
}

/* Ações Principais */
.actions-section {
    margin-bottom: 40px;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

.action-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-blue) 0%, var(--accent-light) 100%);
}

/* Animação shimmer nos action cards */
.action-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 85, 140, 0.08), transparent);
    animation: actionShimmer 4s infinite;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-card:hover::after {
    opacity: 1;
}

@keyframes actionShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.action-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-medium);
}

.action-icon {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-light) 100%);
    width: 64px;
    height: 64px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    box-shadow: 0 4px 6px rgba(0, 85, 140, 0.3);
}

.action-content {
    flex: 1;
}

.action-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.action-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

.action-arrow {
    font-size: 1.5rem;
    color: var(--accent-blue);
    font-weight: 700;
    transition: transform 0.3s ease;
}

.action-card:hover .action-arrow {
    transform: translateX(4px);
}

/* Seção de Gráficos */
.charts-section {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    margin-bottom: 40px;
}

.chart-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

[data-theme="dark"] .chart-controls {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.6);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.control-group select {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 0.875rem;
    color: #374151;
    transition: all 0.2s ease;
    cursor: pointer;
}

[data-theme="dark"] .control-group select {
    background: #374151;
    border-color: #6b7280;
    color: #ffffff;
}

.control-group select:hover {
    border-color: var(--accent-blue);
}

.control-group select:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
}

.chart-container {
    position: relative;
    height: 400px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

[data-theme="dark"] .chart-container {
    background: #1f2937;
    border-color: #4b5563;
}

.chart-container canvas {
    max-height: 100%;
}

/* Atividade Recente */
.recent-activity {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

[data-theme="dark"] .activity-item {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.6);
}

.activity-item:hover {
    background: rgba(59, 130, 246, 0.1);
}

.activity-icon {
    font-size: 1.5rem;
    background: linear-gradient(135deg, var(--accent-light) 0%, var(--accent-blue) 100%);
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.activity-content {
    flex: 1;
}

.activity-text {
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-bottom: 4px;
}

.activity-time {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

/* Responsividade */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 16px;
    }

    .dashboard-title {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .actions-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .action-card {
        padding: 20px;
    }

    .chart-controls {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .chart-container {
        height: 300px;
        padding: 16px;
    }
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.action-card,
.charts-section,
.recent-activity {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(2) { animation-delay: 0.1s; }
.stat-card:nth-child(3) { animation-delay: 0.2s; }
.stat-card:nth-child(4) { animation-delay: 0.3s; }
.action-card:nth-child(2) { animation-delay: 0.1s; }
