const enderecosService = require('../services/enderecosService');

exports.listarEnderecos = async (req, res, next) => {
    try {
        const enderecos = await enderecosService.listarEnderecos();
        res.status(200).json(enderecos);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarEnderecos:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.criarEndereco = async (req, res, next) => {
    try {
        const novoEndereco = await enderecosService.criarEndereco(req.body);
        res.status(201).json(novoEndereco);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarEndereco:', error);
        const status = error.message.includes('obrigatórios') || error.message.includes('inválido') || error.message.includes('deve') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterEnderecoPorId = async (req, res, next) => {
    try {
        const { id_endereco } = req.params;
        const endereco = await enderecosService.obterEnderecoPorId(id_endereco);
        res.status(200).json(endereco);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterEnderecoPorId:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.atualizarEndereco = async (req, res, next) => {
    try {
        const { id_endereco } = req.params;
        const enderecoAtualizado = await enderecosService.atualizarEndereco(id_endereco, req.body);
        res.status(200).json(enderecoAtualizado);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] atualizarEndereco:', error);
        const status = error.message.includes('não encontrado') ? 404 :
                      error.message.includes('deve') || error.message.includes('inválido') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.deletarEndereco = async (req, res, next) => {
    try {
        const { id_endereco } = req.params;
        await enderecosService.deletarEndereco(id_endereco);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarEndereco:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};