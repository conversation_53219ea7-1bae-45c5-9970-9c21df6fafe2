const db = require('../config/db');

const TipoSistema = {
  async findAll() {
    const queryText = 'SELECT nome as id, nome FROM tipo_sistema ORDER BY nome;';
    try {
      const { rows } = await db.query(queryText);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar tipos de sistema:', error);
      throw error;
    }
  },

  async findByNome(nome) {
    const queryText = 'SELECT nome FROM tipo_sistema WHERE nome = $1;';
    try {
      const { rows } = await db.query(queryText, [nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar tipo de sistema por nome:', error);
      throw error;
    }
  },

  async findById(nome) {
    const queryText = 'SELECT * FROM tipo_sistema WHERE nome = $1;';
    try {
      const { rows } = await db.query(queryText, [nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar tipo de sistema por nome:', error);
      throw error;
    }
  },

  async create(dados) {
    const queryText = 'INSERT INTO tipo_sistema (nome) VALUES ($1) RETURNING nome as id, nome;';
    try {
      const { rows } = await db.query(queryText, [dados.nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao criar tipo de sistema:', error);
      throw error;
    }
  }
};

module.exports = TipoSistema;