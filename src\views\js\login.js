

document.getElementById("loginForm").addEventListener("submit", function (event) {
    event.preventDefault();

    const email = document.getElementById("email").value;
    const password = document.getElementById("senha").value;
    const errorElement = document.getElementById('emailError');
    errorElement.textContent = '';

    handleLogin(email, password, errorElement);
});

async function handleLogin(email, password, errorElement) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, senha: password })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            showSuccessToast('Login realizado com sucesso! Redirecionando...', 2000);

            localStorage.setItem("authToken", data.token);
            localStorage.setItem("usuarioId", data.usuarioId);

            setTimeout(() => {
                if (data.permissao === 'admin') {
                    window.location.href = '/adm/add-inspection';
                } else {
                    window.location.href = '/home';
                }
            }, 2000);
        } else {
            showErrorToast(data.message || 'Credenciais inválidas. Verifique seus dados e tente novamente.');
            errorElement.textContent = data.message || "Credenciais inválidas.";
        }
    } catch (error) {
        showErrorToast('Erro de conexão. Verifique sua internet e tente novamente.');
        errorElement.textContent = 'Ocorreu um erro ao tentar fazer login. Tente novamente.';
    }
}