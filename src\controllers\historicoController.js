const historicoService = require('../services/historicoService');

exports.listarHistoricoPorInspecao = async (req, res, next) => {
  try {
    const { id_inspecao } = req.params;
    const historico = await historicoService.listarHistoricoPorInspecao(id_inspecao);
    res.status(200).json(historico);
  } catch (error) {
    console.error('[CONTROLLER_ERROR] listarHistoricoPorInspecao:', error);
    const status = error.message.includes('não encontrado') || error.message.includes('não encontrada') ? 404 : 500;
    res.status(status).json({ message: error.message });
  }
};