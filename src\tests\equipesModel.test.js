const db = require('../config/db');
jest.mock('../config/db'); 

const Equipe = require('../models/equipesModel');

describe('Model: Equipe', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar uma equipe', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Equipe A' }] });
    const result = await Equipe.create({ nome: 'Equipe A' });
    expect(result.nome).toBe('Equipe A');
  });

  it('deve retornar erro ao criar equipe', async () => {
    db.query.mockRejectedValueOnce(new Error('Falha ao criar equipe'));
    await expect(Equipe.create({ nome: 'Equipe B' })).rejects.toThrow('Falha ao criar equipe');
  });

  it('deve buscar todas as equipes', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Equipe A' }] });
    const result = await Equipe.findAll();
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].nome).toBe('Equipe A');
  });

  it('deve tratar erro ao buscar equipes', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar equipes'));
    await expect(Equipe.findAll()).rejects.toThrow('Erro ao buscar equipes');
  });

  it('deve buscar equipe por id (com usuários)', async () => {
    db.query
      .mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Equipe A' }] }) 
      .mockResolvedValueOnce({ rows: [{ id_usuario: 2, nome_usuario: 'João', cargo_nome: 1 }] }); 
    const result = await Equipe.findById(1);
    expect(result.id).toBe(1);
    expect(Array.isArray(result.usuarios)).toBe(true);
  });

  it('deve retornar null se equipe não encontrada por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Equipe.findById(999);
    expect(result).toBeNull();
  });

  it('deve tratar erro ao buscar equipe por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar equipe por ID com usuários:'));
    await expect(Equipe.findById(1)).rejects.toThrow('Erro ao buscar equipe por ID com usuários:');
  });

  it('deve atualizar uma equipe', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Equipe Atualizada' }] });
    const result = await Equipe.update(1, { nome: 'Equipe Atualizada' });
    expect(result.nome).toBe('Equipe Atualizada');
  });

  it('deve tratar erro ao atualizar equipe', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar equipe:'));
    await expect(Equipe.update(1, { nome: 'Equipe Atualizada' })).rejects.toThrow('Erro ao atualizar equipe:');
  });

  it('deve remover uma equipe', async () => {
    db.query
      .mockResolvedValueOnce({}) 
      .mockResolvedValueOnce({ rows: [{ id: 1 }] }); 
    const result = await Equipe.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve tratar erro ao remover equipe', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover equipe:'));
    await expect(Equipe.remove(1)).rejects.toThrow('Erro ao remover equipe:');
  });

  it('deve adicionar usuário à equipe', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, id_equipe: 1, id_usuario: 2, id_cargo: 3 }] });
    const result = await Equipe.addUserToEquipe(1, 2, 3);
    expect(result.id).toBe(1);
  });

  it('deve tratar erro ao adicionar usuário à equipe', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao adicionar usuário à equipe:'));
    await expect(Equipe.addUserToEquipe(1, 2, 3)).rejects.toThrow('Erro ao adicionar usuário à equipe:');
  });

  it('deve remover usuário da equipe', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Equipe.removeUserFromEquipe(1, 2);
    expect(result.removido).toBe(true);
  });

  it('deve retornar null ao remover usuário inexistente da equipe', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Equipe.removeUserFromEquipe(1, 2);
    expect(result).toBeNull();
  });

  it('deve tratar erro ao remover usuário da equipe', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover usuário da equipe:'));
    await expect(Equipe.removeUserFromEquipe(1, 2)).rejects.toThrow('Erro ao remover usuário da equipe:');
  });

  it('deve buscar usuários da equipe', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id_usuario: 2, nome_usuario: 'João', cargo_nome: 1 }] });
    const result = await Equipe.findUsersByEquipeId(1);
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].nome_usuario).toBe('João');
  });

  it('deve tratar erro ao buscar usuários da equipe', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar usuários da equipe:'));
    await expect(Equipe.findUsersByEquipeId(1)).rejects.toThrow('Erro ao buscar usuários da equipe:');
  });
});