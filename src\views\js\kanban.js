let allInspections = [];
let currentInspectionId = null;

function navigateTo(url) {
  window.location.href = url;
}

// Inicialização da página
document.addEventListener('DOMContentLoaded', function() {
  loadKanbanData();
  initializeEventListeners();
});

// Event Listeners
function initializeEventListeners() {
  // Modal de status
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal-overlay')) {
      closeStatusModal();
    }
  });

  // Botões de status no modal
  document.querySelectorAll('.status-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const newStatus = this.dataset.status;
      updateInspectionStatus(currentInspectionId, newStatus);
    });
  });
}

// Carregar dados do kanban
async function loadKanbanData() {
  try {
    const token = localStorage.getItem('authToken');
    if (!token) {
      window.location.href = '/';
      return;
    }

    showLoadingState();

    const response = await fetch('/api/inspecoes', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 401) {
      window.location.href = '/';
      return;
    }

    if (!response.ok) {
      throw new Error('Erro ao carregar inspeções');
    }

    const data = await response.json();
    allInspections = Array.isArray(data) ? data : [];

    // Carregar endereços para cada inspeção
    for (let inspection of allInspections) {
      if (inspection.id_endereco) {
        inspection.address = await getAddressFromCep(inspection.id_endereco);
      }
    }

    renderKanbanBoard();
    updateStatistics();
  } catch (error) {
    console.error('Erro ao carregar dados do kanban:', error);
    showErrorState('Erro ao carregar dados do kanban');
  }
}

// Mostrar estado de carregamento
function showLoadingState() {
  const columns = ['aberta', 'em-andamento', 'concluida'];
  columns.forEach(status => {
    const column = document.getElementById(`column-${status}`);
    if (column) {
      column.innerHTML = '<div class="loading-state">Carregando...</div>';
    }
  });
}

// Mostrar estado de erro
function showErrorState(message) {
  const columns = ['aberta', 'em-andamento', 'concluida'];
  columns.forEach(status => {
    const column = document.getElementById(`column-${status}`);
    if (column) {
      column.innerHTML = `
        <div class="empty-state">
          <div class="icon">⚠️</div>
          <p>${message}</p>
        </div>`;
    }
  });
}

// Renderizar board do kanban
function renderKanbanBoard() {
  const statusMap = {
    'Aberta': 'aberta',
    'Em Andamento': 'em-andamento',
    'Concluída': 'concluida'
  };

  // Agrupar inspeções por status
  const groupedInspections = {
    'aberta': [],
    'em-andamento': [],
    'concluida': []
  };

  allInspections.forEach(inspection => {
    const normalizedStatus = normalizeStatus(inspection.status);
    const columnKey = statusMap[inspection.status] || statusMap[normalizedStatus];
    
    if (columnKey && groupedInspections[columnKey]) {
      groupedInspections[columnKey].push(inspection);
    }
  });

  // Renderizar cada coluna
  Object.keys(groupedInspections).forEach(columnKey => {
    renderColumn(columnKey, groupedInspections[columnKey]);
  });

  // Atualizar contadores
  updateColumnCounts(groupedInspections);
}

// Renderizar coluna específica
function renderColumn(columnKey, inspections) {
  const column = document.getElementById(`column-${columnKey}`);
  if (!column) return;

  if (inspections.length === 0) {
    column.innerHTML = `
      <div class="empty-state">
        <div class="icon">📋</div>
        <p>Nenhuma inspeção neste status</p>
      </div>`;
    return;
  }

  const cardsHtml = inspections.map(inspection => createKanbanCard(inspection)).join('');
  column.innerHTML = cardsHtml;
}

// Criar card do kanban
function createKanbanCard(inspection) {
  const date = inspection.criado_em ?
    new Date(inspection.criado_em).toLocaleDateString('pt-BR') :
    new Date().toLocaleDateString('pt-BR');

  const address = inspection.address || 'Endereço não disponível';

  return `
    <div class="kanban-card"
         data-inspection-id="${inspection.id}"
         data-current-status="${inspection.status}"
         draggable="true"
         ondragstart="handleDragStart(event)"
         ondragend="handleDragEnd(event)">
      <div class="card-title">${inspection.nome || 'Inspeção sem nome'}</div>
      <div class="card-id">#${inspection.id}</div>
      <div class="card-info">
        <div class="card-address">${address}</div>
        <div class="card-date">${date}</div>
      </div>
      <div class="card-actions">
        <button class="card-action-btn" onclick="viewInspectionDetails(${inspection.id})">
          Ver Detalhes
        </button>
        <button class="card-action-btn" onclick="openStatusModal(${inspection.id}, '${inspection.nome}')">
          Alterar Status
        </button>
      </div>
    </div>`;
}

// Atualizar contadores das colunas
function updateColumnCounts(groupedInspections) {
  Object.keys(groupedInspections).forEach(columnKey => {
    const count = groupedInspections[columnKey].length;
    const countElement = document.getElementById(`count-${columnKey}`);
    if (countElement) {
      countElement.textContent = count;
    }
  });
}

// Atualizar contadores baseado nos dados atuais
function updateColumnCountsFromData() {
  const statusMap = {
    'Aberta': 'aberta',
    'Em Andamento': 'em-andamento',
    'Concluída': 'concluida'
  };

  const counts = {
    'aberta': 0,
    'em-andamento': 0,
    'concluida': 0
  };

  allInspections.forEach(inspection => {
    const normalizedStatus = normalizeStatus(inspection.status);
    const columnKey = statusMap[inspection.status] || statusMap[normalizedStatus];
    if (columnKey && counts.hasOwnProperty(columnKey)) {
      counts[columnKey]++;
    }
  });

  document.getElementById('count-aberta').textContent = counts['aberta'];
  document.getElementById('count-em-andamento').textContent = counts['em-andamento'];
  document.getElementById('count-concluida').textContent = counts['concluida'];
}

// Atualizar estatísticas
function updateStatistics() {
  const total = allInspections.length;
  const open = allInspections.filter(i => normalizeStatus(i.status) === 'aberta').length;
  const progress = allInspections.filter(i => normalizeStatus(i.status) === 'em-andamento').length;
  const completed = allInspections.filter(i => normalizeStatus(i.status) === 'concluida').length;

  document.getElementById('total-inspections').textContent = total;
  document.getElementById('open-inspections').textContent = open;
  document.getElementById('progress-inspections').textContent = progress;
  document.getElementById('completed-inspections').textContent = completed;
}

// Normalizar status
function normalizeStatus(status) {
  if (!status) return 'aberta';
  const statusMap = {
    'Aberta': 'aberta',
    'Em Andamento': 'em-andamento',
    'Concluída': 'concluida'
  };
  return statusMap[status] || 'aberta';
}

// Abrir modal de status
function openStatusModal(inspectionId, inspectionName) {
  currentInspectionId = inspectionId;
  document.getElementById('modal-inspection-name').textContent = inspectionName;
  document.getElementById('status-modal').style.display = 'flex';
}

// Fechar modal de status
function closeStatusModal() {
  currentInspectionId = null;
  document.getElementById('status-modal').style.display = 'none';
}

// Atualizar status da inspeção
async function updateInspectionStatus(inspectionId, newStatus) {
  try {
    const token = localStorage.getItem('authToken');
    if (!token) {
      window.location.href = '/';
      return;
    }

    const response = await fetch(`/api/inspecoes/${inspectionId}/status`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status_nome: newStatus })
    });

    if (!response.ok) {
      throw new Error('Erro ao atualizar status');
    }

    // Atualizar dados locais
    const inspectionIndex = allInspections.findIndex(i => i.id === inspectionId);
    if (inspectionIndex !== -1) {
      allInspections[inspectionIndex].status = newStatus;
    }

    // Re-renderizar board
    renderKanbanBoard();
    updateStatistics();
    
    closeStatusModal();
    showSuccessMessage('Status atualizado com sucesso!');
  } catch (error) {
    console.error('Erro ao atualizar status:', error);
    showErrorMessage('Erro ao atualizar status da inspeção');
  }
}

// Ver detalhes da inspeção
function viewInspectionDetails(inspectionId) {
  navigateTo(`/inspection-details/${inspectionId}`);
}

// Atualizar kanban
function refreshKanban() {
  loadKanbanData();
}

// Buscar endereço por CEP
async function getAddressFromCep(cepId) {
  try {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`/api/enderecos/${cepId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      const endereco = await response.json();
      if (endereco && endereco.cep) {
        const cepResponse = await fetch(`https://viacep.com.br/ws/${endereco.cep}/json/`);
        if (cepResponse.ok) {
          const cepData = await cepResponse.json();
          return `${cepData.logradouro}, ${endereco.numero} - ${cepData.bairro}, ${cepData.localidade}`;
        }
      }
    }
    return 'Endereço não disponível';
  } catch (error) {
    return 'Endereço não disponível';
  }
}

// Mensagens de feedback
function showSuccessMessage(message) {
  // Implementar toast de sucesso
  console.log('Sucesso:', message);
}

function showErrorMessage(message) {
  // Implementar toast de erro
  console.error('Erro:', message);
}

// Variáveis para drag and drop
let draggedElement = null;
let draggedInspectionId = null;

// Funções de drag and drop
function handleDragStart(event) {
  draggedElement = event.target;
  draggedInspectionId = event.target.getAttribute('data-inspection-id');
  event.target.classList.add('dragging');

  // Configurar dados de transferência
  event.dataTransfer.effectAllowed = 'move';
  event.dataTransfer.setData('text/html', event.target.outerHTML);
}

function handleDragEnd(event) {
  event.target.classList.remove('dragging');
  draggedElement = null;
  draggedInspectionId = null;

  // Remover classes de drag-over de todas as colunas
  document.querySelectorAll('.column-content').forEach(column => {
    column.classList.remove('drag-over');
  });
}

function handleDragOver(event) {
  event.preventDefault();
  event.dataTransfer.dropEffect = 'move';
}

function handleDragEnter(event) {
  if (event.target.classList.contains('column-content')) {
    event.target.classList.add('drag-over');
  }
}

function handleDragLeave(event) {
  if (event.target.classList.contains('column-content')) {
    event.target.classList.remove('drag-over');
  }
}

function handleDrop(event) {
  event.preventDefault();

  const targetColumn = event.target.closest('.column-content');
  if (!targetColumn || !draggedInspectionId) return;

  const newStatus = targetColumn.closest('.kanban-column').getAttribute('data-status');
  const currentStatus = draggedElement.getAttribute('data-current-status');

  if (newStatus !== currentStatus) {
    // Atualizar status via API
    updateInspectionStatusDragDrop(draggedInspectionId, newStatus);
  }

  targetColumn.classList.remove('drag-over');
}

async function updateInspectionStatusDragDrop(inspectionId, newStatus) {
  try {
    const token = localStorage.getItem('authToken');
    if (!token) {
      showErrorMessage('Token de autenticação não encontrado');
      return;
    }

    const response = await fetch(`/api/inspecoes/${inspectionId}/status`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status_nome: newStatus })
    });

    if (!response.ok) {
      throw new Error('Erro ao atualizar status');
    }

    // Atualizar apenas o card movido sem recarregar tudo
    updateCardStatusLocally(inspectionId, newStatus);

    showSuccessMessage(`Status atualizado para "${newStatus}" com sucesso!`);
  } catch (error) {
    showErrorMessage('Erro ao atualizar status: ' + error.message);
    // Em caso de erro, recarregar para garantir consistência
    await loadKanbanData();
  }
}

// Atualizar status do card localmente sem recarregar
function updateCardStatusLocally(inspectionId, newStatus) {
  // Encontrar a inspeção no array local
  const inspection = allInspections.find(insp => insp.id == inspectionId);
  if (inspection) {
    inspection.status = newStatus;
  }

  // Atualizar contadores
  updateStatistics();
  updateColumnCountsFromData();
}

// Configurar event listeners para drag and drop nas colunas
function setupDragAndDrop() {
  document.querySelectorAll('.column-content').forEach(column => {
    column.addEventListener('dragover', handleDragOver);
    column.addEventListener('dragenter', handleDragEnter);
    column.addEventListener('dragleave', handleDragLeave);
    column.addEventListener('drop', handleDrop);
  });
}

// Inicializar kanban quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
  loadKanbanData();
  setupDragAndDrop();
});
