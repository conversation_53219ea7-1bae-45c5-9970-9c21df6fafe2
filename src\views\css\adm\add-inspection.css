.form-page-container {
    display: flex;
    justify-content: center;
    padding: 2rem 1rem;
}

.form-wrapper {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    width: 100%;
    max-width: 600px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

[data-theme="dark"] .form-wrapper {
    background-color: #1f2937;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.2);
}

.h4.form-title {
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1.5rem;
    color: #1f2937;
    transition: color 0.3s ease;
}

[data-theme="dark"] .h4.form-title {
    color: #ffffff;
}

.message-area {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: none;
}
.message-area.success {
    background-color: #d1fae5;
    color: #065f46;
    display: block;
}
.message-area.error {
    background-color: #fee2e2;
    color: #991b1b;
    display: block;
}

[data-theme="dark"] .message-area.success {
    background-color: #064e3b;
    color: #a7f3d0;
}

[data-theme="dark"] .message-area.error {
    background-color: #7f1d1d;
    color: #fecaca;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
    transition: color 0.3s ease;
}

[data-theme="dark"] .form-label {
    color: #d1d5db;
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
    transition: color 0.3s ease;
}

[data-theme="dark"] .form-help {
    color: #9ca3af;
}

input[type="text"],
input[type="url"],
select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-sizing: border-box;
    transition: border-color 0.2s, box-shadow 0.2s, background-color 0.3s ease, color 0.3s ease;
    background-color: #ffffff;
    color: #1f2937;
}

[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="url"],
[data-theme="dark"] select {
    background-color: #374151;
    border-color: #6b7280;
    color: #ffffff;
}

input[type="text"]:focus,
input[type="url"]:focus,
select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.2);
}

[data-theme="dark"] input[type="text"]:focus,
[data-theme="dark"] input[type="url"]:focus,
[data-theme="dark"] select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

input[readonly] {
    background-color: #f3f4f6;
    cursor: not-allowed;
}

[data-theme="dark"] input[readonly] {
    background-color: #1f2937;
    color: #9ca3af;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    background-color: #f9fafb;
    transition: border-color 0.2s, background-color 0.2s;
}

[data-theme="dark"] .upload-area {
    border-color: #6b7280;
    background-color: #374151;
}

.upload-area:hover {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

[data-theme="dark"] .upload-area:hover {
    border-color: #3b82f6;
    background-color: #1e3a8a;
}

.upload-icons {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
}

.icon-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    transition: color 0.3s ease;
}

[data-theme="dark"] .icon-wrapper {
    color: #d1d5db;
}

.upload-text {
    font-weight: 500;
    color: #4b5563;
    transition: color 0.3s ease;
}

[data-theme="dark"] .upload-text {
    color: #d1d5db;
}

#imageUpload {
    display: none;
}

.image-preview {
    max-width: 100%;
    max-height: 150px;
    margin-bottom: 1rem;
    border-radius: 0.375rem;
}

.preview-info {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
}

[data-theme="dark"] .preview-info {
    color: #ffffff;
}

.remove-selection-btn {
    background: none;
    border: 1px solid #ef4444;
    color: #ef4444;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
}

.remove-selection-btn:hover {
    background-color: #fee2e2;
}

[data-theme="dark"] .remove-selection-btn {
    border-color: #ef4444;
    color: #ef4444;
}

[data-theme="dark"] .remove-selection-btn:hover {
    background-color: #7f1d1d;
    color: #fecaca;
}

.form-footer {
    margin-top: 2rem;
    text-align: right;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: #2563eb;
}

[data-theme="dark"] .btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
}

[data-theme="dark"] .btn-primary:hover {
    background-color: #2563eb;
}

/* Melhorias adicionais para modo escuro */
.upload-area svg {
    color: #9ca3af;
    transition: color 0.3s ease;
}

.upload-area:hover svg {
    color: #3b82f6;
}

[data-theme="dark"] .upload-area svg {
    color: #d1d5db;
}

[data-theme="dark"] .upload-area:hover svg {
    color: #3b82f6;
}

/* Placeholder text no modo escuro */
[data-theme="dark"] input::placeholder,
[data-theme="dark"] select option {
    color: #9ca3af;
}

[data-theme="dark"] select option {
    background-color: #374151;
    color: #ffffff;
}