<link rel="stylesheet" href="/css/sidebar.css">

<% const user = {
  avatar: 'https://github.com/m4rcusml.png',
  name: '<PERSON><PERSON> Ipsum',
  email: '<EMAIL>',
  teams: 3
} %>

<div class="popup-shadow" onclick="closeSidebar()"></div>

<div class="sidebar-container">
  <div class="top bg-primary">
    <img src="<%= user.avatar %>" class="avatar" alt="user photo">

    <div class="user-info">
      <p class="name"><%= user.name %></p>
      <p class="email text-muted"><%= user.email %></p>
      <p class="teams text-muted"><%= user.teams %> equipes</p>
    </div>
  </div>

  <nav>
    <button onclick="navigateTo('/home')">
      <img src="/assets/icons/home-muted.svg" alt="Home Icon">
      <span class="subtitle2 text-muted-foreground">Home</span>
    </button>

    <button onclick="navigateTo('/reports')">
      <img src="/assets/icons/document-muted.svg" alt="Document Icon">
      <span class="subtitle2 text-muted-foreground">Relatórios</span>
    </button>

    <button onclick="navigateTo('/map')">
      <img src="/assets/icons/map-muted.svg" alt="Map Icon">
      <span class="subtitle2 text-muted-foreground">Mapa</span>
    </button>
  </nav>
</div>

<script>
  function closeSidebar() {
    document.querySelector('.popup-shadow').classList.toggle('active');
    document.querySelector('.sidebar-container').classList.toggle('active');
  }

  function navigateTo(url) {
    window.location.href = url;
  }

  function updateButtonStyles() {
    const currentPath = window.location.pathname;
    const buttons = document.querySelectorAll('nav button');

    buttons.forEach(button => {
      const buttonPath = button.getAttribute('onclick').match(/'([^']+)'/)[1];

      if (currentPath === buttonPath) {
        button.querySelector('img').src = button.querySelector('img').src.replace('-muted', '-primary');
        button.querySelector('span').classList.remove('text-muted-foreground');
        button.querySelector('span').classList.add('text-primary');
      } else {
        button.classList.remove('btn-primary');
        button.classList.add('btn-outline');
        if (!button.querySelector('img').src.includes('-muted')) {
          button.querySelector('img').src = button.querySelector('img').src.replace('-primary', '-muted');
        }
        button.querySelector('span').classList.remove('text-white', 'text-primary');
        button.querySelector('span').classList.add('text-muted-foreground');
      }
    });
  }

  document.addEventListener('DOMContentLoaded', updateButtonStyles);
</script>