const db = require('../config/db');
jest.mock('../config/db'); 

const TipoSistema = require('../models/tipoSistemaModel');

describe('Model: TipoSistema', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve buscar todos os tipos de sistema', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ nome: 'Estrutural' }] });
    const result = await TipoSistema.findAll();
    expect(result[0].nome).toBe('Estrutural');
  });

  it('deve tratar erro ao buscar tipos de sistema', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro DB'));
    await expect(TipoSistema.findAll()).rejects.toThrow('Erro DB');
  });
});