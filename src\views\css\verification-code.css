header {
    display: none;
}

main {
    display: grid;
    place-items: center;
    height: 100vh;
    padding: 1.25rem;
}

.verification-container {
    background-color: var(--background-color);
    padding: 1.5rem;
    border-radius: 0.75rem;
    max-width: 25rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.logo-wrapper {
    text-align: center;
}

.logo-wrapper img {
    height: 5.75rem;
}

.text-wrapper {
    text-align: left;
}

.text-wrapper h1 {
    margin-bottom: 0.5rem;
}

.code-input-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.code-input-real {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    border: none;
    background: transparent;
    text-align: center;
    caret-color: var(--primary-color);
    letter-spacing: 2rem;
    padding-left: 1.2rem;
    font-size: 1.5rem;
}

.code-input-visuals {
    display: flex;
    justify-content: center;
    align-items: center;
}

.code-box {
    width: 3rem;
    height: 3rem;
    text-align: center;
    font-size: 1.125rem;
    border: 1px solid var(--muted-foreground-color);
    border-radius: 0.375rem;
    display: grid;
    place-items: center;
    font-weight: 600;
    transition: border-color 0.2s;
}

.code-box:nth-child(1),
.code-box:nth-child(5) {
    border-radius: 0.375rem 0 0 0.375rem;
}

.code-box:nth-child(3),
.code-box:nth-child(7) {
    border-radius: 0 0.375rem 0.375rem 0;
}

.code-box:nth-child(2),
.code-box:nth-child(6) {
    border-radius: 0;
}

.code-box.filled {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.code-box.focused {
  border-color: var(--primary-color);
  border-width: 2px;
  box-shadow: 0 0 0 2px rgba(0, 85, 140, 0.2);
}

.dot {
    background-color: var(--foreground-color);
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin: 0 1rem;
}

.verification-container form {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
}