<img src="../assets/logointeli.png">

# WAD - Web Application Document - Módulo 2 - Inteli

# BugBusters

## Integrantes:

- <a href="https://www.linkedin.com/in/ana-j%C3%BClia-ribeiro/"><PERSON></a>
- <a href="https://www.linkedin.com/in/bernardo<PERSON>rindo/"><PERSON></a>
- <a href="https://www.linkedin.com/in/clara-benito/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/jo%C3%A3ocardosodias/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/marcus-valente/"><PERSON></a>
- <a href="https://www.linkedin.com/in/rebeca-namura-sbroglio-0a85ba358/">Rebeca Namura Sbroglio</a> 
- <a href="https://www.linkedin.com/in/vitor-ribeiro-2822a932a/">Vitor <PERSON></a>

## Sumário

[1. Introdução](#c1)

[2. Visão Geral da Aplicação Web](#c2)

[3. Projeto Técnico da Aplicação Web](#c3)

[4. Desenvolvimento da Aplicação Web](#c4)

[5. Testes da Aplicação Web](#c5)

[6. Estudo de Mercado e Plano de Marketing](#c6)

[7. Conclusões e trabalhos futuros](#c7)

[8. Referências](c#8)

[Anexos](#c9)

<br>

# <a name="c1"></a>1. Introdução 

&emsp;A inspeção predial enfrenta desafios significativos, incluindo a diversidade de tipologias de edifícios, a ausência de padronização nos processos e as dificuldades no gerenciamento dos dados coletados durante as vistorias. Conforme evidenciado pelo IPT (2023), as soluções disponíveis no mercado frequentemente não atendem plenamente às necessidades específicas do setor, seja por falta de flexibilidade, dificuldade de uso ou questões relacionadas à segurança e organização das informações.

&emsp;Diante desse contexto, o InsPecT apresenta-se como solução inovadora, consistindo em uma aplicação web especializada para inspeções prediais, desenvolvida com foco em usabilidade, segurança e acessibilidade multiplataforma. A plataforma foi concebida para otimizar o registro estruturado de dados técnicos, imagens e documentação durante as vistorias, garantindo completa rastreabilidade das informações e oferecendo suporte eficaz aos processos decisórios.

&emsp;Os diferenciais competitivos da solução incluem a capacidade de personalização dos campos de acordo com as particularidades de cada edificação, a integração direta com modelos digitais das construções e a geração automatizada de relatórios técnicos padronizados em formatos exportáveis. O sistema incorpora ainda mecanismos robustos de controle de acesso e identificação de usuários, assegurando níveis adequados de segurança e transparência em todas as fases operacionais.

&emsp;O valor estratégico do InsPecT manifesta-se principalmente através da agilização, organização e confiabilidade introduzidas nos processos de inspeção predial, estabelecendo uma ponte eficiente entre tecnologias avançadas e as demandas concretas do mercado. Dessa maneira, a solução posiciona-se como ferramenta estratégica para o IPT e seus parceiros no processo de modernização do setor e na consolidação de melhores práticas em engenharia diagnóstica.

# <a name="c2"></a>2. Visão Geral da Aplicação Web 

## 2.1. Escopo do Projeto 

### 2.1.1. Modelo de 5 Forças de Porter 

&emsp; O IPT, com mais de 125 anos de excelência, é um dos maiores centros de pesquisa aplicada no Brasil. A análise das Cinco Forças ajuda a entender seu posicionamento estratégico no mercado. Desenvolvida por Michael Porter (1980), a análise das Cinco Forças é uma ferramenta estratégica fundamental para avaliar a competitividade de uma organização em seu setor. Essas forças ajudam a entender a atratividade e lucratividade de um setor, orientando a formulação de estratégias competitivas.

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 1</strong> – Representação das 5 Forças de Porter</p>
    <img src="../assets/negocios/5forcasdeporter.png" style="max-width: 100%; height: auto; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---

#### Análise das Cinco Forças de Porter

**Poder de Negociação dos Fornecedores (Alto)**:A dependência do IPT de equipamentos especializados com limitada disponibilidade de fornecedores confere significativo poder de barganha a esses agentes, que exercem controle sobre preços e prazos. A transformação digital dos processos, com sua demanda por soluções avançadas de segurança cibernética e sistemas sofisticados de gestão de acessos (controle hierárquico de permissões, auditoria de atividades), tende a ampliar ainda mais a influência dos fornecedores especializados em tecnologia da informação.  


**Poder de Negociação dos Clientes (Moderado)**:Embora os serviços oferecidos apresentem alto grau de especialização, a existência de alternativas no mercado mantém o poder de barganha dos clientes em nível intermediário. O reduzido número de contratantes confere peso individual a cada negociação. A implementação de funcionalidades digitais como checklists inteligentes, padronização automatizada de relatórios e sistemas de controle de acesso diferenciado (administradores, coordenadores, inspetores) pode agregar valor substantivo, sem contudo eliminar completamente o poder negocial dos clientes.  


**Ameaça de Produtos Substitutos (Moderada)**:Soluções digitais genéricas - incluindo softwares de desenho técnico e plataformas de compartilhamento de arquivos - representam alternativas parciais para casos específicos, particularmente quando não há disponibilidade de plantas detalhadas ou quando os requisitos se limitam à gestão básica de documentos. Contudo, tais alternativas carecem da profundidade analítica, da padronização técnica e dos mecanismos de segurança que um sistema especializado como o proposto pode oferecer, especialmente considerando recursos como checklists contextualizados e matrizes complexas de controle de acessos. 


**Ameaça de Novos Entrantes (Baixa)**:As barreiras à entrada permanecem significativas, abrangendo requisitos como infraestrutura física e digital especializada, qualificação técnica da equipe, reputação institucional consolidada e, particularmente na esfera digital, exigências complexas de segurança da informação, governança de dados e conformidade normativa. A natureza especializada das soluções, que demandam integração sistêmica e impossibilitam abordagens offline simplificadas, eleva adicionalmente esses obstáculos competitivos.


**Rivalidade entre Concorrentes (Baixa)**: O mercado caracteriza-se por limitada concorrência direta, dado o reduzido número de instituições com capacitação técnica comparável e a natureza altamente especializada dos projetos. A digitalização dos processos de inspeção - com automação de relatórios, gestão inteligente de equipes e sistemas hierárquicos de permissões - potencializa ainda mais os diferenciais competitivos do IPT, ampliando sua vantagem estratégica no segmento.

---

### 2.1.2. Análise SWOT da Instituição Parceira 

&emsp; A Matriz SWOT (Strengths, Weaknesses, Opportunities e Threats) é uma ferramenta estratégica essencial para avaliar a posição competitiva do parceiro. Esta permite identificar forças e fraquezas internas, bem como oportunidades e ameaças externas que impactam seu desempenho. Ao aplicar essa análise, o IPT pode efetivamente alinhar suas capacidades às demandas do mercado, antecipar riscos e explorar nichos promissores. Isso fortalece sua atuação nos campos de pesquisa, inovação e serviços tecnológicos, contribuindo para decisões assertivas e um posicionamento estratégico mais sólido frente aos desafios do setor (Salesforce, 2024).

<div align="center" style="margin-bottom: 1em;">
<p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 2</strong> - Análise SWOT - IPT</p>
<img src="../assets/matrizSwot.png"><br />
<p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
    Fonte: Grupo BugBusters, Faculdade Inteli 2025.
</p>
</div>

---
#### Forças (Strengths)
1. **Reputação consolidada:** mais de 100 anos de atuação reconhecida em pesquisa aplicada e desenvolvimento tecnológico (Assembleia Legislativa do Estado de São Paulo, 2014).

2. **Infraestrutura técnica robusta:** laboratórios de ponta e equipamentos especializados que atraem projetos de grande porte (Assembleia Legislativa do Estado de São Paulo, 2014).

3. Corpo técnico qualificado: profissionais altamente capacitados, com forte formação acadêmica e experiência prática;

4. **Parcerias com governo e indústria:** atuação histórica em colaboração com órgãos públicos e empresas estratégicas (IPT, 2024b, 2024c).
---
#### Fraquezas (Weaknesses)

1. **Burocracia e lentidão nos processos:** Apesar de esforços para desburocratizar, ainda enfrenta desafios relacionados à agilidade nos processos internos (IPT, 2017).

2. **Dificuldade de captação de recursos privados:** Como instituição pública, pode enfrentar limitações na atração de investimentos privados para projetos específicos (Sindicato dos Engenheiros no Estado de São Paulo [SEESP], 2019).

3. **Baixa visibilidade no setor privado:** Apesar de sua relevância, o IPT pode não ser amplamente conhecido por empresas privadas que buscam soluções tecnológicas (IPT, 2024d).

4. **Pouca flexibilidade em seus processos:** Dificulta a criação de projetos e a implementação de soluções inovadoras (IPT, 2024d).
---
#### Oportunidades (Opportunities)

1. **Expansão das demandas por inovação:** O aumento da digitalização e necessidade por soluções tecnológicas abre espaço para o desenvolvimento de novas aplicações (IPT, 2024a).

2. **Fomento à pesquisa aplicada:** Existem diversos incentivos à Pesquisa, Desenvolvimento e Inovação (PD&I) disponíveis no país (IPT, 2024a).

3. **Digitalização de processos e serviços:** possibilidade de modernizar atendimentos e ampliar capilaridade de atuação (IPT, 2023).

4. **Parcerias com hubs de inovação:** integração com ecossistemas como centros tecnológicos e startups pode renovar a imagem institucional. Programas como o IPT Open Experience promovem a conexão com empresas e startups, ampliando oportunidades de colaboração (IPT Open Experience, 2023).
---
#### Ameaças (Threats)

1. **Concorrência com centros privados mais ágeis:** Empresas privadas podem desenvolver soluções similares com maior rapidez e flexibilidade.

2. **Cortes de orçamento público:** Reduções no financiamento público podem impactar a continuidade e desenvolvimento de projetos (Câmara dos Deputados, 2022).

3. **Dificuldades na retenção de talentos:** profissionais migram para o setor privado em busca de melhores condições;

4. **Aceleração tecnológica global:** risco de defasagem se não houver atualização constante dos métodos e equipamentos.

---
&emsp; Com base nesta análise SWOT, destacamos que o IPT pode utilizar sua tradição centenária, reputação técnica e infraestrutura avançada para ampliar sua atuação junto ao setor privado e estabelecer parcerias estratégicas com startups, empresas de base tecnológica e hubs de inovação. Ao modernizar seus processos e investir na digitalização de serviços, o instituto pode superar entraves burocráticos e aumentar sua agilidade frente a concorrentes como SENAI, Embrapii e centros de P&D privados. Essas iniciativas fortaleceriam sua competitividade e mitigariam os impactos de cortes orçamentários e da defasagem tecnológica. O cenário atual exige do IPT uma postura mais adaptativa e voltada ao mercado, a fim de manter sua relevância frente à crescente demanda por inovação e soluções rápidas. Seu reposicionamento estratégico é essencial para responder à concorrência com instituições mais flexíveis e atualizadas tecnologicamente, garantindo a continuidade de sua contribuição histórica para o desenvolvimento nacional.

### 2.1.3. Solução



#### 1. Problema a ser resolvido

&emsp;A baixa adesão a inspeções prediais está relacionada à complexidade e diversidade das edificações, que apresentam características únicas e exigem abordagens específicas. Além disso, a falta de padronização nas ferramentas atualmente disponíveis dificulta a criação de soluções escaláveis e eficazes. Desenvolvedores e inspetores enfrentam barreiras técnicas e operacionais ao tentar adaptar sistemas genéricos a contextos variados, resultando em ineficiência, retrabalho e vulnerabilidades na gestão predial.



#### 2. Dados disponíveis

Não se aplica.



#### 3. Solução proposta

&emsp;A proposta consiste no desenvolvimento de uma plataforma digital integrada, voltada  para inspeções prediais. A ferramenta tem como objetivo principal padronizar o processo de inspeção, reduzir a dependência de métodos manuais e descentralizados, e fornecer recursos tecnológicos que ampliem a confiabilidade, agilidade e rastreabilidade das informações coletadas em campo.

&emsp;A plataforma será projetada para atender às necessidades de técnicos, engenheiros e equipes de inspeção de forma colaborativa e adaptável a diferentes contextos prediais (residenciais, comerciais, industriais etc.). Entre suas funcionalidades centrais, destacam-se:

- **Formulários personalizáveis e dinâmicos**: adaptados ao tipo de edificação, sistema construtivo e ambiente avaliado, permitindo o registro contextualizado das informações.
- **Checklists inteligentes**: com base nas normas técnicas da área, sugerindo campos e etapas obrigatórias conforme o tipo de inspeção e reduzindo falhas por omissão.
- **Catálogo de manifestações patológicas**: com lista padronizada de problemas identificáveis (fissuras, infiltrações, destacamentos, entre outros), incluindo opção para anexar fotografias e adicionar descrições detalhadas.
- **Classificação modular**: as informações são organizadas por sistemas (piso, parede, teto, cobertura etc.) e por ambientes (cozinha, banheiro, sala, etc.), o que facilita a análise técnica e a visualização posterior dos dados.
- **Gerenciamento de inspeções e subinspeções**: possibilidade de criar inspeções principais e dividi-las em subinspeções atribuídas a diferentes membros da equipe, com rastreamento de autoria e desempenho.
- **Banco de dados estruturado**: todos os dados são armazenados em um banco seguro e relacional, garantindo fácil recuperação, comparação e geração de históricos por imóvel, equipe ou tipo de problema.
- **Geração automatizada de relatórios técnicos**: com layout padronizado, exportável em diversos formatos (PDF, DOCX etc.), incluindo todas as evidências e dados da inspeção.
- **Painéis de análise em tempo real**: dashboards com indicadores de desempenho, histórico de inspeções, tipos de patologias mais frequentes, entre outros.

&emsp;A solução será **escalável e responsiva**, adequada tanto para inspeções pontuais quanto para operações mais amplas, como diagnósticos técnicos de edificações inteiras, com divisão do trabalho entre membros da equipe e acompanhamento contínuo do progresso.

&emsp;Além disso, sua interface será otimizada principalmente para dispositivos móveis, permitindo que as inspeções sejam realizadas diretamente de campo, com rapidez e praticidade



#### 4. Forma de utilização da solução

&emsp;A plataforma será utilizada diretamente por engenheiros, técnicos e demais profissionais responsáveis por inspeções prediais, tanto em campo quanto em ambientes administrativos. O acesso será realizado via navegador, em dispositivos móveis (como celulares e tablets) ou em computadores (desktops e notebooks), sem necessidade de instalação de aplicativos adicionais.

&emsp;A solução será projetada para operar de forma fluida em ambientes diversos, com foco em mobilidade, acessibilidade e produtividade em tempo real. Entre os principais modos de utilização, destacam-se:

- **Interface responsiva e intuitiva**: adaptada para telas sensíveis ao toque, com navegação simplificada e preenchimento guiado dos formulários em etapas claras, orientando o usuário em cada fase da inspeção.
- **Organização por equipes de inspeção**: possibilita o cadastro e o gerenciamento de múltiplos membros por inspeção, com atribuição de funções e responsabilidades específicas.
- **Realização de subinspeções distribuídas**: cada membro da equipe pode ser designado a subáreas (por exemplo, apartamentos individuais ou setores distintos de um edifício), com vínculo automático do responsável por cada subinspeção.
- **Registro multimídia em campo**: suporte ao upload instantâneo de fotografias, vídeos e áudios diretamente do dispositivo móvel, anexando essas evidências a ambientes ou manifestações específicas.
- **Georreferenciamento automático**: coleta da localização da inspeção utilizando o GPS do dispositivo, garantindo rastreabilidade espacial das informações coletadas.
- **Sincronização de dados com banco central**: as informações são salvas em tempo real quando houver conexão à internet, ou armazenadas localmente para sincronização posterior em ambientes offline.
- **Geração e exportação de relatórios técnicos**: os dados registrados são automaticamente compilados em relatórios técnicos estruturados, disponíveis para exportação em formatos como PDF, DOCX ou visualização online.

&emsp;Ao concentrar todo o processo de inspeção — desde o planejamento até a emissão de relatórios — em um único sistema digital, a plataforma elimina a necessidade de planilhas manuais, anotações dispersas ou retrabalho administrativo. Isso assegura maior integridade, padronização e confiabilidade das informações coletadas, promovendo ganhos reais de eficiência e controle técnico para empresas e profissionais da área.



#### 5. Benefícios esperados

&emsp;A implementação da plataforma trará ganhos significativos para os profissionais e empresas responsáveis por inspeções prediais, promovendo uma transformação digital no setor. Os principais benefícios esperados incluem:

- **Padronização metodológica das inspeções**: assegura uniformidade nos procedimentos, independentemente do profissional ou tipo de edificação, reduzindo variações nos critérios de avaliação e promovendo confiabilidade técnica.
- **Aumento da produtividade em campo**: elimina tarefas manuais, reduz o tempo necessário para preenchimento de formulários e possibilita a geração imediata de relatórios, otimizando todo o ciclo de inspeção.
- **Minimização de falhas humanas e retrabalho**: a automação de processos e o uso de checklists inteligentes ajudam a evitar esquecimentos, registros incompletos ou informações desencontradas.
- **Aprimoramento da manutenção preventiva**: com dados estruturados e fáceis de consultar, gestores podem antecipar intervenções, planejar reformas com mais assertividade e prolongar a vida útil das edificações.
- **Rastreabilidade, segurança e conformidade**: cada dado inserido é vinculado ao responsável, georreferenciado e armazenado de forma centralizada, garantindo histórico completo e aderência às normas técnicas aplicáveis.



#### 6. Critério de sucesso e avaliação

&emsp;A eficácia da solução será avaliada a partir de indicadores quantitativos e qualitativos, mensurando tanto a adoção quanto o impacto prático da plataforma. Os critérios principais incluem:

- **Nível de adoção e engajamento**: número de inspetores, engenheiros e empresas utilizando ativamente a plataforma em suas rotinas de inspeção.
- **Eficiência operacional**: redução do tempo médio necessário para realizar uma inspeção completa e para a emissão dos respectivos relatórios técnicos.
- **Qualidade e padronização dos documentos gerados**: análise comparativa dos relatórios antes e depois do uso da plataforma, avaliando consistência, completude e aderência às normas.
- **Satisfação dos usuários**: pesquisas periódicas de feedback e usabilidade aplicadas a profissionais em campo e tomadores de decisão.
- **Impacto na gestão predial**: capacidade da plataforma de contribuir com decisões técnicas mais embasadas e ações preventivas mais eficazes, evidenciada por relatos de clientes ou estudos de caso.

&emsp;Esses critérios garantirão não apenas a validação funcional da ferramenta, mas também seu valor agregado no contexto real das inspeções prediais.

### 2.1.4. Value Proposition Canvas: 

O Value Proposition Canvas (Canvas de Proposta de Valor) é uma ferramenta visual que ajuda empresas e empreendedores a entenderem melhor seu público-alvo e a criarem propostas de valor mais alinhadas às suas necessidades (PM3, 2023). A metodologia divide-se em duas partes principais: o perfil do cliente — que detalha suas tarefas, dores e ganhos — e o mapa de valor, que descreve como o produto ou serviço proposto alivia essas dores e gera os ganhos esperados. Ao conectar diretamente o que o cliente precisa com o que a empresa oferece, o Canvas permite desenvolver soluções mais eficazes, aumentar a aderência do produto ao mercado e comunicar com clareza o valor entregue.

Em um cenário de mercado cada vez mais dinâmico, marcado pelo surgimento constante de novos concorrentes e inovações, apenas organizações com um posicionamento claro e alinhado às necessidades dos consumidores conseguem manter sua competitividade (Aqua, 2023). Por isso, toda inovação ou melhoria deve estar diretamente conectada às expectativas do público, garantindo relevância e diferenciação.

A Figura 3 apresenta a proposta de valor da aplicação web que será desenvolvida para o IPT, que visa otimizar o processo de inspeção predial. Essa solução se fundamenta em metodologias modernas, como visualização interativa de dados e usabilidade centrada no usuário, para tornar as inspeções mais eficientes, precisas e acessíveis (SERPRO, 2018).

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 3</strong> – Canvas Prosposta de Valor - IPT</p>
    <img src="../assets/negocios/valuePropositionn.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

&emsp; ​A figura 3 ilustra uma análise estratégica voltada para resolver desafios recorrentes no processo de inspeção predial, como burocracia excessiva, uso de ferramentas ineficientes e dificuldade na padronização de relatórios. Esses obstáculos são mitigados por meio de plataformas web interativas que otimizam a coleta de dados, organizam evidências e facilitam o trabalho de campo com mais agilidade e precisão.​


&emsp;A proposta se apoia em geradores de ganho como a automatização de relatórios, a integração com bancos de dados históricos e checklists inteligentes que se adaptam ao tipo de edificação. Além disso, funcionalidades como upload de fotos em tempo real, análise de risco e visualizações gráficas contribuem para tomadas de decisão mais assertivas e eficientes — especialmente em inspeções técnicas de grande porte.

Soluções como o Checklist Fácil e o Inspeção Pro oferecem recursos que transformam o processo de inspeção predial. O Checklist Fácil disponibiliza checklists digitais, fluxos de trabalho automatizados e integração com sistemas de gestão, permitindo a padronização de processos e a geração automática de relatórios — o que reduz significativamente o retrabalho e aumenta a eficiência operacional (Produttivo, 2024). Já o Inspeção Pro, aplicativo brasileiro, possibilita a personalização de formulários, o registro de fotos em tempo real e o controle de não conformidades, tudo acessível por dispositivos móveis (Inspeção Pro, 2024).


&emsp; Essa solução foi desenhada com foco no usuário engenheiro, atendendo às expectativas por ferramentas intuitivas, compatíveis com dispositivos móveis e que reduzam o retrabalho. Ao transformar um processo frequentemente manual e propenso a falhas em uma experiência digital fluida e confiável, a plataforma se posiciona como uma inovação relevante no setor de manutenção predial, ampliando a rastreabilidade das informações e a vida útil dos edifícios.

&emsp; Enquanto métodos tradicionais de inspeção ainda dependem de anotações físicas, planilhas dispersas e preenchimento manual de laudos, a proposta desenvolvida para o IPT oferece um modelo digital mais moderno, acessível e padronizado. Isso garante maior confiabilidade nas informações e acelera todo o processo de vistoria, desde o registro de campo até a geração de documentos finais (IPT, 2024).

### 2.1.5. Matriz de Riscos do Projeto 

A Matriz de Risco é uma ferramenta visual que avalia e classifica os riscos de um projeto com base em sua probabilidade de ocorrência e impacto potencial. Ela permite identificar, priorizar e gerenciar ameaças e oportunidades, facilitando a tomada de decisões estratégicas e a implementação de medidas preventivas ou mitigadoras (Project Management Institute, 2021).

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 4</strong> – Matriz de risco - Sprint 1</p>
    <img src="../assets/negocios/matriz-de-risco.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

### Ameaças

#### 1. Mudança de escopo pelo parceiro
- Probabilidade: 10%
- Impacto: Moderado 
- Durante o desenvolvimento, alterações significativas nos requisitos pelo IPT podem impactar cronograma e entregas. Isso exigiria retrabalho e possível comprometimento da qualidade final.
- Mitigação: Documentar e validar requisitos detalhadamente na fase inicial, estabelecer processo formal de gestão de mudanças com aprovações necessárias, manter comunicação frequente com stakeholders para identificar alterações antecipadamente e negociar ajustes de prazo/escopo quando necessário.

#### 2. Estimativa falha de tempo das atividades
- Probabilidade: 30%
- Impacto: Moderado
- Erros no planejamento temporal das tarefas podem resultar em atrasos nas entregas e sobrecarga da equipe, afetando a execução adequada do projeto.
- Mitigação: Estabelecer reuniões semanais para planejamento e revisão do backlog, usar técnicas como planning poker para estimar coletivamente o esforço das tarefas, manter uma margem de segurança no cronograma e ajustar continuamente as estimativas com base na velocidade da equipe.

#### 3. Ineficiência das perguntas no kick-off
- Probabilidade: 30%
- Impacto: Muito alto
- Questionamentos inadequados ou insuficientes na reunião inicial podem deixar lacunas de entendimento sobre requisitos essenciais do projeto.
- Mitigação: Preparar roteiro detalhado de perguntas técnicas e de negócio antes do kick-off, validar internamente os questionamentos com a equipe, e manter canal aberto com o parceiro para esclarecimentos posteriores via e-mail ou reuniões complementares.

#### 4. Problema de comunicação na equipe
- Probabilidade: 50%
- Impacto: Alto
- Falhas na comunicação interna podem gerar interpretações divergentes dos requisitos e desalinhamento no desenvolvimento das funcionalidades.
- Mitigação: Comprometer-se com as reuniões diárias curtas (daily meetings), documentar decisões tomadas, usar ferramentas de comunicação persistentes (Slack/Whatsapp) e manter quadro Kanban atualizado para visibilidade das tarefas.

---

### Oportunidades

#### 1. Fornecimento de dados realistas de inspeção
- Probabilidade: 30%
- Impacto: Muito alto
- Acesso a dados de inspeções prediais próximos aos usados no contexto do parceiro, permitindo desenvolver uma solução mais precisa e alinhada com as suas necessidades práticas.

#### 2. Fornecimento de template de relatório real
- Probabilidade: 30%
- Impacto: Alto
- Disponibilidade de modelos de relatórios utilizados pelo IPT facilita a padronização e adequação do sistema às práticas existentes.

#### 3. Aulas extras das tecnologias a serem usadas
- Probabilidade: 70%
- Impacto: Muito alto
- Suporte adicional no aprendizado das ferramentas técnicas aumenta a capacidade da equipe em desenvolver uma solução robusta.

#### 4. Identificar soluções inovadoras
- Probabilidade: 70%
- Impacto: Moderado
- Possibilidade de criar funcionalidades únicas que agreguem valor ao processo de inspeção predial, diferenciando o produto no mercado.

## 2.2. Personas 
&emsp; As personas são representações fictícias e detalhadas de usuários, baseadas em dados reais, usadas para entender suas necessidades, comportamentos e desafios. Elas ajudam a equipe a focar em um público-alvo específico, guiando decisões de design e desenvolvimento (Nielsen Norman Group, 2023). No projeto, personas como as representadas abaixo demonstram profissionais da inspeção predial e suas particularidades, o que orienta a criação de uma aplicação que atenda melhor às suas necessidades no dia a dia.

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 5</strong> – Persona 1</p>
    <img src="../assets/ux/uxPersona01.jpg" alt="Persona 1">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 6</strong> – Persona 2</p>
    <img src="../assets/ux/uxPersona02.jpg" alt="Persona 2">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 7</strong> – Persona 3</p>
    <img src="../assets/ux/uxPersona03.jpg" alt="Persona 3">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

&emsp; Observa-se, nas figuras 5, 6 e 7, a representação de personas com diferentes níveis de envolvimento nas inspeções prediais. As figuras 5 e 6 correspondem a uma Engenheira de Manutenção e a um Técnico em Edificações, cada um com suas observações e perspectivas particulares, enquanto a figura 7 retrata uma Coordenadora, cuja atuação se dá de maneira mais indireta, focada na organização das equipes e na gestão dos processos.
&emsp;Dessa forma, a criação das personas permite que a equipe visualize de maneira mais próxima e empática as dores, necessidades e expectativas do parceiro, favorecendo o desenvolvimento de soluções mais assertivas e alinhadas à realidade do usuário.

---

## 2.3. User Stories 

&emsp; No livro User Story Mapping, Jeff Patton apresenta as user stories como uma forma de entender e planejar produtos centrados nas necessidades reais dos usuários. Diferentemente de listas de requisitos tradicionais, as histórias de usuário são descrições breves de como uma pessoa interage com o produto para atingir determinado objetivo. O autor destaca que o valor das user stories está nas conversas que elas geram e no entendimento compartilhado que proporcionam entre todos os envolvidos no processo de desenvolvimento. Ao organizar essas histórias em mapas visuais — os chamados story maps — é possível enxergar a jornada completa do usuário, priorizar funcionalidades e construir soluções que entregam valor desde as primeiras entregas. Essa abordagem ajuda a criar produtos mais úteis, utilizáveis e alinhados com os objetivos do negócio e dos usuários (Patton, 2014).

&emsp; Portanto, compreender e aplicar o mapeamento de histórias do usuário é extremamente importante para o sucesso de qualquer projeto. Essa prática permite que a equipe tenha uma visão clara e compartilhada do que realmente importa para o usuário, facilitando a tomada de decisões, a priorização de funcionalidades e a entrega contínua de valor. Incorporar o story mapping no processo de desenvolvimento é um passo essencial para garantir que o produto final atenda às necessidades reais dos usuários e gere impacto positivo desde as primeiras entregas.

| **Identificação** | **US01** |
| --- | --- |
| **Persona** | Joana Rocha (Administradora) |
| **User Story** | Como administradora do sistema, posso cadastrar inspetores e atribuir permissões de acesso, para garantir que cada usuário tenha o nível de acesso adequado ao sistema. |
| **Critério de aceite 1** | **CR1**: Quando a administradora cadastrar um novo usuário e atribuir o perfil de "inspetor", o sistema deve permitir que o novo usuário tenha permissões completas no banco de dados. <br> **Testes de Aceite**: <br> 1. Cadastrar um novo usuário com o perfil "inspetor". <br> 2. Verificar se as permissões de leitura, escrita e alteração de registros estão disponíveis para o usuário no banco de dados. <br> 3. Tentar editar um registro de inspeção para confirmar se o inspetor pode manipular os dados. |
| **Critério de aceite 2** | **CR2**: Quando a administradora cadastrar um usuário com um perfil que não seja "inspetor", ele deverá ter apenas acesso de consulta, sem permissão para alterar dados no banco. <br> **Testes de Aceite**: <br> 1. Cadastrar um novo usuário com o perfil "analista" (não inspetor). <br> 2. Tentar editar um registro de inspeção para confirmar que o usuário não pode fazer alterações. <br> 3. Confirmar que o usuário pode apenas visualizar os registros no banco de dados. |
| **Critério de aceite 3** | **CR3**: O administrador deverá conseguir alterar o perfil de um usuário, alterando suas permissões de acordo com a mudança. <br> **Testes de Aceite**: <br> 1. Alterar o perfil de um usuário de "não inspetor" para "inspetor". <br> 2. Verificar se as permissões de escrita e leitura foram liberadas para o usuário após a mudança de perfil. <br> 3. Tentar realizar operações de leitura e escrita para confirmar que a permissão foi alterada corretamente. |
| **Critérios INVEST** | **Independente**: Pode ser desenvolvido independentemente de outras funcionalidades. <br> **Negociável**: O tipo de permissão atribuída pode ser negociado e alterado conforme novas necessidades. <br> **Valiosa**: Essencial para garantir o controle de acesso no sistema e proteger dados sensíveis. <br> **Estimável**: A funcionalidade é bem definida e pode ser estimada facilmente. <br> **Pequena**: Trata-se de uma história pequena, realizável dentro de um ciclo de desenvolvimento ágil. <br> **Testável**: Critérios claros para validação através de testes automatizados e manuais. |
---

| **Identificação** | **US02** |
| --- | --- |
| **Persona** | Rafael Souza (Técnico de Edificações) |
| **User Story** | Como Técnico de Edificações, posso registrar uma inspeção de um imóvel, para garantir que as condições da propriedade sejam documentadas adequadamente. |
| **Critério de aceite 1** | **CR1**: Quando o inspetor iniciar uma nova inspeção, o sistema deve permitir que ele registre informações sobre o imóvel, como localização e tipo de problema. <br> **Testes de Aceite**: <br> 1. Iniciar uma nova inspeção. <br> 2. Verificar se há campos para preencher informações sobre o imóvel. <br> 3. Confirmar se as informações podem ser salvas corretamente. |
| **Critério de aceite 2** | **CR2**: O inspetor deve poder adicionar imagens do imóvel à inspeção. <br> **Testes de Aceite**: <br> 1. Tentar adicionar uma imagem à inspeção. <br> 2. Confirmar que a imagem é carregada e visualizável. |
| **Critério de aceite 3** | **CR3**: O inspetor pode finalizar a inspeção e salvar as informações. <br> **Testes de Aceite**: <br> 1. Finalizar a inspeção. <br> 2. Verificar se as informações são salvas corretamente no banco de dados. |
| **Critérios INVEST** | **Independente**: Funcionalidade isolada que não depende de outras funcionalidades para ser implementada. <br> **Negociável**: O layout e a quantidade de campos podem ser ajustados conforme necessidade. <br> **Valiosa**: Essencial para o registro completo da inspeção e documentação do processo. <br> **Estimável**: A funcionalidade é clara e os testes podem ser realizados de forma simples. <br> **Pequena**: Tarefa pequena que pode ser realizada rapidamente. <br> **Testável**: Facilidade para realizar testes manuais e automatizados. |

---

| **Identificação** | **US03** |
| --- | --- |
| **Persona** | Rafael Souza (Técnico de Edificações) |
| **User Story** | Como Técnico de Edificações, posso gerar relatórios completos das inspeções realizadas, para que eu possa documentar todas as informações de forma organizada e compartilhá-las facilmente com meus superiores ou clientes. |
| **Critério de aceite 1** | **CR1**: Quando o inspetor finalizar uma inspeção, o sistema deve permitir que ele gere um relatório com todas as informações registradas. <br> **Testes de Aceite**: <br> 1. Finalizar uma inspeção. <br> 2. Confirmar que o relatório contém dados como: local da inspeção, resultados dos testes, categorias inspecionadas e observações. |
| **Critério de aceite 2** | **CR2**: O inspetor deve conseguir visualizar uma prévia do relatório antes de exportá-lo. <br> **Testes de Aceite**: <br> 1. Gerar um relatório e verificar se a pré-visualização exibe todas as informações corretamente. <br> 2. Confirmar que a prévia pode ser visualizada corretamente em dispositivos móveis e desktops. |
| **Critério de aceite 3** | **CR3**: O inspetor deve poder exportar o relatório gerado para os formatos PDF e DOC. <br> **Testes de Aceite**: <br> 1. Gerar o relatório e exportá-lo em formato PDF. <br> 2. Testar se o relatório pode ser exportado também em formato DOC. <br> 3. Verificar se as informações do relatório são mantidas corretamente nos arquivos exportados. |
| **Critérios INVEST** | **Independente**: A funcionalidade de geração de relatórios pode ser desenvolvida independentemente de outras funcionalidades. <br> **Negociável**: O layout e a quantidade de informações do relatório podem ser ajustados conforme as necessidades do inspetor. <br> **Valiosa**: Essencial para o registro formal das inspeções e para a comunicação dos resultados com outros profissionais. <br> **Estimável**: A funcionalidade é clara e pode ser estimada facilmente. <br> **Pequena**: A tarefa pode ser dividida em pequenas partes, como geração do relatório, pré-visualização e exportação. <br> **Testável**: A funcionalidade pode ser testada facilmente com a criação de relatórios e exportação em diferentes formatos. |

---
| **Identificação** | **US04** |
| --- | --- |
| **Persona** |Rafael Souza (Técnico de Edificações) |
| **User Story** | Como Técnico de Edificações, posso realizar o registro de uma inspeção em múltiplas etapas (por exemplo, cada parte do prédio), para que o processo de inspeção seja mais organizado e detalhado. |
| **Critério de aceite 1** | **CR1**: O sistema deve permitir que o inspetor divida a inspeção em várias partes ou seções, como "estrutura", "instalações elétricas", "segurança". <br> **Testes de Aceite**: <br> 1. Iniciar uma nova inspeção. <br> 2. Dividir a inspeção em seções distintas. <br> 3. Confirmar que as seções são salvas separadamente. |
| **Critério de aceite 2** | **CR2**: O sistema deve permitir que o inspetor registre dados diferentes para cada seção da inspeção (por exemplo, notas, imagens, e observações técnicas). <br> **Testes de Aceite**: <br> 1. Adicionar dados à seção "estrutura". <br> 2. Adicionar dados à seção "instalações elétricas". <br> 3. Verificar se os dados de cada seção são registrados corretamente. |
| **Critério de aceite 3** | **CR3**: Ao finalizar a inspeção, o sistema deve apresentar um resumo de cada seção para revisão antes de salvar. <br> **Testes de Aceite**: <br> 1. Finalizar a inspeção. <br> 2. Verificar se todas as seções e suas informações aparecem no resumo. |
| **Critérios INVEST** | **Independente**: Funcionalidade pode ser isolada e desenvolvida independentemente. <br> **Negociável**: Estrutura das seções pode ser ajustada conforme a necessidade. <br> **Valiosa**: Facilita a inspeção detalhada, melhorando o controle e a organização. <br> **Estimável**: Funcionalidade com escopo claro e implementação simples. <br> **Pequena**: Tarefa pequena que pode ser concluída rapidamente. <br> **Testável**: Facilidade para realizar testes de navegação e preenchimento das seções. |


---

| **Identificação** | **US05** |
| --- | --- |
| **Persona** |  Rafael Souza (Técnico de Edificações) |
| **User Story** | Como Técnico de Edificações, posso registrar as informações de uma inspeção diretamente no sistema utilizando meu celular, para que eu possa fazer o registro no momento da inspeção sem precisar de papel. |
| **Critério de aceite 1** | **CR1**: O inspetor deve poder registrar informações da inspeção diretamente no celular, incluindo localização, tipo de problema e observações. <br> **Testes de Aceite**: <br> 1. Registrar uma inspeção no celular. <br> 2. Confirmar que as informações são salvas corretamente no sistema. |
| **Critério de aceite 2** | **CR2**: O inspetor deve poder adicionar fotos à inspeção enquanto estiver no local. <br> **Testes de Aceite**: <br> 1. Adicionar fotos de áreas inspecionadas. <br> 2. Confirmar que as fotos são carregadas e visualizáveis. |
| **Critério de aceite 3** | **CR3**: O inspetor deve poder acessar as inspeções registradas no celular e editar as informações, se necessário. <br> **Testes de Aceite**: <br> 1. Acessar uma inspeção registrada. <br> 2. Editar informações, como notas ou resultados. <br> 3. Confirmar que as edições são salvas corretamente. |
| **Critérios INVEST** | **Independente**: O registro das inspeções via celular pode ser feito independentemente de outras funcionalidades da plataforma. <br> **Negociável**: A interface do sistema pode ser ajustada conforme o tipo de dispositivo utilizado pelo inspetor. <br> **Valiosa**: Facilita o trabalho do inspetor, permitindo que ele registre dados no momento da inspeção, sem a necessidade de anotações manuais. <br> **Estimável**: A funcionalidade pode ser facilmente estimada e planejada. <br> **Pequena**: A tarefa pode ser dividida em etapas, como registro de dados e upload de fotos. <br> **Testável**: Pode ser testada diretamente no celular, verificando o processo de registro e upload de fotos. |
 ---
| **Identificação** | **US06** |
| --- | --- |
| **Persona** |Beatriz Lemos (Engenheira de Manutenção) |
| **User Story** | Como Engenheira de Manutenção, posso exportar os relatórios das inspeções em diferentes formatos (PDF, DOC, XLS), para garantir que as informações sejam entregues de forma apropriada para os responsáveis ou clientes. |
| **Critério de aceite 1** | **CR1**: O sistema deve permitir que o inspetor escolha o formato do relatório ao finalizar uma inspeção. <br> **Testes de Aceite**: <br> 1. Finalizar a inspeção. <br> 2. Selecionar o formato de exportação desejado (PDF, DOC, XLS). <br> 3. Confirmar que o relatório é exportado no formato correto. |
| **Critério de aceite 2** | **CR2**: O sistema deve gerar um relatório com todas as informações da inspeção, incluindo dados técnicos, notas e imagens, no formato escolhido. <br> **Testes de Aceite**: <br> 1. Gerar um relatório no formato PDF. <br> 2. Verificar se todas as informações (dados, notas, imagens) estão presentes no relatório. |
| **Critério de aceite 3** | **CR3**: O sistema deve permitir que o usuário visualize o relatório exportado antes de finalizá-lo, para garantir que todas as informações estejam corretas. <br> **Testes de Aceite**: <br> 1. Exportar o relatório. <br> 2. Visualizar o arquivo antes de salvar ou enviar. <br> 3. Confirmar que o relatório está completo e correto. |


---
| **Identificação** | **US07** |
| --- | --- |
| **Persona** | Beatriz Lemos (Engenheira de Manutenção) |
| **User Story** | Como Engenheira de Manutenção, posso gerar relatórios completos com todas as informações e imagens da inspeção, para que o cliente receba um documento formal e profissional. |
| **Critério de aceite 1** | **CR1**: O sistema deve permitir a seleção de uma inspeção finalizada e gerar automaticamente um relatório em PDF. <br> **Testes de Aceite**: <br> 1. Selecionar uma inspeção encerrada. <br> 2. Gerar relatório. <br> 3. Verificar se todos os dados estão no PDF. |
| **Critério de aceite 2** | **CR2**: O relatório deve incluir as imagens adicionadas durante a inspeção. <br> **Testes de Aceite**: <br> 1. Inserir imagens em uma inspeção. <br> 2. Gerar relatório. <br> 3. Confirmar presença das imagens no arquivo final. |
| **Critério de aceite 3** | **CR3**: O relatório deve poder ser exportado em múltiplos formatos (.pdf, .docx). <br> **Testes de Aceite**: <br> 1. Escolher formato de exportação. <br> 2. Confirmar criação do arquivo. <br> 3. Verificar se a formatação é mantida. |
---

| **Identificação** | **US08** |
| --- | --- |
| **Persona** | Beatriz Lemos (Engenheira de Manutenção) |
| **User Story** | Como Engenheira de Manutenção, posso consultar a lista de inspeções anteriores para verificar histórico e status de inspeções passadas, para garantir que não haja sobrecarga de inspeções ou problemas não resolvidos. |
| **Critério de aceite 1** | **CR1**: O sistema deve apresentar uma lista filtrável de inspeções passadas, com status (finalizada, pendente, em andamento). <br> **Testes de Aceite**: <br> 1. Acessar a lista de inspeções. <br> 2. Filtrar por status. <br> 3. Verificar se as inspeções anteriores aparecem corretamente. |
| **Critério de aceite 2** | **CR2**: O sistema deve permitir visualizar detalhes de cada inspeção, como dados técnicos, imagens e observações. <br> **Testes de Aceite**: <br> 1. Selecionar uma inspeção anterior. <br> 2. Verificar se todos os detalhes estão acessíveis (dados técnicos, imagens). |
| **Critério de aceite 3** | **CR3**: O sistema deve indicar inspeções pendentes ou em atraso de forma destacada para facilitar o acompanhamento. <br> **Testes de Aceite**: <br> 1. Acessar a lista de inspeções. <br> 2. Confirmar que inspeções pendentes estão destacadas com uma cor ou alerta visual. |

---
| **Identificação** | **US09** |
| --- | --- |
| **Persona** | Rafael Souza (Técnico de Edificações) |
| **User Story** | Como Técnico de Edificações, posso adicionar notas e observações detalhadas durante a inspeção, para que todas as informações relevantes sejam registradas de forma clara e documentada. |
| **Critério de aceite 1** | **CR1**: O sistema deve permitir que o inspetor adicione notas em campos de texto livre durante a inspeção. <br> **Testes de Aceite**: <br> 1. Durante a inspeção, adicionar uma nota na seção apropriada. <br> 2. Verificar se a nota é salva corretamente. |
| **Critério de aceite 2** | **CR2**: O sistema deve garantir que as notas sejam editáveis até o momento da finalização da inspeção. <br> **Testes de Aceite**: <br> 1. Adicionar uma nota. <br> 2. Editar a nota antes de finalizar a inspeção. <br> 3. Verificar se as alterações são salvas. |
| **Critério de aceite 3** | **CR3**: As notas devem ser visíveis no relatório gerado após a conclusão da inspeção. <br> **Testes de Aceite**: <br> 1. Gerar um relatório da inspeção. <br> 2. Verificar se as notas estão incluídas no relatório. |

---


| **Identificação** | **US10** |
| --- | --- |
| **Persona** | Rafael Souza (Técnico de Edificações) |
| **User Story** | Como Técnico de Edificações, posso acessar inspeções passadas e gerar novos relatórios baseados nessas inspeções, para que eu possa economizar tempo ao revisar informações já registradas anteriormente. |
| **Critério de aceite 1** | **CR1**: O inspetor deve poder acessar um histórico de inspeções realizadas anteriormente. <br> **Testes de Aceite**: <br> 1. Acessar o histórico de inspeções. <br> 2. Confirmar que todas as inspeções passadas estão listadas corretamente. |
| **Critério de aceite 2** | **CR2**: O inspetor deve conseguir selecionar uma inspeção anterior e gerar um relatório a partir dela. <br> **Testes de Aceite**: <br> 1. Selecionar uma inspeção do histórico. <br> 2. Gerar um relatório com base na inspeção selecionada. <br> 3. Confirmar que os dados da inspeção selecionada são corretamente refletidos no relatório gerado. |
| **Critério de aceite 3** | **CR3**: O inspetor deve ser capaz de editar as informações de uma inspeção passada antes de gerar o relatório. <br> **Testes de Aceite**: <br> 1. Editar dados de uma inspeção passada, como observações e resultados. <br> 2. Gerar o relatório e confirmar se as edições foram corretamente aplicadas. |

---


# <a name="c3"></a>3. Projeto da Aplicação Web 

## 3.1. Arquitetura 


Para desenvolver o diagrama e arquitetura da aplicação web, utilizou-se do padrão **MVC (Model-View-Controller)**, que oferece:

 **Vantagens:**
- Maior flexibilidade e escalabilidade
- Facilidade de manutenção e evolução
- Melhor desempenho das aplicações
- Redução de custos e riscos
- Segurança aprimorada



## Diagrama da Arquitetura

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 10</strong> – Cores usadas no projeto</p>
    <img src="../assets/arquiteturamvc.drawio.png" style="max-width: 100%; height: auto; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

### Camadas da Arquitetura

#### Model (Modelo)
Responsável pela lógica de negócios e persistência de dados:

- Núcleo da aplicação
- Armazena e valida dados
- Independente da interface
- Acessado via Controller
- Facilita testes e reúso

#### Controller (Controle)
Intermediário entre View e Model:

- Processa requisições
- Gerencia fluxo de dados
- Aplica regras de negócio
- Retorna respostas adequadas

#### View (Visualização)
Interface com o usuário:

- Renderização de templates (EJS)
- Estilização (CSS)
- Interatividade (JavaScript)
- Organização modular

---

### Entidades da Camada Models

| Entidade       | Atributos Principais                                                                 |
|----------------|-------------------------------------------------------------------------------------|
| **Usuários**   | `id`, `email`, `nome`, `senha`                                                     |
| **Equipes**    | `id`, `nome`                                                                       |
| **Endereços**  | `id`, `cep`, `numero`, `complemento`, `referencia`                                 |
| **Inspeções**  | `id`, `nome`, `url_planta`, `id_tipo_edificacao`, `id_equipe`, `id_endereco`, `criado_em` |
| **Fotos**      | `id`, `id_ambiente`, `id_ocorrencia`, `url`                                        |
| **Ambiente**   | `id`, `titulo`, `id_usuario`, `id_inspecao`, `observacoes`, `criado_em`            |
| **Ocorrências**| `id`, `titulo`, `id_inspecao`, `id_ambiente`, `id_tipo_sistema`, `id_tipo_patologia`, `descricao`, `criado_em`, `concluido_em` |
| **Relatórios** | `id`, `id_inspecao`, `url`                                                         |
| **Afazeres**  | `id`, `titulo`                                |
| **Tipo de Sistema**  | `nome`                              |
| **Tipo de Edificação**  | `nome`                              |
| **Tipo de Patologia**  | `nome`,  `id_tipo_sistema`                              |
---

#### Controllers

&emsp; No desenvolvimento do InsPecT, aplicação web voltada à inspeção predial, os controladores desempenham um papel central na arquitetura da solução. Responsáveis por intermediar as ações do usuário e a lógica da aplicação, eles interpretam as requisições vindas da interface e direcionam essas demandas para os componentes adequados, como modelos de dados e visualizações. Essa camada garante que cada interação seja processada de forma segura, estruturada e coerente com os fluxos operacionais definidos, assegurando a rastreabilidade das ações e o controle preciso dos acessos e permissões dentro do sistema.

#### AuthController
&emsp; O AuthController é responsável por lidar com a autenticação de usuários. Sua principal função, loginUsuario, recebe e-mail e senha via requisição e verifica se o usuário existe no banco de dados. Caso o usuário não seja encontrado ou a senha não coincida com a armazenada (comparação direta), retorna um erro de credenciais inválidas. Se as informações forem válidas, o controller gera um token JWT com validade de 1 hora, contendo o ID, nome e e-mail do usuário. Esse token, junto com os dados do usuário, é retornado em formato JSON para ser utilizado na autenticação das próximas requisições.

#### UsuariosController
&emsp; O UsuarioController gerencia operações relacionadas aos usuários do sistema. Ele possui funções para listar todos os usuários (listarUsuarios), buscar um usuário por ID (obterUsuarioPorId), criar um novo usuário (criarUsuario), atualizar dados de um usuário (atualizarUsuario) e deletar um usuário (deletarUsuario). Durante a criação, valida se nome, e-mail e senha foram fornecidos, e impede duplicidade de e-mail. A senha não é retornada nas respostas. O endpoint de atualização permite modificar apenas nome e e-mail. Em todos os casos, são tratadas exceções e erros adequadamente.


#### EquipesController
&emsp; O EquipeController é responsável por gerenciar as operações relacionadas às equipes. Ele permite listar todas as equipes (listarEquipes), criar uma nova (criarEquipe), buscar uma equipe específica com seus usuários (obterEquipeEspecificaComUsuarios), atualizar informações de uma equipe (atualizarEquipe) e deletar uma equipe (deletarEquipe). Além disso, oferece endpoints para adicionar usuários a uma equipe com cargo definido (adicionarUsuarioNaEquipe) e removê-los posteriormente (removerUsuarioDaEquipe). Todos os métodos tratam erros e validações de forma adequada.

#### EnderecosController
&emsp; O EnderecoController gerencia as operações relacionadas aos endereços no sistema. Ele permite listar todos os endereços (listarEnderecos), criar um novo (criarEndereco), buscar um endereço específico pelo ID (obterEnderecoPorId), atualizar dados de um endereço (atualizarEndereco) e remover um endereço (deletarEndereco). Os métodos realizam validações básicas — como a obrigatoriedade do CEP e número — e retornam mensagens apropriadas em caso de erro ou sucesso, garantindo robustez na manipulação dos dados.

#### InspecoesController
&emsp; O InspecaoController é responsável por lidar com as operações referentes às inspeções prediais no sistema. Ele permite listar todas as inspeções (listarInspecoes), criar uma nova inspeção (criarInspecao), buscar uma inspeção específica por ID (obterInspecaoPorId), atualizar os dados de uma inspeção (atualizarInspecao) e excluir uma inspeção (deletarInspecao). O controller realiza validações dos campos obrigatórios e trata adequadamente os erros, garantindo respostas claras e seguras durante o ciclo de vida das inspeções.

#### AmbientesController
&emsp; O AmbienteController gerencia as operações relacionadas aos ambientes dentro de uma inspeção. Ele permite listar todos os ambientes vinculados a uma inspeção específica (listarAmbientesPorInspecao), criar novos ambientes associados a uma inspeção (criarAmbienteNaInspecao), buscar um ambiente individual pelo ID (obterAmbientePorId), atualizar dados de um ambiente existente (atualizarAmbiente) e excluir um ambiente (deletarAmbiente). O controller também realiza validações básicas e verifica a existência da inspeção associada antes de criar ou manipular ambientes.

#### OcorrenciasController
&emsp; O ocorrenciaController gerencia as operações relacionadas às ocorrências dentro de ambientes em uma inspeção predial. Ele permite listar todas as ocorrências vinculadas a um ambiente específico (listarOcorrenciasPorAmbiente), criar novas ocorrências associadas a um ambiente e à inspeção correspondente (criarOcorrenciaNoAmbiente), buscar uma ocorrência individual pelo ID (obterOcorrenciaPorId), atualizar dados de uma ocorrência existente (atualizarOcorrencia) e excluir uma ocorrência (deletarOcorrencia). O controller realiza validações básicas, verifica a existência do ambiente e da inspeção associada, e garante a consistência dos dados antes de criar ou manipular ocorrências.

#### FotosController
&emsp; O fotoController gerencia as operações relacionadas às fotos associadas a ambientes e ocorrências dentro de uma inspeção predial. Ele permite listar todas as fotos vinculadas a um ambiente específico, com filtro opcional por ocorrência (listarFotosPorAmbiente), adicionar uma nova foto a um ambiente (adicionarFotoAmbienteOcorrencia), obter os metadados de uma foto pelo seu ID (obterMetadadosFotoPorId), obter a imagem da foto por ID (obterImagemFotoPorId) e excluir uma foto (deletarFoto). O controller realiza validações básicas para garantir a existência do ambiente antes de criar ou manipular fotos, além de tratar erros e fornecer respostas apropriadas para cada operação.

#### RelatoriosController
&emsp; O relatorioController gerencia as operações relacionadas aos relatórios vinculados às inspeções prediais. Ele permite listar todos os relatórios associados a uma inspeção específica (listarRelatoriosPorInspecao), adicionar um novo relatório a uma inspeção (adicionarRelatorioNaInspecao), buscar um relatório individual pelo ID (obterRelatorioPorId) e excluir um relatório (deletarRelatorio). O controller realiza validações básicas para garantir a existência da inspeção antes de criar ou manipular relatórios, além de tratar erros e fornecer respostas adequadas para cada operação.

#### lookupController
&emsp; O lookupController define e exporta quatro funções responsáveis por listar entidades do sistema: tipos de sistema, afazeres, tipos de edificação e tipos de patologia.. Ele age como o intermediário entre as requisições HTTP e os modelos (banco de dados).

### Views 
&emsp; A camada de Visualização (View) é responsável pela interface com o usuário, apresentando dados e capturando interações. No InsPecT, as views são implementadas usando EJS (Embedded JavaScript templating) para renderização no lado do servidor, CSS para estilização e JavaScript no lado do cliente para interatividade e comunicação assíncrona com o backend. A estrutura da pasta /views organiza esses arquivos em subpastas (pages, css, js, components, layout).

&emsp; A seguir, detalhamos as principais telas (views) da aplicação, agrupadas por perfil de usuário, descrevendo suas funções e conexões com os Controllers.

#### Todos os Usuários
**1. Tela de Login**
- Função: Permite que usuários (inspetores, coordenadores, administradores) autentiquem-se na plataforma InsPecT para acessar suas funcionalidades específicas.

- Componentes/Inputs Principais: Campos para inserção de email e senha; botão para submeter o login. Utiliza views/pages/login.ejs para a estrutura HTML, views/css/login.css para estilização específica e views/js/login.js para a lógica de cliente.

- Conexão com Controllers: Ao submeter o formulário, o views/js/login.js captura os dados (email, senha), previne o envio padrão do formulário e envia uma requisição POST assíncrona para o endpoint da API /api/auth/login (definido em routes/authRoutes.js). Esta rota aciona a função loginUsuario no controllers/authController.js. O controller valida as credenciais consultando o usuariosModel, gera um token JWT em caso de sucesso e retorna uma resposta JSON (com o token ou mensagem de erro) para o login.js, que então redireciona o usuário para a /home ou exibe a mensagem de erro na própria tela de login.

#### Inspetor

**1. Tela Inicial / Dashboard do Inspetor**

- Função: Apresenta uma visão geral das atividades do inspetor, como inspeções atribuídas, pendentes ou em andamento, e acesso rápido a funcionalidades comuns.

- Componentes/Inputs Principais: Lista/cards de inspeções, botões para iniciar/continuar inspeções, possivelmente um resumo de notificações. Utiliza views/pages/home.ejs (renderizado com dados específicos do inspetor), estilizado por views/css/home.css.

- Conexão com Controllers: A rota /home (routes/index.js) renderiza home.ejs. O JavaScript associado a esta página (seja inline ou um arquivo dedicado como homeInspetor.js) faz chamadas à API (ex: /api/inspecoes?inspetorId=..., /api/usuarios/me) para buscar os dados necessários. Essas rotas da API acionam funções nos controllers (inspecoesController.js, usuariosController.js) que consultam os models e retornam os dados em JSON para o frontend atualizar a interface.



**2. Detalhes da Inspeção**

- Função: Exibe todas as informações de uma inspeção específica, incluindo dados gerais, lista de ambientes inspecionados e ocorrências registradas.

- Componentes/Inputs Principais: Informações da inspeção (data, status, equipe), lista de ambientes (links para detalhes do ambiente), botão para adicionar novo ambiente. Utiliza views/pages/inspection-details.ejs e views/css/inspection-details.css.

- Conexão com Controllers: A rota /inspection-details/:inspectionId (routes/index.js) renderiza a página. O JS da página faz chamadas à API (ex: /api/inspecoes/:inspectionId, /api/ambientes?inspecaoId=...) que acionam funções em inspecoesController.js e ambientesController.js para buscar os detalhes e a lista de ambientes, retornando JSON para popular a tela.

**3. Detalhes do Ambiente**

- Função: Mostra os detalhes de um ambiente específico dentro de uma inspeção, incluindo suas características e a lista de ocorrências encontradas.

- Componentes/Inputs Principais: Informações do ambiente (nome, tipo), lista de ocorrências (com fotos e descrições), botão para adicionar nova ocorrência. Utiliza views/pages/ambient-details.ejs e views/css/ambient-details.css.

- Conexão com Controllers: A rota /ambient-details/:ambientId (routes/index.js) renderiza a página. O JS da página chama endpoints da API (ex: /api/ambientes/:ambientId, /api/ocorrencias?ambienteId=...) que acionam funções em ambientesController.js e ocorrenciasController.js para buscar os dados e retornar JSON.



**4. Adicionar/Editar Ambiente**

- Função: Permite ao inspetor registrar um novo ambiente dentro de uma inspeção ou editar um existente, informando suas características.

- Componentes/Inputs Principais: Formulário com campos para nome do ambiente, tipo, dimensões, observações, etc.; botão para salvar. Utiliza views/pages/add-ambient.ejs e views/css/add-ambient.css.

- Conexão com Controllers: A rota /add-ambient/:ambientId? (routes/index.js) renderiza o formulário. Ao salvar, o JS da página envia uma requisição POST (para criar) ou PUT (para editar) para a API (ex: /api/ambientes ou /api/ambientes/:ambientId). As rotas correspondentes acionam as funções criarAmbiente ou atualizarAmbiente em ambientesController.js, que processam os dados, interagem com o model e retornam uma resposta JSON.



**5. Adicionar Ocorrência**

- Função: Permite ao inspetor registrar uma nova ocorrência (patologia, não conformidade) encontrada em um ambiente específico, incluindo descrição e fotos.

- Componentes/Inputs Principais: Formulário com campos para descrição da ocorrência, tipo (usando o catálogo de patologias), nível de criticidade, upload de fotos. Utiliza views/pages/add-occurrence.ejs e views/css/add-occurrence.css.

- Conexão com Controllers: A rota /add-occurrence/:ambientId? (routes/index.js) renderiza o formulário. Ao salvar, o JS envia uma requisição POST para a API /api/ocorrencias, possivelmente incluindo dados de formulário e arquivos de imagem (multipart/form-data). A rota aciona a função criarOcorrencia em ocorrenciasController.js. O upload de fotos pode acionar funções em fotosController.js (ex: uploadFoto) através de uma rota específica /api/fotos. Os controllers retornam JSON indicando sucesso ou falha.



**6. Perfil do Usuário**

- Função: Exibe as informações do inspetor logado e permite a edição de dados básicos (como senha).

- Componentes/Inputs Principais: Visualização dos dados do usuário (nome, email), campos para alteração de senha. Utiliza views/pages/profile.ejs e views/css/profile.css.

- Conexão com Controllers: A rota /profile (routes/index.js) renderiza a página. O JS busca os dados do usuário via API (ex: /api/usuarios/me) que aciona uma função em usuariosController.js. A alteração de senha envia uma requisição PUT para um endpoint da API (ex: /api/usuarios/me/senha) que aciona a função atualizarSenha (ou similar) em usuariosController.js.


**7. Histórico de relatórios**

- Função: Exibe os relatórios que o usuário ja participou com informações gerais.

- Componentes/Inputs Principais: Visualização de dados como: data de incío e fim, endereço inspecionado, equipe, ambientes e ocorrências encontradas. Utiliza views/pages/report-list.ejs e views/css/report-list.css.

- Conexão com Controllers: A rota /report (routes/index.js) renderiza a página. O JS busca dados do usuário via API (ex: /api/relatorios ou /api/relatorios?:relatoriosId). A rota aciona as funções de relatoriosController, que por sua vez, lista todos os relatórios ou por Id.
 

#### Coordenador

**1. Tela Inicial / Dashboard do Coordenador**

- Função: Apresenta uma visão geral das inspeções em andamento, status das equipes, relatórios recentes e acesso a funcionalidades de gerenciamento.

- Componentes/Inputs Principais: Gráficos de progresso, lista de inspeções ativas, status das equipes, atalhos para gerenciamento de equipes e relatórios. Utiliza views/pages/home.ejs (renderizado com dados específicos do coordenador), estilizado por views/css/home.css.

- Conexão com Controllers: Similar ao inspetor, a rota /home renderiza a view. O JS associado busca dados agregados via API (ex: /api/inspecoes/overview, /api/equipes/status, /api/relatorios/recentes). Essas rotas acionam funções em inspecoesController.js, equipesController.js, relatoriosController.js que retornam JSON para popular o dashboard.



**2. Gerenciamento de Equipes (Tela Inferida)**

- Função: Permite ao coordenador visualizar, criar, editar e excluir equipes de inspeção, além de atribuir inspetores a elas.

- Componentes/Inputs Principais: Tabela/lista de equipes existentes com seus membros, botões para adicionar nova equipe, editar e excluir equipe. Utilizaria um EJS como manage-teams.ejs.

- Conexão com Controllers: Uma rota como /manage-teams renderizaria a view. O JS da página chamaria a API /api/equipes para listar as equipes (acionando listarEquipes em equipesController.js). Ações de criar, editar ou excluir enviariam requisições POST, PUT ou DELETE para /api/equipes ou /api/equipes/:teamId, acionando as funções correspondentes (criarEquipe, atualizarEquipe, deletarEquipe) em equipesController.js.



**3. Criação/Edição de Equipe (Tela Inferida)**

- Função: Formulário para criar uma nova equipe ou editar uma existente, definindo nome e selecionando membros (inspetores).

- Componentes/Inputs Principais: Campo para nome da equipe, lista de seleção múltipla/checkboxes para adicionar/remover inspetores. Utilizaria um EJS como edit-team.ejs.

- Conexão com Controllers: Renderizada por uma rota como /edit-team/:teamId?. O JS buscaria a lista de inspetores disponíveis via API /api/usuarios?role=inspetor (acionando listarUsuarios em usuariosController.js). Ao salvar, enviaria POST ou PUT para /api/equipes ou /api/equipes/:teamId (acionando criarEquipe ou atualizarEquipe em equipesController.js).



**4. Atribuição de Inspeções (Tela Inferida)**

- Função: Permite ao coordenador atribuir inspeções pendentes a equipes específicas.

- Componentes/Inputs Principais: Lista de inspeções não atribuídas, dropdown/lista para selecionar a equipe, botão para confirmar atribuição. Utilizaria um EJS como assign-inspection.ejs.

- Conexão com Controllers: Renderizada por uma rota como /assign-inspection. O JS buscaria inspeções pendentes (/api/inspecoes?status=pendente) e equipes (/api/equipes) via API. A atribuição enviaria uma requisição PUT para /api/inspecoes/:inspectionId/assign (ou similar), acionando uma função em inspecoesController.js para atualizar a inspeção com a equipe designada.



**5. Visualização de Relatórios (Tela Inferida)**

- Função: Permite ao coordenador visualizar e baixar relatórios gerados a partir das inspeções concluídas.

- Componentes/Inputs Principais: Lista/tabela de relatórios disponíveis com filtros (por data, inspeção, equipe), botões para visualizar/baixar cada relatório. Utilizaria um EJS como view-reports.ejs.

- Conexão com Controllers: Renderizada por uma rota como /reports. O JS chamaria a API /api/relatorios para listar os relatórios (acionando listarRelatorios em relatoriosController.js). A visualização/download acionaria um endpoint como /api/relatorios/:reportId/download que chamaria uma função em relatoriosController.js para gerar e enviar o arquivo.



**6. Detalhes da Inspeção (Visualização)**

- Função: Permite ao coordenador visualizar os detalhes completos de uma inspeção (sem permissão de edição, diferente do inspetor).

- Componentes/Inputs Principais: Similar ao Inspetor, mas com foco em visualização e acompanhamento. Utiliza views/pages/inspection-details.ejs.

- Conexão com Controllers: Idêntica à do Inspetor para buscar dados via API (inspecoesController.js, ambientesController.js, etc.).



**7. Detalhes do Ambiente (Visualização)**

- Função: Permite ao coordenador visualizar os detalhes de um ambiente e suas ocorrências (sem permissão de edição).

- Componentes/Inputs Principais: Similar ao Inspetor, mas apenas para visualização. Utiliza views/pages/ambient-details.ejs.

- Conexão com Controllers: Idêntica à do Inspetor para buscar dados via API (ambientesController.js, ocorrenciasController.js).



**8. Perfil do Usuário**

- Função: Exibe as informações do coordenador logado e permite a edição de dados básicos.

- Componentes/Inputs Principais: Similar ao Inspetor. Utiliza views/pages/profile.ejs.

- Conexão com Controllers: Idêntica à do Inspetor (usuariosController.js).



#### Administrador

**1. Tela Inicial / Dashboard do Administrador**

- Função: Apresenta uma visão geral do sistema, como número de usuários, inspeções totais, e acesso rápido a funcionalidades administrativas.

- Componentes/Inputs Principais: Cards com estatísticas chave, atalhos para gerenciamento de usuários e configurações. Utiliza views/pages/home.ejs (renderizado com dados específicos do administrador).

- Conexão com Controllers: A rota /home renderiza a view. O JS busca dados agregados via API (ex: /api/stats/geral, /api/usuarios/count). Essas rotas acionam funções em controllers como usuariosController.js ou um statsController.js dedicado.



**2. Dashboard Geral (Tela Inferida)**

- Função: Painel com gráficos e métricas detalhadas sobre o uso da plataforma, desempenho das inspeções, atividade dos usuários, etc.

- Componentes/Inputs Principais: Gráficos interativos (barras, pizza, linha), tabelas com dados agregados, filtros por período. Utilizaria um EJS como admin-dashboard.ejs.

- Conexão com Controllers: Renderizada por uma rota como /admin/dashboard. O JS chamaria diversos endpoints da API para buscar dados para os gráficos (ex: /api/stats/inspecoes-por-mes, /api/stats/usuarios-ativos), acionando funções em vários controllers ou um statsController.js.



**3. Gerenciamento de Usuários (Tela Inferida)**

- Função: Permite ao administrador visualizar, criar, editar, ativar/desativar e excluir contas de usuário;(inspetores, coordenadores, outros administradores).

- Componentes/Inputs Principais: Tabela/lista de usuários com filtros e busca, botões para adicionar novo usuário, editar, ativar/desativar e excluir. Utilizaria um EJS como manage-users.ejs.

- Conexão com Controllers: Renderizada por uma rota como /admin/users. O JS chamaria a API /api/usuarios para listar todos os usuários (acionando listarUsuarios em usuariosController.js). Ações de CRUD (Create, Read, Update, Delete) enviariam requisições POST, PUT, DELETE para /api/usuarios ou /api/usuarios/:userId, acionando as funções correspondentes (criarUsuario, atualizarUsuario, deletarUsuario) em usuariosController.js.



**4. Cadastro/Edição de Usuário (Tela Inferida)**

- Função: Formulário para criar um novo usuário ou editar um existente, definindo nome, email, perfil (inspetor, coordenador, admin) e senha inicial (para criação).

- Componentes/Inputs Principais: Campos para nome, email, seleção de perfil, campo de senha (apenas na criação ou reset). Utilizaria um EJS como edit-user.ejs.

- Conexão com Controllers: Renderizada por uma rota como /admin/users/edit/:userId?. Ao salvar, enviaria POST ou PUT para /api/usuarios ou /api/usuarios/:userId (acionando criarUsuario ou atualizarUsuario em usuariosController.js).



**5. Gerenciamento de Configurações (Tela Inferida)**

- Função: Permite ao administrador configurar parâmetros gerais do sistema, como catálogo de patologias, tipos de ambiente, configurações de notificação, etc.

- Componentes/Inputs Principais: Seções diferentes para cada tipo de configuração, com formulários, tabelas e botões para gerenciar os itens (adicionar, editar, excluir). Utilizaria um EJS como settings.ejs.

- Conexão com Controllers: Renderizada por uma rota como /admin/settings. Cada seção interagiria com endpoints específicos da API (ex: /api/config/patologias, /api/config/tipos-ambiente), acionando funções em controllers dedicados (ex: configController.js - a ser criado) para gerenciar essas configurações no banco de dados.



**6. Perfil do Usuário**

- Função: Exibe as informações do administrador logado e permite a edição de dados básicos.

- Componentes/Inputs Principais: Similar aos outros perfis. Utiliza views/pages/profile.ejs.

- Conexão com Controllers: Idêntica aos outros perfis (usuariosController.js).

---

### Requests e Responses

- O cliente faz reque
sts HTTP genéricos para endpoin
ts.
- O servidor responde com status e dados (JSON), não só HTML.
- O fluxo é válido tanto para o front-end quanto para qualquer consumidor da Webapi.

#### Exemplo Genérico
#### Request

```javascript

GET /api/fotos/123
Authorization: Bearer <token>

```
#### Response

```javascript
{
    "id": 123,
    "url": "https://...",
    "id_ambiente": 45,
    "created_at": "2024-06-13T12:00:00z"
}

```



## 3.2 - Wireframe da Solução Proposta

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 9</strong> – Wireframe de baixa fidelidade com navegabilidade</p>
    <img src="../assets/Wireframe_baixaFidelidade.jpg" style="max-width: 100%; height: auto; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---

### Autenticação:
- Login de usuário
- Cadastro de novos usuários

### Gestão Predial:
- Cadastro de edifícios
- Lançamento de novas inspeções
- Registro de ocorrências

### Visualização:
- Listagem de inspeções realizadas
- Consulta de edifícios cadastrados

---
### Fluxo de Navegação Principal:

```mermaid
graph TD
    A[Tela de Login] -->|Credenciais válidas| B[Tela Principal]
    A --> C[Tela de Cadastro]
    B --> D[Nova Inspeção]
    D --> E[Novo Edifício]
    B --> F[Inspeções]
    F --> G[Ocorrências]
    G --> H[Ocorrência Detalhada]
    B --> I[Edifícios Cadastrados]
```
---
### Detalhamento dos Fluxos

1. **Fluxo de Autenticação:**
   - Login → Dashboard Principal
   - Login → Cadastro (para novos usuários)

2. **Fluxo de Inspeção:**
   - Dashboard → Nova Inspeção
     - → Cadastro de Novo Edifício (se necessário)
   - Dashboard → Histórico de Inspeções → Detalhes de Ocorrências

3. **Fluxo de Consulta:**
   - Dashboard → Edifícios Cadastrados
---
### User Stories Atendidas

| Código | Descrição Resumida | Telas Relacionadas |
|--------|-------------------|-------------------|
| US01 | Autenticação de usuário | Tela de Login |
| US02 | Criação de nova inspeção | Tela Principal → Nova Inspeção |
| US03 | Registro de ocorrências | Ocorrências → Ocorrência Detalhada |
| US04 | Cadastro de edifícios | Novo Edifício |
| US05 | Consulta de inspeções | Tela de Inspeções Realizadas |
| US06 | Relatório de inspeções | Tela de Inspeções Realizadas |
| US07 | Cadastro de usuários | Tela de Cadastro de Usuário |
| US08 | Visualização do dashboard | Tela Principal |
| US09 | Consulta de edifícios | Tela de Edifícios Cadastrados |
---
### Mapeamento Detalhado

#### US01 - Autenticação

**Telas:**
- Login (entrada principal)
- Cadastro (para novos usuários)

**Funcionalidades:**
- Validação de credenciais
- Redirecionamento seguro

#### US02-US04 - Gestão de Inspeções

**Fluxo Completo:**
1. Acesso ao formulário de nova inspeção
2. Seleção/cadastro de edifício
3. Registro de detalhes da inspeção
4. Associação de ocorrências (US03)

#### US05-US06 - Consultas

**Dados Disponíveis:**
- Listagem cronológica de inspeções
- Filtros por:
  - Período
  - Edifício
  - Status

#### US07 - Administração

**Processo:**
1. Formulário de cadastro
2. Validação de dados
3. Confirmação por e-mail (opcional)

#### US08-US09 - Visualizações

**Componentes do Dashboard:**
- Resumo estatístico
- Acesso rápido às principais funcionalidades
- Lista condensada de edifícios recentes                    |




---

## 3.3. Guia de estilos

&emsp;O guia de estilos é uma ferramenta essencial para assegurar a uniformidade e a clareza na produção textual, especialmente em contextos acadêmicos, científicos e institucionais. Ele estabelece diretrizes quanto à formatação, estrutura do texto, uso de citações e referências, promovendo uma comunicação mais eficiente e padronizada. Além disso, contribui para a credibilidade do conteúdo, facilita a leitura e compreensão por parte do público-alvo e garante que diferentes autores sigam um padrão comum. Essa padronização é particularmente importante quando se trabalha em equipe ou quando os textos precisam seguir normas reconhecidas, como o estilo APA, ABNT ou Chicago. Como destaca Day (2005), o uso de um guia de estilos não apenas melhora a apresentação do trabalho, mas também fortalece a integridade e a confiabilidade das informações apresentadas.

---
### 3.3.1 ***Cores***


<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 10</strong> – Cores usadas no projeto</p>
    <img src="../assets/cores.png" style="max-width: 100%; height: auto; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

### Paleta de Cores da Aplicação IPT
**Azul Primário** 
Hexadecimal: #005493

Aplicações:

- Cor principal da identidade visual.

- Botões de destaque (como "ENTRAR" e "CRIAR OCORRÊNCIA").

- Cabeçalho e barra de navegação.

---

**Azul Secundário**
Hexadecimal: #00B5E2

Aplicações:

- Elementos de apoio visual e destaques secundários.

- Ícones ou botões complementares.

--- 

### Paleta Neutra
**Branco**
Hexadecimal: #FFFFFF

Aplicações:

- Fundo principal das telas.

- Cor base para caixas de entrada, textos escuros e ícones.
--- 

**Cinza Claro**
Hexadecimal: #F5F5F5

Aplicações:

- Fundos de campos de entrada.

- Separadores e contornos sutis.
---
**Cinza Médio**
Hexadecimal: #CCCCCC



Aplicações:

- Bordas e divisores.

- Texto secundário ou desabilitado.
---

**Preto**
Hexadecimal: #000000

Aplicações:

- Texto principal e títulos.

- Ícones importantes e menus.

---

 ### Paleta de Status e Ação
**Amarelo Alerta**
Hexadecimal: #FFB246

Aplicações:

- Indicadores de atenção ou avisos.

- Status "Em andamento" ou "Pendente".
---

**Verde Concluído**
Hexadecimal: #56A757

Aplicações:

- Status "Concluído" ou positivo.

- Sinalizações de sucesso ou finalização.
--- 

**Vermelho de Alerta**
Hexadecimal: #E53935

Aplicações:

- Indicação de erro, falha ou status "Incompleto".

- Alertas críticos.
---

**Azul Neutral**
Hexadecimal: #4C718B

Aplicações:

- Elementos secundários ou informativos.

- Fundos discretos ou ícones de apoio.
---

### 3.3.2 Tipografia 


&emsp; A tipografia desempenha um papel essencial na comunicação visual do projeto, influenciando diretamente a experiência do usuário. Optamos por fontes modernas e elegantes, que aliam legibilidade e personalidade à interface. A hierarquia tipográfica é cuidadosamente estruturada, diferenciando títulos, subtítulos e textos corridos por meio de variações de tamanho, peso e espaçamento. Essa abordagem não só reforça a identidade visual, como também facilita a leitura e a navegação, tornando a interação mais intuitiva, acessível e agradável para todos os públicos.


<div align="center">
  <sub>FIGURA 11 - Tipografia </sub><br>
  <img src= "../assets/tipografia.png" width="100%" 
  alt="Tipografia"><br>
  <sup>Fonte: Material produzido pelo grupo, 2025</sup>
</div>

---

### 3.3.3 Iconografia e imagens 

&emsp; Ícones são elementos visuais essenciais para a comunicação rápida e intuitiva em interfaces digitais. Eles facilitam a navegação, representam ações, objetos e estados, e contribuem para a identidade visual da aplicação. Documentar os ícones garante padronização e clareza para toda a equipe de design e desenvolvimento, além de melhorar a experiência do usuário.

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 12</strong> – Ícones</p>
    <img src="../assets/iconografia.png" style="max-width: 100%; height: auto; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


## 3.4 Protótipo de alta fidelidade 
&emsp; Um protótipo de alta fidelidade representa de forma realista o design final de um produto digital.
Ele inclui elementos visuais detalhados, tipografia, cores e interações funcionais.
É usado para testes com usuários, validação de ideias e apresentação para stakeholders.
(Figma, 2024).
&emsp; Com isso, utilizou-se de ferramentas como o Figma e de materiais mais complexos como o guia de estilos para produzir uma interface bem fiel daquilo que virá a ser a aplicação. Nesse sentido, foi desenvolvido um protótipo para o projeto em parceria com o Instituto de Pesquisas Tecnológicas (IPT), que simula as principais funcionalidades da aplicação, respeitando os critérios de alta fidelidade. A seguir, as páginas de maior relevância serão apresentadas e explicadas. O protótipo completo está disponível no seguinte acesso:

[ Clique aqui para acessar o figma do protótipo de alta fidelidade ](https://www.figma.com/design/W3xYJ74stos7MqISMewOTQ/Defined?node-id=28-2&t=C8tyx4G8qoa9Bnaw-1)

---
### Tela de login
&emsp; A página inicial do sistema funciona também como Tela de Login. Nela, os usuários realizam a autenticação utilizando e-mail corporativo e senha. Não há uma tela separada de cadastro, pois o sistema permite o registro automático com validação do domínio de e-mail. Além disso, existe um botão para recuperação de senha, com envio automático para o e-mail do usuário. Esta tela está diretamente relacionada à US01, ao garantir controle de acesso seguro e diferenciado por tipo de usuário, protegendo os dados e regulando permissões conforme o perfil cadastrado.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 13</strong> – Tela de login do inspetor</p>
    <img src="../assets/ux/telaLogin.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>



---
### Home
&emsp; A Home funciona como o painel principal do usuário logado. Exibe a lista de inspeções com seus respectivos status (em andamento, finalizadas, pendentes), podendo ser filtradas por situação. O usuário pode iniciar uma nova inspeção ou consultar detalhes de inspeções anteriores. Essa tela está conectada à US08 e US10, por permitir o acesso ao histórico e facilitar a retomada ou reuso de inspeções já realizadas, além de evidenciar pendências.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 14</strong> – Tela de home do inspetor</p>
    <img src="../assets/ux/telaHome.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


---
### Inspeções
&emsp; A tela de Inspeções apresenta as informações principais da  selecionada, como local, data, status e inspetor responsável. Atende às US02, US04 e US09, permitindo a continuidade do processo de inspeção em várias etapas e o acompanhamento detalhado das informações registradas.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 15</strong> – Tela de inspeções do inspetor</p>
    <img src="../assets/ux/telaDetalhesInspecao.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


---

### Ambientes
&emsp; Na tela de Ambientes, o usuário visualiza e edita dados referentes a um ambiente específico inspecionado — como nome, imagens e observações. Também é possível adicionar ocorrências específicas daquele local. Está alinhada às US04, US05 e US09, pois permite registrar dados segmentados por ambiente, com inclusão de imagens e anotações detalhadas diretamente do campo.

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 16</strong> – Tela de ambientes do inspetor</p>
    <img src="../assets/ux/telaDetalhesAmbientes.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---


### Ocorrências
&emsp; A tela de Detalhes da Ocorrência mostra um problema detectado em um ambiente, pedidindo o preenchimento de: descrição, imagens, classificações e observações técnicas. Está fortemente relacionada às US02, US04, US05 e US09, permitindo a documentação de cada ocorrência de forma precisa e completa, garantindo rastreabilidade no relatório final.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 17</strong> – Tela de ocorrências do inspetor</p>
    <img src="../assets/ux/telaAdicionarOcorrencia.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


---
### Perfil do usuário
&emsp; A tela de perfil exibe informações do inspetor, como nome, e-mail, cargo e dados da empresa, além de permitir edição de informações pessoais. É também por meio desta tela que o usuário pode realizar logout. Essa tela cumpre diretamente a US01, ao permitir o gerenciamento de perfis e a modulação de permissões conforme a função do usuário.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 18</strong> – Tela perfil do inspetor</p>
    <img src="../assets/ux/telaPerfil.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


---

### Histórico de relatórios
&emsp; A Lista de Relatórios reúne todos os relatórios já gerados. Cada item pode ser visualizado e exportado. O usuário também pode gerar novos relatórios a partir de inspeções finalizadas. Essa funcionalidade está de acordo com as US03, US06, US07 e US10, permitindo um gerenciamento eficiente dos documentos técnicos e facilitando a entrega de resultados profissionais.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 19</strong> – Tela de histórico de relatórios do inspetor</p>
    <img src="../assets/ux/telaRelatorios.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


---
### Adicinar ocorrência
&emsp; A tela Adicionar Ocorrência permite registrar uma nova anomalia identificada durante a inspeção, com campos para descrição, categoria, nível de severidade, imagens e observações. O processo é intuitivo e otimizado para uso em campo, inclusive em dispositivos móveis. Relaciona-se diretamente às US02, US04, US05 e US09, pois facilita o registro de informações completas e organizadas durante a vistoria.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 20</strong> – Tela de adicionar ocorrência do inspetor</p>
    <img src="../assets/ux/telaAdicionarOcorrencia.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


---
### Adicionar ambiente
&emsp; Na tela Adicionar Ambiente, o inspetor pode criar um título, selecionar um tipo de sistema (e até mesmo adicionar um tipo de sistema que não esteja disponivelmpara seleção), também é possível incluir fotos e descrições iniciais. Essa funcionalidade está conectada à US04 e US05, promovendo uma divisão lógica e clara da inspeção por partes do imóvel, o que facilita tanto o preenchimento quanto a posterior geração de relatórios.
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 21</strong> – Tela de adicionar ocorrência do inspetor</p>
    <img src="../assets/ux/telaDetalhesAmbientes.png" style="max-width: 100%; height: 450; margin: 0.5em 0;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>


---

### Especificação de Telas do Sistema



#### **Tela 1 – Login**

**Descrição:**  
A tela de login é onde ocorre a entrada do usuário no sistema. Deve ser simples, intuitiva e garantir a segurança do acesso.

**Elementos:**
- **Logo:** Localizada no topo, reforçando a identidade visual.
- **Título:** Nome do site.
- **Campo de Usuário:** Para inserir o e-mail.
- **Campo de Senha:** Para digitação da senha.
- **Botão "Entrar":** Ação principal para autenticação.
- **"Esqueci minha senha":** Acesso ao fluxo de recuperação de senha.

**Fluxo do Usuário:**
1. Usuário insere e-mail e senha.
2. Clica em "Entrar".
3. Se as informações estiverem corretas, é direcionado à tela principal.
4. Se houver erro, um feedback é exibido.

**Regras de Negócio:**
- Campos obrigatórios não podem estar vazios para habilitar o botão "Entrar".
- Após 5 tentativas de login inválidas, bloquear temporariamente o acesso.

---

#### **Tela 2 – Tela Principal**

**Descrição Geral:**  
Tela inicial após login, concentra as principais funcionalidades e acessos rápidos.

**Elementos:**
- **Barra Superior:** Ícone do usuário (perfil), logo do site.
- **Campo de Busca/Filtro:** Pesquisar inspeções já feitas ou em aberto.
- **Atalhos/Categorias:** Três blocos horizontais para acesso rápido (ex: conteúdos, ambientes, funcionalidades).
- **Destaques/Notificações:** Área para exibir todas as inspeções.
- **Lista de Cartões:** Cada cartão com título, breve descrição, data, ícones de usuários relacionados e botão "+".

**Fluxo do Usuário:**
- Acessar atalhos para diferentes áreas do sistema.
- Utilizar campo de busca para localizar informações.
- Visualizar notificações ou destaques recentes.
- Interagir com cartões para ver detalhes, editar ou adicionar itens.

**Regras de Negócio:**
- Cartões devem ser ordenados por relevância ou data.
- Notificações com prioridade alta aparecem no topo.

---

#### **Tela 3 – Tela de Ambientes**

**Descrição Geral:**  
Permite ao usuário visualizar, gerenciar e adicionar ambientes.

**Elementos:**
- **Barra Superior:** Botão de voltar.
- **Lista de Ambientes:** Cada item exibe imagem, nome, botão de alternância (ativo/inativo) e botão "+".
- **Botão Flutuante de Ação:** Adicionar novo ambiente.

**Fluxo do Usuário:**
- Visualizar todos os ambientes cadastrados.
- Alternar status de ambientes (ativo/inativo).
- Clicar em "+" para editar ou ver detalhes.
- Usar botão flutuante para criar um novo ambiente.

**Regras de Negócio:**
- Não permitir nomes duplicados de ambientes.
- Ao adicionar novo ambiente, campos obrigatórios: nome, imagem, descrição.

---

#### **Tela 4 – Ocorrências**

**Descrição:**  
Lista de ocorrências registradas, permitindo visualização rápida, acesso a detalhes ou edição.

**Elementos:**
- **Botão de voltar (seta)**
- **Barra de navegação/categorias**
- **Cartões de ocorrência:** Imagem, breve descrição, data, botão de alternância (ativo/inativo).

**Fluxo do Usuário:**
- Visualizar todas as ocorrências cadastradas.
- Alternar status (ativo/inativo).
- Clicar no botão flutuante para adicionar nova ocorrência.

**Regras de Negócio:**
- Não permitir ocorrências duplicadas.
- Campos obrigatórios ao adicionar: imagem, descrição, data.

---

#### **Tela 5- Cadastro/Edição de Ambiente**

**Descrição:**  
Tela/formulário onde o usuário pode adicionar um novo ambiente ou editar um ambiente existente, preenchendo os campos obrigatórios.

**Elementos:**
- **Botão de voltar (seta)**
- **Campo para nome do ambiente**
- **Campo para upload/seleção de imagem**
- **Campo de descrição do ambiente**
- **Botão "Salvar" ou "Confirmar"**
- **Validação de campos obrigatórios**

**Fluxo do Usuário:**
1. Preenche os campos obrigatórios
2. Salva o ambiente
3. Em caso de edição, os campos já vêm preenchidos

**Regras de Negócio:**
- Não permitir nomes duplicados
- Todos os campos obrigatórios devem ser preenchidos para habilitar o botão de salvar

---
#### **Tela 6 – Ocorrências (Visualização Detalhada)**

**Descrição:**  
Detalhamento de uma ocorrência específica, com informações completas, imagens e navegação entre ocorrências.

**Elementos:**
- **Botão de voltar (seta)**
- **Barra de navegação/categorias**
- **Imagem principal da ocorrência**
- **Área de destaque para informações resumidas**
- **Setas laterais para navegar entre ocorrências**
- **Seção de detalhes com campos informativos e botões de ação**
- **Campo de descrição detalhada**

**Fluxo do Usuário:**
- Visualizar detalhes completos da ocorrência selecionada.
- Navegar entre ocorrências usando as setas.
- Acessar campos de edição ou informações adicionais.

**Regras de Negócio:**
- Apenas usuários autorizados podem editar informações.
- Campos obrigatórios para edição: descrição, status, data.

---

#### **Tela 7 – Relatórios (Detalhe)**

**Descrição:**  
Detalhe de um relatório específico, exibindo informações completas e permitindo adicionar itens.

**Elementos:**
- **Botão de voltar (seta)**
- **Imagem principal do relatório**
- **Área para informações detalhadas**
- **Botão "+" para adicionar itens**
- **Lista de itens com campos editáveis e botões de ação**

**Fluxo do Usuário:**
- Visualizar detalhes do relatório.
- Adicionar ou editar itens do relatório.

**Regras de Negócio:**
- Apenas usuários autorizados podem editar relatórios.
- Campos obrigatórios: nome, imagem, descrição.

---

#### **Tela 8 – Relatórios**

**Descrição:**  
Visualização completa de um relatório, exibindo todas as informações, imagens e descrições detalhadas.

**Elementos:**
- **Botão de voltar (seta)**
- **Imagem principal do relatório**
- **Área central de destaque (gráficos ou imagem ampliada)**
- **Setas laterais para navegar entre relatórios**
- **Campo de texto descritivo detalhado**
- **Lista de itens do relatório, cada um com botão "+"**

**Fluxo do Usuário:**
- Visualizar o relatório em detalhes.
- Navegar entre relatórios usando as setas.

**Regras de Negócio:**
- Apenas visualização; edição restrita a usuários autorizados.
- Todos os campos obrigatórios devem estar preenchidos para exibição completa.

---

#### **Tela 9 – Relatórios (Visualização Detalhada)**

**Descrição:**  
Visualização detalhada do relatório, com campos de texto extensos e imagens.

**Elementos:**
- **Botão de voltar (seta)**
- **Imagem principal do relatório (superior)**
- **Campo de texto descritivo detalhado (abaixo da imagem)**
- **Imagem adicional ilustrativa (meio da tela)**
- **Campo de texto extenso para informações completas**

**Fluxo do Usuário:**
- Visualizar o relatório em detalhes.
- Retornar à tela anterior pelo botão de voltar.

**Regras de Negócio:**
- Apenas visualização; edição restrita a usuários autorizados.
- Todos os campos obrigatórios devem estar preenchidos para exibição completa.


---
## Especificação de Telas do Sistema

### **Tela 1 – Login**

**Descrição:**  
A tela de login é onde ocorre a entrada do usuário no sistema. Deve ser simples, intuitiva e garantir a segurança do acesso.

**Elementos:**
- **Logo:** Localizada no topo, reforçando a identidade visual.
- **Título:** Nome do site.
- **Campo de Usuário:** Para inserir o e-mail.
- **Campo de Senha:** Para digitação da senha.
- **Botão "Entrar":** Ação principal para autenticação.
- **"Esqueci minha senha":** Acesso ao fluxo de recuperação de senha.

**Fluxo do Usuário:**
1. Usuário insere e-mail e senha.
2. Clica em "Entrar".
3. Se as informações estiverem corretas, é direcionado à tela principal.
4. Se houver erro, um feedback é exibido.

**Regras de Negócio:**
- Campos obrigatórios não podem estar vazios para habilitar o botão "Entrar".
- Após 5 tentativas de login inválidas, bloquear temporariamente o acesso.

---

### **Tela 2 – Tela Principal**

**Descrição Geral:**  
Tela inicial após login, concentra as principais funcionalidades e acessos rápidos.

**Elementos:**
- **Barra Superior:** Ícone do usuário (perfil), logo do site.
- **Campo de Busca/Filtro:** Pesquisar inspeções já feitas ou em aberto.
- **Atalhos/Categorias:** Três blocos horizontais para acesso rápido (ex: conteúdos, ambientes, funcionalidades).
- **Destaques/Notificações:** Área para exibir todas as inspeções.
- **Lista de Cartões:** Cada cartão com título, breve descrição, data, ícones de usuários relacionados e botão "+".

**Fluxo do Usuário:**
- Acessar atalhos para diferentes áreas do sistema.
- Utilizar campo de busca para localizar informações.
- Visualizar notificações ou destaques recentes.
- Interagir com cartões para ver detalhes, editar ou adicionar itens.

**Regras de Negócio:**
- Cartões devem ser ordenados por relevância ou data.
- Notificações com prioridade alta aparecem no topo.

---

### **Tela 3 – Tela de Ambientes**

**Descrição Geral:**  
Permite ao usuário visualizar, gerenciar e adicionar ambientes.

**Elementos:**
- **Barra Superior:** Botão de voltar.
- **Lista de Ambientes:** Cada item exibe imagem, nome, botão de alternância (ativo/inativo) e botão "+".
- **Botão Flutuante de Ação:** Adicionar novo ambiente.

**Fluxo do Usuário:**
- Visualizar todos os ambientes cadastrados.
- Alternar status de ambientes (ativo/inativo).
- Clicar em "+" para editar ou ver detalhes.
- Usar botão flutuante para criar um novo ambiente.

**Regras de Negócio:**
- Não permitir nomes duplicados de ambientes.
- Ao adicionar novo ambiente, campos obrigatórios: nome, imagem, descrição.

---

### **Tela 4 – Ocorrências**

**Descrição:**  
Lista de ocorrências registradas, permitindo visualização rápida, acesso a detalhes ou edição.

**Elementos:**
- **Botão de voltar (seta)**
- **Barra de navegação/categorias**
- **Cartões de ocorrência:** Imagem, breve descrição, data, botão de alternância (ativo/inativo).

**Fluxo do Usuário:**
- Visualizar todas as ocorrências cadastradas.
- Alternar status (ativo/inativo).
- Clicar no botão flutuante para adicionar nova ocorrência.

**Regras de Negócio:**
- Não permitir ocorrências duplicadas.
- Campos obrigatórios ao adicionar: imagem, descrição, data.

---

### **Tela 5- Cadastro/Edição de Ambiente**

**Descrição:**  
Tela/formulário onde o usuário pode adicionar um novo ambiente ou editar um ambiente existente, preenchendo os campos obrigatórios.

**Elementos:**
- **Botão de voltar (seta)**
- **Campo para nome do ambiente**
- **Campo para upload/seleção de imagem**
- **Campo de descrição do ambiente**
- **Botão "Salvar" ou "Confirmar"**
- **Validação de campos obrigatórios**

**Fluxo do Usuário:**
1. Preenche os campos obrigatórios
2. Salva o ambiente
3. Em caso de edição, os campos já vêm preenchidos

**Regras de Negócio:**
- Não permitir nomes duplicados
- Todos os campos obrigatórios devem ser preenchidos para habilitar o botão de salvar

---
### **Tela 6 – Ocorrências (Visualização Detalhada)**

**Descrição:**  
Detalhamento de uma ocorrência específica, com informações completas, imagens e navegação entre ocorrências.

**Elementos:**
- **Botão de voltar (seta)**
- **Barra de navegação/categorias**
- **Imagem principal da ocorrência**
- **Área de destaque para informações resumidas**
- **Setas laterais para navegar entre ocorrências**
- **Seção de detalhes com campos informativos e botões de ação**
- **Campo de descrição detalhada**

**Fluxo do Usuário:**
- Visualizar detalhes completos da ocorrência selecionada.
- Navegar entre ocorrências usando as setas.
- Acessar campos de edição ou informações adicionais.

**Regras de Negócio:**
- Apenas usuários autorizados podem editar informações.
- Campos obrigatórios para edição: descrição, status, data.

---

### **Tela 7 – Relatórios (Detalhe)**

**Descrição:**  
Detalhe de um relatório específico, exibindo informações completas e permitindo adicionar itens.

**Elementos:**
- **Botão de voltar (seta)**
- **Imagem principal do relatório**
- **Área para informações detalhadas**
- **Botão "+" para adicionar itens**
- **Lista de itens com campos editáveis e botões de ação**

**Fluxo do Usuário:**
- Visualizar detalhes do relatório.
- Adicionar ou editar itens do relatório.

**Regras de Negócio:**
- Apenas usuários autorizados podem editar relatórios.
- Campos obrigatórios: nome, imagem, descrição.

---

### **Tela 8 – Relatórios**

**Descrição:**  
Visualização completa de um relatório, exibindo todas as informações, imagens e descrições detalhadas.

**Elementos:**
- **Botão de voltar (seta)**
- **Imagem principal do relatório**
- **Área central de destaque (gráficos ou imagem ampliada)**
- **Setas laterais para navegar entre relatórios**
- **Campo de texto descritivo detalhado**
- **Lista de itens do relatório, cada um com botão "+"**

**Fluxo do Usuário:**
- Visualizar o relatório em detalhes.
- Navegar entre relatórios usando as setas.

**Regras de Negócio:**
- Apenas visualização; edição restrita a usuários autorizados.
- Todos os campos obrigatórios devem estar preenchidos para exibição completa.

---

### **Tela 9 – Relatórios (Visualização Detalhada)**

**Descrição:**  
Visualização detalhada do relatório, com campos de texto extensos e imagens.

**Elementos:**
- **Botão de voltar (seta)**
- **Imagem principal do relatório (superior)**
- **Campo de texto descritivo detalhado (abaixo da imagem)**
- **Imagem adicional ilustrativa (meio da tela)**
- **Campo de texto extenso para informações completas**

**Fluxo do Usuário:**
- Visualizar o relatório em detalhes.
- Retornar à tela anterior pelo botão de voltar.

**Regras de Negócio:**
- Apenas visualização; edição restrita a usuários autorizados.
- Todos os campos obrigatórios devem estar preenchidos para exibição completa.


## 3.5. Modelagem do banco de dados

### 3.5.1. Modelo conceitual

&emsp;O modelo conceitual é uma das etapas iniciais da modelagem de banco de dados. Ele tem como principal objetivo representar, de forma abstrata e independente da tecnologia, os principais elementos de informação do sistema e seus relacionamentos. Utiliza-se frequentemente o diagrama entidade-relacionamento (ER), que permite visualizar as entidades, atributos e os vínculos entre elas.

&emsp;Este modelo facilita o entendimento global do banco de dados, sendo fundamental para validar os requisitos com os stakeholders antes de avançar para a estrutura lógica (modelo relacional). A clareza proporcionada pelo modelo conceitual é essencial para garantir que o banco de dados reflita corretamente a realidade que ele busca representar.

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 22</strong> – Modelo Conceitual de Banco de Dados</p>
    <img src="../assets/programacao/modelo-conceitual.png" style="max-width: 800px;">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
### 3.5.2. Modelo relacional


&emsp;O modelo de banco de dados relacional é fundamental no desenvolvimento de sistemas de informação, pois permite representar com clareza as entidades e seus relacionamentos, facilitando a integridade e consistência dos dados. Sua estrutura baseada em tabelas interligadas reduz a redundância, evita inconsistências e torna o acesso às informações mais eficiente (Elmasri & Navathe, 2011).
&emsp;A criação do modelo relacional durante a etapa de modelagem conceitual e lógica é essencial para antecipar problemas, definir chaves primárias e estrangeiras corretamente, aplicar as regras de negócio do sistema e permitir a normalização dos dados (Silberschatz, Korth & Sudarshan, 2013). Além disso, o modelo facilita a comunicação entre desenvolvedores, analistas e stakeholders, garantindo que todos compartilhem o mesmo entendimento sobre a estrutura dos dados.
&emsp;Portanto, o modelo relacional não é apenas uma etapa técnica, mas também estratégica no ciclo de vida de um sistema, sendo indispensável para sua escalabilidade, manutenção e evolução.


<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 23</strong> – Modelo Relacional de Banco de Dados</p>
    <img src="../assets/programacao/modelo-banco.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

##  Descrição do Banco de dados
---

###  Tabela: `cargos`
Define os cargos possíveis para usuários dentro das equipes.

| Coluna | Tipo      | Chave | Descrição                              |
|--------|-----------|-------|----------------------------------------|
| nome   | VARCHAR   | PK    | Nome do cargo (ex: COORDENADOR, MEMBRO) |

---

###  Tabela: `usuarios`
Armazena os dados dos usuários do sistema.

| Coluna | Tipo          | Chave | Descrição                |
|--------|---------------|-------|--------------------------|
| id     | SERIAL        | PK    | Identificador único      |
| nome   | VARCHAR(100)  |       | Nome do usuário          |
| email  | VARCHAR(100)  |       | E-mail único do usuário  |
| senha  | VARCHAR(100)  |       | Senha de acesso          |
|criado_em| TIMESTAMP|   |   Data de criação|
|permissao|VARCHAR(30)  |     |Permissão do úsuario|

---

###  Tabela: `equipes`
Armazena as equipes que realizam inspeções.

| Coluna | Tipo          | Chave | Descrição                |
|--------|---------------|-------|--------------------------|
| id     | SERIAL        | PK    | Identificador único      |
| nome   | VARCHAR(100)  |       | Nome da equipe           |

---

###  Tabela: `usuarios_cargos_equipes`
Relaciona usuários, cargos e equipes.

| Coluna      | Tipo      | Chave | Descrição                          |
|-------------|-----------|-------|------------------------------------|
| id          | SERIAL    | PK    | Identificador da relação          |
| id_cargo    | VARCHAR   | FK    | Referência à tabela `cargos`      |
| id_usuario  | INTEGER   | FK    | Referência à tabela `usuarios`    |
| id_equipe   | INTEGER   | FK    | Referência à tabela `equipes`     |

---

###  Tabela: `tipo_edificacao`
Lista os tipos de edificações.

| Coluna | Tipo          | Chave | Descrição                      |
|--------|---------------|-------|--------------------------------|
| nome   | VARCHAR(100)  | PK    | Nome do tipo de edificação     |

---

###  Tabela: `enderecos`
Armazena os dados de localização.

| Coluna       | Tipo          | Chave | Descrição                |
|--------------|---------------|-------|--------------------------|
| id           | SERIAL        | PK    | Identificador do endereço|
| cep          | VARCHAR(8)    |       | Código postal            |
| numero       | VARCHAR(20)   |       | Número                   |
| complemento  | VARCHAR(50)   |       | Complemento              |
| referencia   | VARCHAR(100)  |       | Ponto de referência      |

---

###  Tabela: `inspecoes`
Representa uma inspeção predial.

| Coluna               | Tipo          | Chave | Descrição                              |
|----------------------|---------------|-------|----------------------------------------|
| id                   | SERIAL        | PK    | Identificador da inspeção              |
| nome                 | VARCHAR(100)  |       | Nome da inspeção                       |
| criado_em            | TIMESTAMP     |       | Data de criação                        |
| url_planta           | VARCHAR(200)  |       | URL da planta baixa                    |
| id_tipo_edificacao   | VARCHAR       | FK    | Tipo da edificação (ref. `tipo_edificacao`) |
| id_equipe            | INTEGER       | FK    | Equipe responsável                     |
| id_endereco          | INTEGER       | FK    | Endereço da inspeção                   |

---

###  Tabela: `status`
Status possíveis de uma inspeção.

| Coluna | Tipo          | Chave | Descrição            |
|--------|---------------|-------|----------------------|
| nome   | VARCHAR(20)   | PK    | Nome do status       |
| cor    | VARCHAR(20)   |       | Cor do status        |

---

###  Tabela: `historicos`
Histórico de mudanças de status de uma inspeção.

| Coluna        | Tipo          | Chave | Descrição                          |
|---------------|---------------|-------|------------------------------------|
| id            | SERIAL        | PK    | Identificador                      |
| status_id     | VARCHAR(20)   | FK    | Referência ao status               |
| id_inspecao   | INTEGER       | FK    | Referência à inspeção              |
| adicionado_em | TIMESTAMP     |       | Data da alteração                  |

---

###  Tabela: `ambientes`
Ambientes inspecionados dentro da edificação.

| Coluna         | Tipo          | Chave | Descrição                          |
|----------------|---------------|-------|------------------------------------|
| id             | SERIAL        | PK    | Identificador                      |
| titulo         | VARCHAR(100)  |       | Nome do ambiente                   |
| id_usuario     | INTEGER       | FK    | Usuário responsável                |
| id_inspecao    | INTEGER       | FK    | Inspeção associada                 |
| criado_em      | TIMESTAMP     |       | Data de criação                    |
| id_ambiente    | INTEGER       | FK    | Ambiente pai (caso seja subambiente) |
| observacoes    | VARCHAR(150)  |       | Observações gerais                 |

---

###  Tabela: `afazeres`
Tarefas padrão atribuídas a ambientes.

| Coluna | Tipo          | Chave | Descrição            |
|--------|---------------|-------|----------------------|
| id     | SERIAL        | PK    | Identificador        |
| titulo | VARCHAR(100)  |       | Título da tarefa     |

---

###  Tabela: `ambientes_afazeres`
Relaciona tarefas com ambientes.

| Coluna      | Tipo      | Chave | Descrição                          |
|-------------|-----------|-------|------------------------------------|
| id_afazer   | INTEGER   | FK    | Referência à tabela `afazeres`     |
| id_ambiente | INTEGER   | FK    | Referência à tabela `ambientes`    |
| valor       | BOOLEAN   |       | Indica se a tarefa foi realizada   |

---

###  Tabela: `tipo_sistema`
Sistemas construtivos possíveis (hidráulico, elétrico, etc).

| Coluna | Tipo          | Chave | Descrição            |
|--------|---------------|-------|----------------------|
| nome   | VARCHAR(30)   | PK    | Nome do sistema      |

---

###  Tabela: `tipos_patologia`
Tipos de patologias técnicas.

| Coluna           | Tipo          | Chave | Descrição                          |
|------------------|---------------|-------|------------------------------------|
| nome             | VARCHAR(100)  | PK    | Nome da patologia                 |
| id_tipo_sistema  | INTEGER       | FK    | Sistema ao qual está ligada       |

---

###  Tabela: `ocorrencias`
Ocorrências de problemas encontradas.

| Coluna             | Tipo          | Chave | Descrição                          |
|--------------------|---------------|-------|------------------------------------|
| id                 | SERIAL        | PK    | Identificador                      |
| titulo             | VARCHAR(100)  |       | Título da ocorrência               |
| id_inspecao        | INTEGER       | FK    | Inspeção associada                 |
| id_tipo_sistema    | INTEGER       | FK    | Sistema afetado                    |
| id_tipo_patologia  | VARCHAR(100)  | FK    | Tipo da patologia                  |
| descricao          | VARCHAR(500)  |       | Descrição detalhada do problema    |
| criado_em          | TIMESTAMP     |       | Data de criação                    |
| concluido_em       | TIMESTAMP     |       | Data de conclusão (se houver)      |

---

###  Tabela: `fotos`
Fotos associadas às ocorrências.

| Coluna          | Tipo          | Chave | Descrição                          |
|-----------------|---------------|-------|------------------------------------|
| id              | SERIAL        | PK    | Identificador                      |
| id_ocorrencia   | INTEGER       | FK    | Ocorrência relacionada             |
| url             | VARCHAR(100)  |       | Link da imagem                     |

---

###  Tabela: `relatorios`
Armazena os relatórios gerados das inspeções.

| Coluna      | Tipo          | Chave | Descrição                          |
|-------------|---------------|-------|------------------------------------|
| id          | INTEGER       | PK    | Identificador                      |
| url         | VARCHAR(200)  |       | Link para o relatório gerado       |
| id_local    | INTEGER       | FK    | Inspeção correspondente (`inspecoes`) |


---
###  Código SQL para criação das tabelas:
```sql
-- Tabela: cargos
CREATE TABLE cargos (
    nome VARCHAR CHECK (nome IN ('COORDENADOR', 'MEMBRO','ADMINISTRADOR')) PRIMARY KEY NOT NULL
);

-- Tabela: usuarios
CREATE TABLE usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    senha VARCHAR(100) NOT NULL,
    criado_em TIMESTAMP ,
    permissao VARCHAR(30) NOT NULL
);

-- Tabela: equipes
CREATE TABLE equipes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL
);

-- Tabela: usuarios_cargos_equipes
CREATE TABLE usuarios_cargos_equipes (
    id SERIAL PRIMARY KEY,
    id_cargo VARCHAR REFERENCES cargos(nome),
    id_usuario INTEGER REFERENCES usuarios(id),
    id_equipe INTEGER REFERENCES equipes(id)
);

-- Tabela: tipo_edificacao
CREATE TABLE tipo_edificacao (
    nome VARCHAR(100) PRIMARY KEY NOT NULL
);

-- Tabela: enderecos
CREATE TABLE enderecos (
    id SERIAL PRIMARY KEY,
    cep VARCHAR(8) NOT NULL,
    numero VARCHAR(20) NOT NULL,
    complemento VARCHAR(50),
    referencia VARCHAR(100)
);

-- Tabela: inspecoes
CREATE TABLE inspecoes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    criado_em TIMESTAMP,
    url_planta VARCHAR(200),
    id_tipo_edificacao VARCHAR(100) REFERENCES tipo_edificacao(nome),
    id_equipe INTEGER REFERENCES equipes(id),
    id_endereco INTEGER REFERENCES enderecos(id)
);

-- Tabela: status
CREATE TABLE status (
    nome VARCHAR(20) PRIMARY KEY NOT NULL,
    cor VARCHAR(20)
);

-- Tabela: historicos
CREATE TABLE historicos (
    id SERIAL PRIMARY KEY,
    status_id VARCHAR(20) REFERENCES status(nome),
    id_inspecao INTEGER REFERENCES inspecoes(id),
    adicionado_em TIMESTAMP
);

-- Tabela: ambientes
CREATE TABLE ambientes (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL,
    id_usuario INTEGER REFERENCES usuarios(id),
    id_inspecao INTEGER REFERENCES inspecoes(id),
    criado_em TIMESTAMP,
    id_ambiente INTEGER,
    observacoes VARCHAR(150)
);

-- Tabela: afazeres
CREATE TABLE afazeres (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL
);

-- Tabela: ambientes_afazeres
CREATE TABLE ambientes_afazeres (
    id_afazer INTEGER REFERENCES afazeres(id),
    id_ambiente INTEGER REFERENCES ambientes(id),
    valor BOOLEAN NOT NULL
);

-- Tabela: tipo_sistema
CREATE TABLE tipo_sistema (
    nome VARCHAR(30) PRIMARY KEY NOT NULL
);

-- Tabela: tipos_patologia
CREATE TABLE tipos_patologia (
    nome VARCHAR(100) PRIMARY KEY NOT NULL,
    id_tipo_sistema INTEGER REFERENCES tipo_sistema(nome)
);

-- Tabela: ocorrencias
CREATE TABLE ocorrencias (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL,
    id_inspecao INTEGER REFERENCES inspecoes(id),
    id_tipo_sistema INTEGER REFERENCES tipo_sistema(nome),
    id_tipo_patologia VARCHAR(100) REFERENCES tipos_patologia(nome),
    descricao VARCHAR(500),
    criado_em TIMESTAMP,
    concluido_em TIMESTAMP
);

-- Tabela: fotos
CREATE TABLE fotos (
    id SERIAL PRIMARY KEY,
    id_ocorrencia INTEGER REFERENCES ocorrencias(id),
    url VARCHAR(100) NOT NULL
);

-- Tabela: relatorios
CREATE TABLE relatorios (
    id INTEGER PRIMARY KEY,
    url VARCHAR(200) NOT NULL,
    id_local INTEGER REFERENCES inspecoes(id)
);


```

---


### 3.5.3. Consultas SQL e lógica proposicional

&emsp; A lógica proposicional trata da construção e avaliação de afirmações que podem ser classificadas como verdadeiras ou falsas, mas nunca ambas simultaneamente. No contexto de bancos de dados, as consultas SQL são instruções que solicitam informações específicas conforme as necessidades da aplicação. Cada uma dessas consultas pode ser associada a uma expressão lógica, na qual as proposições representam as condições que precisam ser atendidas para que determinados dados sejam retornados pelo sistema.

---
#### Consulta 1
Esta consulta SQL acessa a tabela `local` e retorna a coluna `id_equipe` para as linhas onde a condição `status = 'em andamento' OR data_conclusao IS NULL` é verdadeira. Trata-se de uma disjunção: se o status for 'em andamento', OU se a data de conclusão for nula (ou ambas), a condição é satisfeita.

| #1 | Seleção de locais com inspeções em andamento ou sem data de conclusão                                                                                                                                                                                                |
| -- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL** | `SELECT id_equipe FROM local WHERE status = 'em andamento' OR data_conclusao IS NULL;`                                                                                                                                                    |
| **Proposições lógicas** | $A$: `status = 'em andamento'`<br> $B$: `data_conclusao IS NULL`                                                                                                                                                                              |
| **Expressão lógica proposicional** | $A \lor B $                                                                                                                                                                                                                            |
| **Tabela Verdade** | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$(A \lor B)$</th> </tr> </thead> <tbody> <tr> <td>V</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> </tr> <tr> <td>F</td> <td>F</td> <td>F</td> </tr> </tbody> </table> |

---
#### Consulta 2
Esta instrução SQL `UPDATE` modifica a coluna `tipo_edificacao` para 'casa' na tabela `local`. A alteração é aplicada às linhas onde a condição `id_equipe IN (4, 9, 10)` é verdadeira. Esta condição `IN` é logicamente equivalente a uma disjunção: `id_equipe = 4 OR id_equipe = 9 OR id_equipe = 10`.

| #2 | Atualização do tipo de edificação para equipes específicas                                                                                                                                                                                                 |
| -- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL** | `UPDATE local SET tipo_edificacao = 'casa' WHERE id_equipe IN (4, 9, 10);`                                                                                                                                                           |
| **Proposições lógicas** | $A$: `id_equipe = 4`<br> $B$: `id_equipe = 9`<br> $C$: `id_equipe = 10`                                                                                                                                                              |
| **Expressão lógica proposicional** | $A \lor B \lor C$                                                                                                                                                                                                                |
| **Tabela Verdade** | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$A \lor B \lor C$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>V</td> <td>V</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>V</td></tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table> |

---
#### Consulta 3
Esta consulta SQL retorna dados das tabelas `inspecoes` e `usuarios`. A cláusula `INNER JOIN ... ON inspecoes.id_usuario_fk = usuarios.id` forma pares de linhas onde esta condição de junção é verdadeira. Destes pares, a cláusula `WHERE` filtra aqueles onde `inspecoes.data_inicio > '2024-01-01'` E `usuarios.nome LIKE '%Ana%'` são ambos verdadeiros. As colunas especificadas são então retornadas.

| #3 | Listagem de inspeções realizadas por usuárias chamadas Ana após 2024                                                                                                                                                                                                |
| -- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL** | `SELECT inspecoes.id AS id_inspecao, inspecoes.data_inicio, inspecoes.data_conclusao, usuarios.nome AS nome_usuario, usuarios.email FROM inspecoes INNER JOIN usuarios ON inspecoes.id_usuario_fk = usuarios.id WHERE inspecoes.data_inicio > '2024-01-01' AND usuarios.nome LIKE '%Ana%';` |
| **Proposições lógicas** | $A$: `inspecoes.id_usuario_fk = usuarios.id` (condição do JOIN)<br> $B$: `inspecoes.data_inicio > '2024-01-01'`<br> $C$: `usuarios.nome LIKE '%Ana%'`                                                                                             |
| **Expressão lógica proposicional** | $A \land B \land C$ (Para que uma linha combinada seja retornada, todas as três condições devem ser verdadeiras)                                                                                                                          |
| **Tabela Verdade** | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$A \land B \land C$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>V</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>F</td></tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table> |

---
#### Consulta 4
Esta consulta SQL seleciona todas as colunas (`*`) da tabela `endereco` para as linhas que satisfazem duas condições conectadas por `AND`: `id BETWEEN 1 AND 20` (o ID está na faixa de 1 a 20, inclusive) E `cep LIKE '05510%'` (o CEP começa com '05510').

| #4 | Consulta de endereços por faixa de ID e padrão de CEP                                                                                                                                                                                                                |
| -- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL** | `SELECT * from endereco WHERE id BETWEEN 1 AND 20 AND cep LIKE '05510%';`                                                                                                                                                              |
| **Proposições lógicas** | $A$: `id BETWEEN 1 AND 20`<br> $B$: `cep LIKE '05510%'`                                                                                                                                                                                        |
| **Expressão lógica proposicional** | $A \land B $                                                                                                                                                                                                                            |
| **Tabela Verdade** | <table class="truth-table"> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$(A \land B)$</th> </tr> </thead> <tbody> <tr> <td>V</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>F</td> </tr> </tbody> </table> |

---
#### Consulta 5
Esta instrução SQL `DELETE` remove linhas da tabela `inspecoes`. As linhas são removidas se ambas as condições na cláusula `WHERE` forem verdadeiras: `data_conclusao IS NOT NULL` (a inspeção tem uma data de conclusão) E `NOT (id_equipe_fk = 5)` (a inspeção não é da equipe 5).

| #5 | Exclusão de inspeções concluídas que não são da equipe 5                                                                                                                                                                                                                  |
| -- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL** | `DELETE FROM inspecoes WHERE data_conclusao IS NOT NULL AND NOT id_equipe_fk = 5;`                                                                                                                                                            |
| **Proposições lógicas** | $A$: `data_conclusao IS NOT NULL`<br> $B$: `id_equipe_fk = 5`                                                                                                                                                                                     |
| **Expressão lógica proposicional** | $A \land (\lnot B)$                                                                                                                                                                                                                          |
| **Tabela Verdade** | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$\lnot B$</th> <th>$A \land (\lnot B)$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>V</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>F</td> </tr> </tbody> </table> |

---

## 3.6. WebAPI e endpoints 

A documentação detalhada da API pode ser encontrada em **[Documentação da API](../documentos/outros/apidoc.md).**

---

# <a name="c4"></a>4. Desenvolvimento da Aplicação Web

## 4.1. Primeira versão da aplicação web 

A documentação detalhada da Primeira versão web pode ser encontrada em **[Documentação da Primeira Versão](../documentos/outros/primeiraversao.md).**

## 4.2. Segunda versão da aplicação web 

A documentação detalhada da Segunda versão web pode ser encontrada em **[Documentação da Segunda Versão](../documentos/outros/segundaversao.md).**


## 4.3. Versão final da aplicação web

A documentação detalhada da Versão Final web pode ser encontrada em **[Documentação da Versão Final](../documentos/outros/versaofinal.md).**


---

### Dificuldades Encontradas

Durante o processo, uma das principais dificuldades enfrentadas foi a integração correta entre os dados provenientes do banco de dados e a renderização dessas informações no frontend. Essa etapa exigiu diversos testes e ajustes na lógica de recuperação e exibição dos dados. Também foram encontradas barreiras relacionadas à manutenção da coerência entre os modelos de dados atualizados e os componentes visuais, o que exigiu refatorações cuidadosas para evitar efeitos colaterais no funcionamento geral da aplicação.

---

### Próximos Passos

Para os próximos ciclos de desenvolvimento, planeja-se:

- Realizar melhorias estruturais e funcionais no painel administrativo, ampliando suas funcionalidades e reorganizando os elementos de forma mais intuitiva;
- Corrigir limitações de responsividade em telas de desktop, garantindo melhor adaptação da interface a diferentes tamanhos de tela e resoluções;
- Reforçar os testes automatizados e de integração para prevenir regressões em futuras atualizações;

Essas ações visam consolidar a solução como uma ferramenta robusta, acessível e adaptável aos diversos perfis de usuários envolvidos nas inspeções prediais.



# <a name="c5"></a>5. Testes

## 5.1. Relatório de testes de integração de endpoints automatizados 

### Visão Geral
O projeto implementa uma suíte abrangente de testes unitários utilizando Jest como framework de teste. Os testes são organizados por modelo/entidade e cobrem as principais operações CRUD (Create, Read, Update, Delete) de cada endpoint. A estrutura de testes foi projetada para garantir a robustez e confiabilidade do sistema, com foco especial no tratamento de erros e validação de dados.

### Estrutura dos Testes
Os testes estão localizados no diretório `src/tests/` e seguem um padrão consistente de organização. Cada arquivo de teste corresponde a um modelo específico do sistema, garantindo uma cobertura completa das funcionalidades.

#### Modelos Testados

| Modelo | Arquivo | Linhas | Descrição |
|--------|---------|---------|------------|
| Usuários | usuariosModel.test.js | 105 | Testes de autenticação e gerenciamento de usuários |
| Equipes | equipesModel.test.js | 115 | Testes de criação e gestão de equipes |
| Fotos | fotosModel.test.js | 103 | Testes de upload e gerenciamento de fotos |
| Ocorrências | ocorrenciasModel.test.js | 100 | Testes de registro e gestão de ocorrências |
| Inspeções | inspecoesModel.test.js | 94 | Testes de criação e gestão de inspeções |
| Relatórios | relatoriosModel.test.js | 83 | Testes de geração e gestão de relatórios |
| Endereços | enderecosModel.test.js | 71 | Testes de cadastro e gestão de endereços |
| Ambientes | ambienteModel.test.js | 71 | Testes de criação e gestão de ambientes |
| Tipos de Edificação | tipoEdificacaoModel.test.js | 52 | Testes de categorização de edificações |
| Afazeres | afazeresModel.test.js | 31 | Testes de gestão de tarefas |
| Tipos de Patologia | tipoPatologiaModel.test.js | 19 | Testes de categorização de patologias |
| Tipos de Sistema | tipoSistemaModel.test.js | 19 | Testes de categorização de sistemas |

### Padrão de Testes
Cada conjunto de testes segue um padrão consistente que inclui:

#### 1. Testes de Criação (Create)

| Tipo de Teste | Descrição | Exemplo |
|---------------|-----------|----------|
| Criação bem-sucedida | Verifica se um registro é criado corretamente | `it('deve criar um usuário com sucesso')` |
| Tratamento de erros | Verifica o comportamento em caso de falha | `it('deve retornar erro ao criar usuário')` |
| Validação de dados | Verifica a integridade dos dados salvos | `it('deve validar dados obrigatórios')` |

#### 2. Testes de Leitura (Read)

| Tipo de Teste | Descrição | Exemplo |
|---------------|-----------|----------|
| Busca por ID | Verifica a recuperação por identificador | `it('deve buscar usuário por id')` |
| Busca por campo | Verifica a recuperação por campo específico | `it('deve buscar usuário por email')` |
| Listagem completa | Verifica a recuperação de todos os registros | `it('deve buscar todos os usuários')` |
| Registro não encontrado | Verifica o comportamento quando não há resultados | `it('deve retornar undefined se usuário não encontrado')` |

#### 3. Testes de Atualização (Update)

| Tipo de Teste | Descrição | Exemplo |
|---------------|-----------|----------|
| Atualização bem-sucedida | Verifica a modificação de registros | `it('deve atualizar um usuário')` |
| Validação de dados | Verifica a integridade dos dados atualizados | `it('deve lançar erro se tentar atualizar sem dados válidos')` |
| Tratamento de erros | Verifica o comportamento em caso de falha | `it('deve tratar erro ao atualizar usuário')` |

#### 4. Testes de Remoção (Delete)

| Tipo de Teste | Descrição | Exemplo |
|---------------|-----------|----------|
| Remoção bem-sucedida | Verifica a exclusão de registros | `it('deve remover um usuário')` |
| Registro inexistente | Verifica o comportamento com registros não encontrados | `it('deve retornar undefined ao remover usuário inexistente')` |
| Tratamento de erros | Verifica o comportamento em caso de falha | `it('deve tratar erro ao remover usuário')` |

### Exemplo Detalhado: Testes de Usuários
O arquivo `usuariosModel.test.js` demonstra a implementação completa dos testes:

```javascript
// Exemplo de testes para criação de usuário
it('deve criar um usuário com sucesso', async () => {
  bcrypt.genSalt.mockResolvedValueOnce('salt');
  bcrypt.hash.mockResolvedValueOnce('hashed');
  db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Teste', email: '<EMAIL>' }] });
  const result = await Usuario.create({ nome: 'Teste', email: '<EMAIL>', senha: '123456' });
  expect(result).toHaveProperty('id');
  expect(result.nome).toBe('Teste');
});
```

### Cobertura de Testes

| Área de Cobertura | Descrição |
|-------------------|-----------|
| Operações CRUD | Testes para todas as operações básicas em todos os modelos |
| Tratamento de Erros | Verificação de comportamentos em caso de falhas |
| Validação de Dados | Testes de integridade e validação de dados |
| Casos de Sucesso | Verificação de operações bem-sucedidas |
| Casos de Falha | Verificação de comportamentos em situações de erro |
| Integração com DB | Testes de interação com o banco de dados (mockado) |
| Regras de Negócio | Validação de regras específicas do sistema |

### Mocking e Dependências

| Componente | Descrição |
|------------|-----------|
| jest.mock() | Utilizado para mockar dependências |
| Módulo DB | Mock do módulo de banco de dados |
| Bibliotecas Externas | Mock de bibliotecas como bcryptjs |
| Limpeza de Mocks | Implementação de afterEach para limpeza |

### Boas Práticas Implementadas

| Prática | Descrição |
|---------|-----------|
| Limpeza de Mocks | Limpeza após cada teste (afterEach) |
| Testes Isolados | Independência entre casos de teste |
| Verificações | Cobertura de casos de sucesso e erro |
| Validação de Dados | Verificação de tipos e estruturas |
| Tratamento de Erros | Gestão adequada de erros assíncronos |
| Nomenclatura | Nomes descritivos para os testes |
| Organização | Estrutura lógica dos casos de teste |
| Casos de Borda | Cobertura de casos limites |

### Relatório de Cobertura
Para gerar o relatório de cobertura de testes, execute o comando:
```bash
npm run test: coverage
```

O relatório será gerado no diretório `coverage/` e incluirá:
- Porcentagem de cobertura por arquivo
- Linhas cobertas/não cobertas
- Branches cobertos/não cobertos
- Funções cobertas/não cobertas

Esta documentação fornece uma visão abrangente dos testes unitários implementados no projeto, demonstrando a robustez e confiabilidade do sistema através de uma cobertura extensiva de casos de teste.

## 5.2. Testes de usabilidade 


A documentação detalhada dos testes de usabilidade pode ser encontrada em **[Documentação dos Testes de Usabilidade](https://docs.google.com/spreadsheets/d/1A5tkIQy6HD_AhnS7qeK6z3XmY_oN7__-dIdVIZknbwU/edit?gid=0#gid=0).**

A partir dos testes de usabilidade, foram identificadas diversos pontos de melhoria, que podem ser corrigidos na versão final para melhorar a experiencia do usuário. A seguir a lista dos pontos de melhoria:

- Relatório não têm seu coteúdo.
- Não mostra o que está errado no login, se é a senha ou o email.
- Os filtros não estão funcionando corretamente.
- Usabilidade ruim na tela de editar perfil.



# <a name="c6"></a>6. Estudo de Mercado e Plano de Marketing 

## 6.1 Resumo Executivo

&emsp;O projeto **InsPecT** foi concebido para atender a uma expressiva oportunidade de mercado no setor de engenharia diagnóstica no Brasil. Foi identificada uma crescente demanda por digitalização e padronização nos processos de inspeção predial, impulsionada por novas legislações e pela necessidade de maior eficiência e rastreabilidade, em um cenário ainda dominado por métodos manuais. Diante disso, foi desenvolvida uma solução digital robusta, na forma de uma aplicação web, para a gestão completa do ciclo de inspeções.
&emsp;O diferencial competitivo da plataforma reside na fusão de especialização técnica com usabilidade intuitiva. A aplicação permite a coordenação de equipes, o registro centralizado de ocorrências e evidências, e a geração automatizada de relatórios, garantindo controle rigoroso das atividades e conformidade normativa. Sua arquitetura escalável e adaptável a diferentes tipologias de empreendimentos a distingue de soluções genéricas ou de alto custo.
&emsp;O objetivo estratégico é posicionar o InsPecT como uma ferramenta de referência para empresas de engenharia, administradoras de condomínios e construtoras. A estratégia de mercado será focada em canais digitais, com um modelo de negócio por assinatura (SaaS) que oferece planos escaláveis, visando penetração no mercado e crescimento sustentável.

---
## 6.2 Análise de Mercado

#### Visão Geral do Setor:
&emsp;O setor de inspeção predial no Brasil encontra-se em um momento de formalização e expansão. Este movimento é catalisado principalmente por dois fatores: o regulatório e o tecnológico. Do ponto de vista regulatório, a promulgação da norma técnica **ABNT NBR 16747:2020** estabeleceu diretrizes e procedimentos padronizados, conferindo maior rigor técnico e profissionalismo ao setor. Complementarmente, diversas legislações municipais têm tornado as inspeções periódicas obrigatórias, criando uma demanda compulsória por esses serviços.
&emsp;Tecnologicamente, o setor da construção civil, tradicionalmente conservador, passa por uma acelerada transformação digital. A ascensão das *ConTechs* e *PropTechs* evidencia uma forte tendência à adoção de soluções digitais para otimização de processos, gestão de ativos e análise de dados. Nesse contexto, a substituição de métodos manuais (baseados em papel e planilhas) por plataformas digitais é vista como um passo essencial para aumentar a eficiência, a rastreabilidade e a segurança das informações coletadas em campo, alinhando a engenharia diagnóstica às práticas da Indústria 4.0 .

#### Tamanho e Crescimento do Mercado:
&emsp;A relevância deste mercado é confirmada em escala global. Conforme projeções da MarketsandMarkets (2023), o mercado de software para gerenciamento de ativos imobiliários (Property Management Software) está estimado para atingir US$ 7,6 bilhões até 2028, com uma robusta taxa de crescimento anual composta (CAGR) de 10,3%. Embora o Brasil represente uma fração desse montante, o seu potencial é acentuado pela baixa penetração de tecnologia especializada, um fato corroborado por levantamentos de entidades como a Associação Brasileira de Facilities (ABRAFAC). Tais estudos indicam que uma parcela significativa de edifícios comerciais no país ainda opera com métodos manuais ou planilhas, revelando uma expressiva lacuna de mercado. Essa defasagem tecnológica, quando aplicada ao público-alvo potencial – composto por construtoras, empresas de facilities e administradoras de condomínios –, se traduz em um universo de mais de 200 mil empreendimentos com necessidade recorrente de inspeções técnicas, configurando um mercado endereçável substancial e com demanda latente por digitalização .

#### Tendências de Mercado:
&emsp;Observa-se um conjunto de tendências que moldam a demanda por soluções como o InsPecT. A principal delas é a **digitalização dos processos operacionais**, com a substituição de checklists em papel por plataformas que automatizam o fluxo de trabalho e centralizam os dados em nuvem (McKinsey, 2023; Gartner, 2024).

&emsp;Outra tendência relevante é a crescente aplicação de **Inteligência Artificial (IA) e Big Data** para análise de dados históricos coletados em inspeções. O objetivo é evoluir de uma manutenção corretiva para uma abordagem preditiva, identificando padrões de degradação e otimizando a alocação de recursos (McKinsey Global Institute, 2023).

&emsp;A agenda **ESG (Environmental, Social, and Governance)** também exerce influência, incentivando práticas de gestão responsável que incluem a manutenção rigorosa de edificações para garantir segurança e sustentabilidade. Sistemas que documentam e rastreiam essas ações de conformidade são cada vez mais valorizados.

&emsp;Por fim, a **mobilidade e o modelo SaaS (Software as a Service)** são tendências consolidadas. A execução de atividades em campo por meio de aplicativos em tablets e smartphones é uma realidade, e o modelo de assinatura permite que empresas de todos os portes adotem soluções tecnológicas com baixo investimento inicial, flexibilidade e escalabilidade (IDC, 2023).

---

## 6.3 Análise da Concorrência

#### Principais Concorrentes:
&emsp;A análise do cenário competitivo revelou concorrentes diretos, indiretos e substitutos:

1.  **Concorrentes Diretos (Softwares Especializados):** Plataformas especializadas em checklists e vistorias, como **Checklist Fácil** e **Inspeção Pro**. Essas ferramentas destacam-se pela capacidade de padronizar processos e personalizar formulários. O Checklist Fácil possui forte penetração em diversos setores, enquanto o Inspeção Pro é uma solução nacional com foco em mobilidade para inspeções técnicas. Concorrentes internacionais como **"The Checker Pro"** e **"HappyCo"** também atuam no mercado com soluções robustas de auditoria digital.

2.  **Concorrentes Indiretos (Softwares de Gestão de Obras/Facilities):** Grandes sistemas de gestão de ativos e facilities (ERP), como **IBM TRIRIGA**, **Tractian**, e módulos de plataformas de gestão de obras como **Sienge**, oferecem funcionalidades de inspeção. Sua desvantagem competitiva para nosso nicho é a complexidade, o alto custo e a falta de especialização na terminologia e nos fluxos da engenharia diagnóstica.

3.  **Soluções Substitutas (Métodos Manuais):** A concorrência mais significativa ainda reside nos **métodos tradicionais**: planilhas, documentos de texto e relatórios fotográficos manuais. Embora não possuam custo de licenciamento, são processos ineficientes, propensos a erros e que não geram um histórico de dados estruturado para análise.

#### Vantagens Competitivas da Aplicação Web:
O InsPecT foi projetado para se diferenciar estrategicamente no mercado com base nas seguintes vantagens competitivas:

* **Especialização Nativa:** A plataforma é construída sobre os princípios e normas da inspeção predial (ABNT NBR 16747), diferentemente de concorrentes genéricos que exigem adaptação.
* **Flexibilidade e Customização:** A capacidade de personalizar formulários de inspeção para diferentes tipologias construtivas e ambientes oferece um registro contextualizado e de maior relevância técnica.
* **Geração Automatizada de Relatórios:** A automação na criação de relatórios técnicos padronizados e exportáveis otimiza drasticamente o tempo dos profissionais e reduz o retrabalho.
* **Centralização e Rastreabilidade:** A unificação de todo o histórico de inspeções, laudos, ocorrências e intervenções em um ambiente seguro e em nuvem cria um ativo de dados valioso para a gestão predial.
* **Usabilidade Otimizada para o Campo:** A interface foi projetada para ser intuitiva e responsiva em dispositivos móveis, visando máxima produtividade das equipes em campo.

---

## 6.4 Público-Alvo

#### Segmentação de Mercado
&emsp;O mercado para o InsPecT foi segmentado em três grupos prioritários com base em suas necessidades e modelo de atuação:

1. **Empresas de Engenharia Diagnóstica e Facilities:** Segmento primário, composto por empresas especializadas cujas operações centrais são serviços de manutenção, perícia e inspeção predial em edifícios corporativos, hospitais, escolas e outros. Segundo relatório do SindusCon (Sindicato da Indústria da Construção Civil do Estado de São Paulo, 2024), esse segmento cresce anualmente em função da necessidade de gestão eficiente dos ativos imobiliários.
2. **Administradoras de Condomínios e Síndicos Profissionais:** Este segmento busca soluções para garantir a conformidade legal das inspeções periódicas, controlar a manutenção e gerar relatórios de forma padronizada para assembleias e proprietários. De acordo com a Abadi (Associação Brasileira das Administradoras de Imóveis, 2023), a demanda por ferramentas digitais nesse mercado tem aumentado consistentemente.
3. **Construtoras e Incorporadoras:** Segmento secundário, focado no uso da aplicação para vistorias de entrega de obras e gestão de chamados de assistência técnica no período pós-venda, documentando o estado inicial dos imóveis. A CBIC (Câmara Brasileira da Indústria da Construção, 2023) destaca a importância da digitalização dos processos de vistoria para a redução de conflitos e aumento da satisfação dos clientes.

#### Perfil do Público-Alvo

&emsp;O perfil do usuário final da aplicação é predominantemente composto por **engenheiros civis, eletricistas, arquitetos e técnicos em edificações**, com idade entre 30 e 55 anos e formação técnica ou superior. Comportamentalmente, este público valoriza a precisão técnica, a otimização de processos e a mitigação de riscos operacionais e legais. São profissionais habituados ao uso de tecnologias móveis e sistemas de gestão em seu dia a dia. Segundo dados do Conselho Federal de Engenharia e Agronomia (CREA, 2024) e do IBGE (Instituto Brasileiro de Geografia e Estatística, 2023), esse perfil de usuário representa uma parcela significativa do mercado de construção e manutenção predial, com alta demanda por soluções digitais que centralizem dados, otimizem operações em campo e facilitem a comprovação de conformidade com as normas técnicas.

---

## 6.5 Posicionamento

#### Proposta de Valor Única:
&emsp;A proposta de valor do InsPecT é ser uma **plataforma digital especializada que traduz a complexidade da inspeção predial em um processo eficiente, seguro e rastreável**. A solução oferece uma combinação única de praticidade em campo, por meio de uma interface intuitiva, com a robustez necessária para garantir a conformidade técnica. Ao centralizar todos os dados da vistoria e automatizar a geração de relatórios, o InsPecT não apenas economiza tempo, mas eleva a qualidade e a confiabilidade do serviço prestado pelo profissional de engenharia.

####  Estratégia de Diferenciação:
&emsp;A diferenciação do InsPecT será construída sobre três pilares: **especialização funcional, usabilidade e uma estrutura de custo-benefício atrativa**. Em contraste com concorrentes que são ou genéricos ou excessivamente complexos e caros, o InsPecT se posiciona como uma solução completa e acessível, com foco exclusivo nas dores do inspetor predial. A lógica de posicionamento visa atender ao vasto mercado de pequenas e médias empresas de engenharia e administradoras de condomínios, um nicho ainda carente de soluções especializadas e de fácil adoção.

---

## 6.6 Estratégia de Marketing

#### Produto/Serviço:
&emsp;O InsPecT será oferecido como um serviço de software em nuvem (SaaS), acessível por navegador em desktops e dispositivos móveis. As funcionalidades centrais incluem: cadastro e execução de inspeções, registro de ocorrências com classificação de patologias, upload de evidências fotográficas, geração de relatórios automáticos e atribuição de tarefas por equipe. O diferencial está na flexibilidade de customização dos tipos de inspeção e na centralização de todo o histórico em um ambiente seguro, que promove a rastreabilidade total das atividades.

####  Preço:
&emsp;Foi adotado um modelo de precificação por assinatura mensal (SaaS), estruturado em planos escaláveis para garantir acessibilidade e sustentabilidade. A estratégia visa oferecer um custo-benefício superior às alternativas manuais (em termos de produtividade) e aos grandes softwares ERP. Serão oferecidos pacotes **Básico** e **Premium**, com funcionalidades e limites de usuários/inspeções distintos, permitindo que clientes de pequeno e médio porte contratem a solução de forma flexível e evoluam seu plano conforme o crescimento de suas operações.


#### Praça (Distribuição):
&emsp;A distribuição da aplicação será realizada de forma **100% digital**. O canal principal será o site oficial do produto, onde os clientes poderão se cadastrar, escolher um plano e iniciar o uso imediatamente (self-service). Para planos corporativos ou customizados, haverá um canal de venda consultiva. A aplicação será hospedada em nuvem, garantindo acesso de qualquer local, sem necessidade de instalação local. Estratégias de onboarding digital (tutoriais, vídeos, suporte via chat) serão implementadas para facilitar a adoção.

#### Promoção:
&emsp;A estratégia de promoção será focada em marketing digital educacional e de autoridade. Será desenvolvida uma estratégia de **Marketing de Conteúdo** com artigos de blog, e-books e webinars sobre temas técnicos relevantes para o público-alvo. O **SEO (Search Engine Optimization)** será aplicado para posicionar o site em buscas por termos como "software de inspeção predial" e "laudo de vistoria". Campanhas de anúncios pagos serão veiculadas em plataformas como **Google Ads** e **LinkedIn**, com segmentação precisa por cargo e setor. Adicionalmente, serão exploradas parcerias com associações de engenharia e eventos do setor para ampliar a visibilidade e credibilidade da marca.

---
# <a name="c7"></a>7. Conclusões e trabalhos futuros 

## Avaliação Final e Planos de Melhoria

### Atingimento dos Objetivos da Seção 2

A aplicação web InsPecT demonstrou ter atingido, de forma significativa, os objetivos definidos na Seção 2 deste documento. Entre os principais resultados alcançados, destaca-se a padronização do processo de inspeções prediais, por meio de formulários dinâmicos e adaptáveis ao tipo de edificação, e a centralização dos registros em um banco de dados relacional. O fluxo de trabalho proposto, desde a atribuição de equipes até a geração automatizada de relatórios técnicos, foi devidamente implementado e validado por meio de testes automatizados.

Outro objetivo atendido refere-se à rastreabilidade e à organização modular das informações, possibilitando a recuperação eficiente de dados históricos e facilitando o gerenciamento técnico. A arquitetura responsiva, aliada ao uso de tecnologias web modernas, também permitiu o uso da aplicação em campo por dispositivos móveis, conforme pretendido inicialmente.

### Pontos Fortes

- Estrutura robusta baseada na arquitetura MVC.
- Interface clara, funcional e responsiva.
- Sistema modular de inspeções e subinspeções.
- Geração automatizada e padronizada de relatórios.
- Validação técnica dos dados inseridos por meio de testes.
- Navegação intuitiva e fluxo consistente de tarefas.

### Pontos a Melhorar

Durante a fase de testes e validação da aplicação web InsPecT, foi identificado que as principais limitações concentram-se na versão para desktop, especialmente no painel administrativo. Observou-se que a responsividade da interface apresenta inconsistências em telas de maiores dimensões, como computadores e notebooks, dificultando a leitura, a organização visual e a interação com os elementos da interface.

Além disso, o painel administrativo carece de maior refinamento estrutural. A disposição dos dados não favorece a navegação eficiente, e a ausência de filtros e recursos visuais de apoio compromete a experiência de usuários com perfil gerencial ou administrativo. Essa limitação afeta diretamente a escalabilidade da aplicação para contextos institucionais com maior volume de inspeções e usuários.

### Plano de Ações para Melhorias Futuras

| Aspecto Identificado                                 | Ação Proposta                                                                 |
|------------------------------------------------------|--------------------------------------------------------------------------------|
| Responsividade inadequada em desktop                 | Implementar media queries específicas para tamanhos de tela acima de 1024px, reorganizando o grid de exibição. |
| Layout pouco otimizado no painel administrativo      | Redesenhar a arquitetura visual do painel com foco em clareza, legibilidade e usabilidade em telas grandes. |
| Navegação limitada para usuários administrativos     | Adicionar menus laterais colapsáveis, barras fixas e componentes visuais para facilitar o acesso às funcionalidades principais. |
| Ausência de filtros e visão consolidada              | Desenvolver filtros avançados e dashboards com estatísticas resumidas por equipe, tipo de sistema e status das inspeções. |




### Outras Ideias para Futuras Iterações

- Implementação de sistema de notificações por e-mail ou push para acompanhamento de prazos e conclusões de inspeções.
- Adição de um painel administrativo com indicadores visuais e dashboards de desempenho.
- Integração com APIs externas para validação de dados (como CNPJ ou geolocalização oficial).
- Desenvolvimento de um módulo offline para uso em campo sem necessidade de conexão contínua.
- Inclusão de recursos de Inteligência Artificial para sugestão de manutenções corretivas baseadas no histórico de inspeções.


# <a name="c8"></a>8. Referências 

Aqua. (2024). Glossário: Posicionamento de mercado. https://www.aqua.com.br/glossario/posicionamento-de-mercado

Assembleia Legislativa do Estado de São Paulo. (2014, maio 19). IPT: 115 anos impulsionando o desenvolvimento tecnológico. https://www.al.sp.gov.br/noticia/?id=329312

Câmara dos Deputados. (2022, dezembro 20). Orçamento da pesquisa científica perdeu mais de R$ 80 bilhões nos últimos sete anos. https://www.camara.leg.br/noticias/883070-orcamento-da-pesquisa-cientifica-perdeu-mais-de-r-80-bilhoes-nos-ultimos-sete-anos/

Elmasri, R., & Navathe, S. B. (2011). Fundamentals of database systems (6ª ed.). Addison-Wesley.

Inspeção Pro. (2024). Software para inspeção. https://inspecao.pro

IPT. (2017, junho 26). Menos burocracia, mais inovação. https://ipt.br/2017/06/26/menos-burocracia-mais-inovacao/

IPT. (2023, setembro 5). Laboratório de tecnologia e desempenho de sistemas construtivos. Instituto de Pesquisas Tecnológicas. https://ipt.br/2023/09/05/laboratorio-de-tecnologia-e-desempenho-de-sistemas-construtivos/

IPT. (2023, setembro 25). O que o IPT pode fazer por mim? https://ipt.br/2023/09/25/o-que-o-ipt-pode-fazer-por-mim/

IPT. (2024a). Fomento. https://ipt.br/fomento/

IPT. (2024b). Folder institucional IPT. https://ipt.br/wp-content/uploads/2024/03/Folder-Institucional-IPT.pdf

IPT. (2024c). Fomento - Ministério da Ciência, Tecnologia e Inovação. https://ipt.br/fomento/

IPT. (2024d). Apresentação institucional [Arquivo PDF]. https://drive.google.com/file/d/1mLnsd1Q_tkhSsVTQAW3J9hzcHhNn9Zyh/view

IPT. (2024). Instituto de Pesquisas Tecnológicas. https://ipt.br/

IPT Open Experience. (2023). Portas abertas à inovação. https://iptopen.ipt.br/portas-abertas-a-inovacao/

Nielsen Norman Group. (2023). Personas. NN/g Nielsen Norman Group. https://www.nngroup.com/articles/persona/

Patton, J. (2014). User story mapping: Discover the whole story, build the right product. O'Reilly Media.

PM3. (2024). Glossário: Value proposition. https://pm3.com.br/glossario/value-proposition

Porter, M. E., & Montgomery, C. (1980). A busca da vantagem competitiva. Campus.

Produttivo. (2024). Checklist de obra. https://www.produttivo.com.br/modelo/checklist-de-obra/

Project Management Institute. (2021). A guide to the project management body of knowledge (PMBOK® guide) (7ª ed.). Project Management Institute.

Salesforce. (2024). Análise SWOT: o que é e como fazer? Entenda com exemplos. https://www.salesforce.com/br/blog/analise-swot/

SEESP. (2019). Aos 120 anos, IPT busca inovação com iniciativa privada. https://www.seesp.org.br/site/comunicacao/noticias/item/18351-aos-120-anos-ipt-busca-inovacao-com-iniciativa-privada

Serpro. (2018). Por que, o que, onde e quando avaliar a experiência do usuário. https://www.serpro.gov.br/menu/noticias/noticias-2018/por-que-o-que-onde-e-quando-avaliar-a-experiencia-do-usuario

Silberschatz, A., Korth, H. F., & Sudarshan, S. (2013). Database system concepts (6ª ed.). McGraw-Hill.

Fielding, R. T. (2000). Architectural Styles and the Design of Network-based Software Architectures.

Richardson, L., & Amundsen, S. (2013). RESTful Web APIs. O'Reilly Media.

Associação Brasileira de Manutenção e Gestão de Ativos. (n.d.). ABRAMAN. https://abramanoficial.org.br

Associação Brasileira de Manutenção e Gestão de Ativos. (2022). Documento Nacional de Manutenção e Gestão de Ativos – 2022 [Relatório de pesquisa]. https://abramanoficial.org.br/downloads/pesquisa-documento-nacional-2022.pdf

Associação Brasileira de Manutenção e Gestão de Ativos. (n.d.). Documento Nacional de Manutenção e Gestão de Ativos [Informações sobre a pesquisa]. https://abramanoficial.org.br/publicacoes/documento-nacional

Associação Brasileira de Manutenção e Gestão de Ativos. (n.d.). Revista ABRAMAN [Edições e publicações técnicas]. https://abramanoficial.org.br/publicacoes/revista

Associação Brasileira de Manutenção e Gestão de Ativos. (n.d.). 39º Congresso Brasileiro de Manutenção e Gestão de Ativos. https://abramanoficial.org.br/eventos/congressos/39o-congresso-brasileiro-de-manutencao-e-gestao-de-ativos

DAY, Robert A. How to write and publish a scientific paper. 6. ed. Cambridge: Cambridge University Press, 2005.

Hague Group. (n.d.). Checkup – Full Building Inspections App: Plataforma abrangente para inspetores prediais. https://haguegroup.com.br/checkup-full-building-inspections-app/

Diferencial Serviços. (n.d.). Escolha a melhor empresa de inspeção predial para sua necessidade. https://www.diferencialservicossp.com.br/blog/categorias/artigos/escolha-a-melhor-empresa-de-inspe-ccedil-atilde-o-predial-para-sua-necessidade

Mendes, L. L. (2021). Proposta de um sistema informatizado para inspeção predial em edifícios residenciais multifamiliares [Trabalho de Conclusão de Curso, Centro Universitário do Planalto Central Apparecido dos Santos – UNICEPLAC]. Repositório Institucional UNICEPLAC. https://dspace.uniceplac.edu.br/bitstream/123456789/403/1/Lucas%20Lopes%20Mendes_0005631.pdf

Growth Code Network. (2025, 16 de maio). Building maintenance management software market size. Growth Code Network.

Grand View Research. (2024). Computerized maintenance management system market, 2024–2030 .

Mordor Intelligence. (2025). Facility management software market size & share analysis, 2025–2030.

Business Research Insights. (2025). Building maintenance services market size – forecast to 2033.

MarketsandMarkets. (n.d.). Building management system market size, share, industry trends.

StratView Insight. (2025, 18 de maio). Facility maintenance management software market size, share, trends, outlook and investment analysis.

Growth Code Network. (2025). Building maintenance management software market forecast to 2033. Growth Code Network.


# <a name="c9"></a>Anexos


