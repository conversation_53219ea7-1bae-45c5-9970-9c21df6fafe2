<link rel="stylesheet" href="/css/shortcut.css">

<div class="shortcut-container">
  <div class="shortcut-buttons">
    <button class="btn-primary" id= "add-ambient-btn" onclick="SCBnavigateTo('/add-ambient')">
      Adicionar ambiente
    </button>

    <button class="btn-primary" onclick="SCBnavigateTo('/add-occurrence')">
      Adicionar ocorrência
    </button>
  </div>

  <button class="shortcut-button btn-secondary" onclick="openShortcuts()">
    <img src="/assets/icons/add-white.svg" alt="Shortcut Icon">
  </button>
</div>

<script>
  const shortcutButtonsContainer = document.querySelector('.shortcut-buttons');

  if (!window.location.href.includes('details') || window.location.href.includes('report-details')) {
    document.querySelector('.shortcut-container').style.display = 'none';
  }

  if (!window.location.href.includes('ambient-details') ) {
    document.querySelector('.shortcut-buttons button:last-child').style.display = 'none';
  }
  
  function openShortcuts() {
    shortcutButtonsContainer.classList.toggle('active');
  }

  function SCBnavigateTo(url) {
    const parts = window.location.href.split('/');
    const inspectionDetailsIndex = parts.indexOf('inspection-details');
    const inspectionId = inspectionDetailsIndex !== -1 ? parts[inspectionDetailsIndex + 1] : null;
    const ambientDetailsIndex = parts.indexOf('ambient-details');
    const ambientId = ambientDetailsIndex !== -1 ? parts[ambientDetailsIndex + 1] : null;
    const occurrenceDetailsIndex = parts.indexOf('occurrence-details');
    const occurrenceId = occurrenceDetailsIndex !== -1 ? parts[occurrenceDetailsIndex + 1] : null;
    
    let targetUrl = url;
    if (inspectionId) {
      targetUrl += `?inspectionId=${inspectionId}`;
      if (ambientId) {
      targetUrl += `&ambientId=${ambientId}`;
      } else if (occurrenceId) {
      targetUrl += `&occurrenceId=${occurrenceId}`;
      }
    } else if (ambientId) {
      targetUrl += `?ambientId=${ambientId}`;
      if (occurrenceId) {
      targetUrl += `&occurrenceId=${occurrenceId}`;
      }
    }
    window.location.href = targetUrl;
  }
</script>