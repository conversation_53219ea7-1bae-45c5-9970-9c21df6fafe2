const db = require('../config/db'); 
const bcrypt = require('bcryptjs');

const Usuario = {
  async create({ nome, email, senha }) {
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(senha, salt);
    const query = `
      INSERT INTO usuarios (nome, email, senha)
      VALUES ($1, $2, $3)
      RETURNING id, nome, email, criado_em; 
    `;
    
    try {
      const { rows } = await db.query(query, [nome, email, hashedPassword]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao criar usuário no modelo:', error);
      throw error;
    }
  },

  async findByEmail(email) {
    const query = 'SELECT id, nome, email, senha, permissao FROM usuarios WHERE email = $1';
    try {
      const { rows } = await db.query(query, [email]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar usuário por email no modelo:', error);
      throw error;
    }
  },

  async findById(id) {
    const query = 'SELECT id, nome, email FROM usuarios WHERE id = $1';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar usuário por ID no modelo:', error);
      throw error;
    }
  },

  async findAll() {
    const query = 'SELECT id, nome, email FROM usuarios';
    try {
      const { rows } = await db.query(query);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar todos os usuários no modelo:', error);
      throw error;
    }
  },

  async update(id, dados) {
    const campos = [];
    const valores = [];
  
    let i = 1;
    for (const chave in dados) {
      if (dados[chave] !== undefined && dados[chave] !== '') {
        campos.push(`${chave} = $${i}`);
        valores.push(dados[chave]);
        i++;
      }
    }
  
    if (campos.length === 0) {
      throw new Error("Nenhum dado válido para atualizar.");
    }
  
    const query = `
      UPDATE usuarios
      SET ${campos.join(', ')}
      WHERE id = $${i}
      RETURNING id, nome, email;
    `;
  
    valores.push(id); 
  
    try {
      const { rows } = await db.query(query, valores);
      return rows[0];
    } catch (error) {
      console.error('Erro ao atualizar usuário no modelo:', error);
      throw error;
    }
  },
  
  async remove(id) {
    const query = 'DELETE FROM usuarios WHERE id = $1 RETURNING id;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0]; 
    } catch (error) {
      console.error('Erro ao deletar usuário no modelo:', error);
      throw error;
    }
  }
};

module.exports = Usuario;