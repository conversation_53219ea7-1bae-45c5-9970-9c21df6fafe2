require('dotenv').config();
const express = require('express');
const path = require('path');
const routes = require('./routes/index.js');
const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.set("views", path.join(__dirname, "views"));
app.set("view engine", "ejs");

app.use("/css", express.static(path.join(__dirname, "views/css")));
app.use("/js", express.static(path.join(__dirname, "views/js")));
app.use("/assets", express.static(path.join(__dirname, "assets")));

app.use((req, res, next) => {
  if (req.path.endsWith(".css")) {
    res.type("text/css");
  }
  if (req.path.endsWith(".js")) {
    res.type("application/javascript");
  }
  next();
});

app.use("/", routes);
app.use("/api/", require("./routes/apiRoutes"));

app.listen(PORT, () => {
  console.log(`Servidor rodando na porta ${PORT}`);
});