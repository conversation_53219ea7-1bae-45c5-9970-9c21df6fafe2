const projetosService = require('../services/projetosService');

exports.criarProjeto = async (req, res, next) => {
    try {
        const { codigo_projeto, nome, descricao } = req.body;

        if (!codigo_projeto || !nome) {
            return res.status(400).json({ 
                message: 'Código do projeto e nome são obrigatórios' 
            });
        }

        const novoProjeto = await projetosService.criarProjeto({
            codigo_projeto,
            nome,
            descricao
        });

        res.status(201).json(novoProjeto);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarProjeto:', error);
        const status = error.message.includes('Já existe') ? 409 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterProjetoPorCodigo = async (req, res, next) => {
    try {
        const { codigo_projeto } = req.params;
        const projeto = await projetosService.obterProjetoPorCodigo(codigo_projeto);
        res.status(200).json(projeto);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterProjetoPorCodigo:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterProjetoPorId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const projeto = await projetosService.obterProjetoPorId(id);
        res.status(200).json(projeto);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterProjetoPorId:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.listarProjetos = async (req, res, next) => {
    try {
        const projetos = await projetosService.listarProjetos();
        res.status(200).json(projetos);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarProjetos:', error);
        res.status(500).json({ message: 'Erro ao listar projetos' });
    }
};

exports.atualizarProjeto = async (req, res, next) => {
    try {
        const { id } = req.params;
        const dadosProjeto = req.body;

        const projetoAtualizado = await projetosService.atualizarProjeto(id, dadosProjeto);
        res.status(200).json(projetoAtualizado);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] atualizarProjeto:', error);
        const status = error.message.includes('não encontrado') ? 404 :
                      error.message.includes('Já existe') ? 409 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.deletarProjeto = async (req, res, next) => {
    try {
        const { id } = req.params;
        await projetosService.deletarProjeto(id);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarProjeto:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterEstatisticasProjetos = async (req, res, next) => {
    try {
        const estatisticas = await projetosService.obterEstatisticasProjetos();
        res.status(200).json(estatisticas);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterEstatisticasProjetos:', error);
        res.status(500).json({ message: 'Erro ao obter estatísticas dos projetos' });
    }
};
