const inspecoesService = require('../services/inspecoesService');
const geocodingService = require('../services/geocodingService');

// Cache para melhorar performance
const mapCache = {
    data: null,
    lastUpdate: null,
    cacheTime: 2 * 60 * 1000 // 2 minutos
};

class MapController {
    
    /**
     * Renderiza a página do mapa
     */
    async renderMapPage(req, res) {
        try {
            res.render('pages/map', {
                title: 'Mapa de Inspeções - InsPecT'
            });
        } catch (error) {
            console.error('Erro ao renderizar página do mapa:', error);
            res.status(500).render('pages/error', {
                title: 'Erro - InsPecT',
                message: 'Erro interno do servidor'
            });
        }
    }

    /**
     * API para obter dados das inspeções com coordenadas
     */
    async getInspectionsWithCoordinates(req, res) {
        try {
            // Verificar cache primeiro
            const now = Date.now();
            if (mapCache.data && mapCache.lastUpdate &&
                (now - mapCache.lastUpdate) < mapCache.cacheTime) {
                console.log('Retornando dados do cache do servidor');
                return res.json(mapCache.data);
            }

            // Buscar inspeções com endereços (limitado para performance)
            const inspections = await inspecoesService.listarInspecoesComEnderecos();

            // Se há muitas inspeções, processar apenas uma amostra para carregamento rápido
            const sampleSize = Math.min(inspections.length, 20);
            const sampleInspections = inspections.slice(0, sampleSize);

            console.log(`Processando ${sampleSize} inspeções de ${inspections.length} total para carregamento rápido`);

            // Processar coordenadas de forma otimizada
            const inspectionsWithCoordinates = await geocodingService.processInspectionsWithCoordinates(sampleInspections);

            // Filtrar apenas inspeções com coordenadas válidas
            const validInspections = inspectionsWithCoordinates.filter(
                inspection => inspection.latitude && inspection.longitude
            );

            const responseData = {
                success: true,
                data: validInspections,
                total: validInspections.length,
                totalWithoutCoordinates: sampleInspections.length - validInspections.length,
                message: `Mostrando ${validInspections.length} de ${inspections.length} inspeções (carregamento rápido)`
            };

            // Salvar no cache
            mapCache.data = responseData;
            mapCache.lastUpdate = now;

            res.json(responseData);
            
        } catch (error) {
            console.error('Erro ao obter inspeções com coordenadas:', error);
            res.status(500).json({
                success: false,
                message: 'Erro ao carregar dados do mapa',
                error: error.message
            });
        }
    }

    /**
     * API para obter coordenadas de uma inspeção específica
     */
    async getInspectionCoordinates(req, res) {
        try {
            const { id } = req.params;
            
            const inspection = await inspecoesService.obterInspecaoPorId(id);
            if (!inspection) {
                return res.status(404).json({
                    success: false,
                    message: 'Inspeção não encontrada'
                });
            }

            if (!inspection.cep) {
                return res.status(400).json({
                    success: false,
                    message: 'Inspeção não possui endereço cadastrado'
                });
            }

            const coordinates = await geocodingService.getCepCoordinates(inspection.cep);
            
            res.json({
                success: true,
                data: {
                    ...inspection,
                    ...coordinates,
                    endereco_completo: `${coordinates.endereco.logradouro}, ${inspection.numero || 'S/N'}, ${coordinates.endereco.bairro}, ${coordinates.endereco.cidade} - ${coordinates.endereco.uf}`
                }
            });
            
        } catch (error) {
            console.error('Erro ao obter coordenadas da inspeção:', error);
            res.status(500).json({
                success: false,
                message: 'Erro ao obter localização da inspeção',
                error: error.message
            });
        }
    }
}

module.exports = new MapController();
