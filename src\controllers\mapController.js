const inspecoesService = require('../services/inspecoesService');
const geocodingService = require('../services/geocodingService');

class MapController {
    
    /**
     * Renderiza a página do mapa
     */
    async renderMapPage(req, res) {
        try {
            res.render('pages/map', {
                title: 'Mapa de Inspeções - InsPecT'
            });
        } catch (error) {
            console.error('Erro ao renderizar página do mapa:', error);
            res.status(500).render('pages/error', {
                title: 'Erro - InsPecT',
                message: 'Erro interno do servidor'
            });
        }
    }

    /**
     * API para obter dados das inspeções com coordenadas
     */
    async getInspectionsWithCoordinates(req, res) {
        try {
            // Buscar inspeções com endereços
            const inspections = await inspecoesService.listarInspecoesComEnderecos();
            
            // Processar coordenadas
            const inspectionsWithCoordinates = await geocodingService.processInspectionsWithCoordinates(inspections);
            
            // Filtrar apenas inspeções com coordenadas válidas
            const validInspections = inspectionsWithCoordinates.filter(
                inspection => inspection.latitude && inspection.longitude
            );

            res.json({
                success: true,
                data: validInspections,
                total: validInspections.length,
                totalWithoutCoordinates: inspectionsWithCoordinates.length - validInspections.length
            });
            
        } catch (error) {
            console.error('Erro ao obter inspeções com coordenadas:', error);
            res.status(500).json({
                success: false,
                message: 'Erro ao carregar dados do mapa',
                error: error.message
            });
        }
    }

    /**
     * API para obter coordenadas de uma inspeção específica
     */
    async getInspectionCoordinates(req, res) {
        try {
            const { id } = req.params;
            
            const inspection = await inspecoesService.obterInspecaoPorId(id);
            if (!inspection) {
                return res.status(404).json({
                    success: false,
                    message: 'Inspeção não encontrada'
                });
            }

            if (!inspection.cep) {
                return res.status(400).json({
                    success: false,
                    message: 'Inspeção não possui endereço cadastrado'
                });
            }

            const coordinates = await geocodingService.getCepCoordinates(inspection.cep);
            
            res.json({
                success: true,
                data: {
                    ...inspection,
                    ...coordinates,
                    endereco_completo: `${coordinates.endereco.logradouro}, ${inspection.numero || 'S/N'}, ${coordinates.endereco.bairro}, ${coordinates.endereco.cidade} - ${coordinates.endereco.uf}`
                }
            });
            
        } catch (error) {
            console.error('Erro ao obter coordenadas da inspeção:', error);
            res.status(500).json({
                success: false,
                message: 'Erro ao obter localização da inspeção',
                error: error.message
            });
        }
    }
}

module.exports = new MapController();
