# Documentação da API - Web APIs e Endpoints

Este documento é destinado à explicação detalhada das Web APIs e endpoints disponíveis no sistema. Aqui você encontrará todas as informações necessárias para integrar e consumir os serviços oferecidos pela plataforma.

## Introdução às Web APIs

As Web APIs são fundamentais para a integração de sistemas, permitindo que diferentes aplicações se comuniquem de forma padronizada. Por meio de endpoints, que são os pontos de acesso às funcionalidades da API, é possível realizar operações como consulta, envio e atualização de dados. Essa comunicação promove escalabilidade, reutilização de serviços e acelera o desenvolvimento de soluções digitais (Fielding, 2000; Richardson & Amundsen, 2013). Assim, APIs e endpoints são essenciais para a construção de ecossistemas tecnológicos eficientes e conectados.

---

## Estrutura da Documentação

Cada seção abaixo representa um módulo da API, organizado por funcionalidade. Para cada endpoint, são fornecidos:

- **URL**: O caminho do endpoint
- **Método**: O verbo HTTP utilizado (GET, POST, PUT,PATCH E DELETE)
- **Headers**: Cabeçalhos necessários
- **Parâmetros da URL (Path Parameters)**: Partes dinâmicas da  URL
- **Body**: Estrutura do corpo da requisição (quando aplicável)
- **Response**: Exemplo de resposta com status code

---

### Autenticação

#### Login de Usuário
Este endpoint permite que um usuário se autentique no sistema para obter acesso aos demais recursos.

-   **URL:** `/api/login`
-   **Método:** `POST`
-   **Headers:**
    -   `Content-Type: application/json`
-   **Body:**
    ```json
    {
      "email": "<EMAIL>",
      "senha": "senha123"
    }
    ```
-   **Response (200 OK):**
    ```json
    {
      "usuario": {
        "id": 1,
        "nome": "Maria Oliveira",
        "email": "<EMAIL>"
      }
      
    }
    ```

---

### Usuários 
Gerenciamento de informações dos usuários do sistema.

#### Listar Usuários
-   **URL:** `/api/usuarios`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "nome": "Maria Oliveira",
        "email": "<EMAIL>"
      },
      {
        "id": 2,
        "nome": "João Silva",
        "email": "<EMAIL>"
      }
    ]
    ```

#### Obter Usuário Específico
-   **URL:** `/api/usuarios/:id_usuario`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_usuario}`: `integer` - Identificador único do usuário.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "nome": "Maria Oliveira",
      "email": "<EMAIL>"
    }
    ```

#### Criar Novo Usuário
-   **URL:** `/api/usuarios`
-   **Método:** `POST`
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "nome": "Novo Usuário",
      "email": "<EMAIL>",
      "senha": "senhaSuperForte123"
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 3,
      "nome": "Novo Usuário",
      "email": "<EMAIL>"
    }
    ```

#### Atualizar Usuário 
Este endpoint é usado para substituir completamente os dados de um usuário existente (exceto a senha, que geralmente tem um fluxo dedicado). Você deve enviar todos os campos editáveis que deseja manter.

-   **URL:** `/api/usuarios/:id_usuario`
-   **Método:** `PUT`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_usuario}`: `integer` - Identificador único do usuário.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "nome": "Usuário Com Nome Completo Atualizado",
      "email": "<EMAIL>"
    }
    ```
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "nome": "Usuário Com Nome Completo Atualizado",
      "email": "<EMAIL>"
    }
    ```

#### Atualizar Usuário Parcialmente 
Este endpoint permite modificar apenas alguns campos de um usuário existente sem afetar os outros. Envie no corpo da requisição somente os campos que deseja alterar.

-   **URL:** `/api/usuarios/:id_usuario`
-   **Método:** `PATCH`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_usuario}`: `integer` - Identificador único do usuário a ser parcialmente atualizado.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    *Envie apenas os campos que deseja modificar. Todos os campos são opcionais.*
    ```json
    
    {
      "nome": "Nome Parcialmente Alterado"
    }
    ```
-   **Response (200 OK):**
    *Retorna o recurso completo do usuário atualizado.*
    ```json
    {
      "id": 1,
      "nome": "Nome Parcialmente Alterado",
      "email": "<EMAIL>" 
    }
    ```

#### Deletar Usuário
-   **URL:** `/api/usuarios/:id_usuario`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_usuario}`: `integer` - Identificador único do usuário.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

## Inspeções 
Representa os projetos, edificações ou locais que estão sob inspeção.

#### Listar Inspeções
-   **URL:** `/api/inspecoes`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "nome": "Edifício Alpha",
        "criacao_em": "2024-05-20T10:00:00Z",
        "url_planta": "[https://example.com/planta_alpha.pdf](https://example.com/planta_alpha.pdf)",
        "id_tipo_edificacao": 1,
        "id_equipe": 1,
        "id_endereco": 1
      }
    ]
    ```

#### Criar Nova Inspeção
-   **URL:** `/api/inspecoes`
-   **Método:** `POST`
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "nome": "Edifício Beta",
      "url_planta": "[https://example.com/planta_beta.pdf](https://example.com/planta_beta.pdf)",
      "id_tipo_edificacao": 2,
      "id_equipe": 1,
      "id_endereco": 2
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 2,
      "nome": "Edifício Beta",
      "criacao_em": "2024-05-26T14:30:00Z",
      "url_planta": "[https://example.com/planta_beta.pdf](https://example.com/planta_beta.pdf)",
      "id_tipo_edificacao": 2,
      "id_equipe": 1,
      "id_endereco": 2
    }
    ```

#### Obter Inspeção Específica
-   **URL:** `/api/inspecoes/:id_inspecao`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "nome": "Edifício Alpha",
      "criacao_em": "2024-05-20T10:00:00Z",
      "url_planta": "[https://example.com/planta_alpha.pdf](https://example.com/planta_alpha.pdf)",
      "id_tipo_edificacao": 1,
      "id_equipe": 1,
      "id_endereco": 1
    }
    ```

#### Atualizar Inspeção 
Substitui todos os dados editáveis de uma inspeção existente.

-   **URL:** `/api/inspecoes/:id_inspecao`
-   **Método:** `PUT`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "nome": "Edifício Alpha Prime Totalmente Novo",
      "url_planta": "[https://example.com/planta_alpha_v3.pdf](https://example.com/planta_alpha_v3.pdf)",
      "id_tipo_edificacao": 1,
      "id_equipe": 2,
      "id_endereco": 1
    }
    ```
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "nome": "Edifício Alpha Prime Totalmente Novo",
      "criacao_em": "2024-05-20T10:00:00Z",
      "url_planta": "[https://example.com/planta_alpha_v3.pdf](https://example.com/planta_alpha_v3.pdf)",
      "id_tipo_edificacao": 1,
      "id_equipe": 2,
      "id_endereco": 1
    }
    ```

#### Atualizar Inspeção Parcialmente
Permite modificar apenas alguns campos de uma inspeção existente.

-   **URL:** `/api/inspecoes/:id_inspecao`
-   **Método:** `PATCH`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    *Envie apenas os campos que deseja modificar.*
    ```json
    {
      "nome": "Edifício Alpha (Nome Revisado)",
      "id_equipe": 3
    }
    ```
-   **Response (200 OK):**
    *Retorna o recurso completo da inspeção atualizada.*
    ```json
    {
      "id": 1,
      "nome": "Edifício Alpha (Nome Revisado)",
      "criacao_em": "2024-05-20T10:00:00Z",
      "url_planta": "[https://example.com/planta_alpha.pdf](https://example.com/planta_alpha.pdf)", 
      "id_tipo_edificacao": 1, 
      "id_equipe": 3, 
      "id_endereco": 1 
    }
    ```

#### Deletar Inspeção
-   **URL:** `/api/inspecoes/:id_inspecao`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

###  Endereços 
Gerenciamento de endereços.

#### Listar Endereços
-   **URL:** `/api/enderecos`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "cep": "01000-000",
        "numero": "123",
        "complemento": "Apto 10",
        "referencia": "Próximo ao metrô"
      }
    ]
    ```

#### Criar Novo Endereço
-   **URL:** `/api/enderecos`
-   **Método:** `POST`
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "cep": "02000-000",
      "numero": "456",
      "complemento": "Bloco B",
      "referencia": "Esquina com Rua Y"
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 2,
      "cep": "02000-000",
      "numero": "456",
      "complemento": "Bloco B",
      "referencia": "Esquina com Rua Y"
    }
    ```

#### Obter Endereço Específico
-   **URL:** `/api/enderecos/:id_endereco`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_endereco}`: `integer` - Identificador único do endereço.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "cep": "01000-000",
      "numero": "123",
      "complemento": "Apto 10",
      "referencia": "Próximo ao metrô"
    }
    ```

#### Atualizar Endereço 
Substitui todos os dados de um endereço existente.

-   **URL:** `/api/enderecos/:id_endereco`
-   **Método:** `PUT`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_endereco}`: `integer` - Identificador único do endereço.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "cep": "01000-001",
      "numero": "123 A",
      "complemento": "Apto 10 - Fundos Revisado",
      "referencia": "Ao lado da padaria XPTO"
    }
    ```
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "cep": "01000-001",
      "numero": "123 A",
      "complemento": "Apto 10 - Fundos Revisado",
      "referencia": "Ao lado da padaria XPTO"
    }
    ```

#### Atualizar Endereço Parcialmente 
Permite modificar apenas alguns campos de um endereço existente.

-   **URL:** `/api/enderecos/:id_endereco`
-   **Método:** `PATCH`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_endereco}`: `integer` - Identificador único do endereço.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    *Envie apenas os campos que deseja modificar.*
    ```json
    {
      "complemento": "Sala 3B"
    }
    ```
-   **Response (200 OK):**
    *Retorna o recurso completo do endereço atualizado.*
    ```json
    {
      "id": 1,
      "cep": "01000-001", 
      "numero": "123 A", 
      "complemento": "Sala 3B", 
      "referencia": "Ao lado da padaria XPTO" 
    }
    ```

#### Deletar Endereço
-   **URL:** `/api/enderecos/:id_endereco`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_endereco}`: `integer` - Identificador único do endereço.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

### Equipes 
Gerenciamento de equipes de inspeção.

#### Listar Equipes
-   **URL:** `/api/equipes`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "nome": "Equipe Alpha de Inspeção"
      }
    ]
    ```

#### Criar Nova Equipe
-   **URL:** `/api/equipes`
-   **Método:** `POST`
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "nome": "Equipe Beta Inspeções"
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 2,
      "nome": "Equipe Beta Inspeções"
    }
    ```

#### Obter Equipe Específica (com seus usuários)
Recupera os detalhes de uma equipe específica, incluindo a lista de seus usuários (membros) e os respectivos cargos de cada um na equipe.
-   **URL:** `/api/equipes/:id_equipe`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_equipe}`: `integer` - Identificador único da equipe.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1, 
      "nome": "Equipe Alpha de Inspeção",
      "usuarios": [ 
        {
          "id_usuario": 1, 
          "nome_usuario": "Maria Oliveira", 
          "cargo_nome": "COORDENADOR" 
        },
        {
          "id_usuario": 2,
          "nome_usuario": "João Silva",
          "cargo_nome": "MEMBRO"
        }
      ]
    }
    ```

#### Atualizar Nome da Equipe 
Atualiza o nome de uma equipe existente.
-   **URL:** `/api/equipes/:id_equipe`
-   **Método:** `PATCH` 
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_equipe}`: `integer` - Identificador único da equipe.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "nome": "Equipe Alpha (Nome Oficial)"
    }
    ```
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "nome": "Equipe Alpha (Nome Oficial)"
    }
    ```

#### Deletar Equipe
-   **URL:** `/api/equipes/:id_equipe`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_equipe}`: `integer` - Identificador único da equipe.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

#### Adicionar Usuário a uma Equipe
-   **URL:** `/api/equipes/:id_equipe/usuarios`
-   **Método:** `POST`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_equipe}`: `integer` - Identificador único da equipe.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "id_usuario": 3,
      "cargo_nome": "MEMBRO" 
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "mensagem": "Usuário adicionado à equipe com sucesso.",
      "associacao": {
        "id_equipe": 1, 
        "id_usuario": 3, 
        "cargo_nome": "MEMBRO" 
      }
    }
    ```

#### Remover Usuário de uma Equipe
-   **URL:** `/api/equipes/:id_equipe/usuarios/:id_usuario`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_equipe}`: `integer` - Identificador único da equipe.
    -   `{id_usuario}`: `integer` - Identificador único do usuário a ser removido.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

### Ambientes 
Representa as "Subinspeções" ou áreas específicas dentro de uma Inspeção (Imóvel).

#### Listar Ambientes de uma Inspeção
-   **URL:** `/api/inspecoes/:id_inspecao/ambientes`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "titulo": "Apartamento 101 - Sala",
        "id_inspecao": 1,
        "id_planta_baixa": null,
        "criacao_em": "2024-05-20T10:30:00Z",
        "observacoes": "Sala principal do apartamento"
      }
    ]
    ```

#### Criar Novo Ambiente em uma Inspeção
-   **URL:** `/api/inspecoes/:id_inspecao/ambientes`
-   **Método:** `POST`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "titulo": "Garagem - Vaga 23",
      "id_planta_baixa": null,
      "observacoes": "Vaga de garagem coberta"
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 2,
      "titulo": "Garagem - Vaga 23",
      "id_inspecao": 1,
      "id_planta_baixa": null,
      "criacao_em": "2024-05-26T16:00:00Z",
      "observacoes": "Vaga de garagem coberta"
    }
    ```

#### Obter Ambiente Específico
-   **URL:** `/api/ambientes/:id_ambiente`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "titulo": "Apartamento 101 - Sala",
      "id_inspecao": 1,
      "id_planta_baixa": null,
      "criacao_em": "2024-05-20T10:30:00Z",
      "observacoes": "Sala principal do apartamento"
    }
    ```

#### Atualizar Ambiente (Completo com `PUT`)
-   **URL:** `/api/ambientes/:id_ambiente`
-   **Método:** `PUT`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "titulo": "Apartamento 101 - Sala de Estar (Revisão Completa)",
      "id_planta_baixa": 7,
      "observacoes": "Todas as observações foram revisadas e atualizadas.",
      "id_usuario": 2,
      "id_ambiente_pai": null
    }
    ```
-   **Response (200 OK):** (Objeto do ambiente atualizado)

#### Atualizar Ambiente Parcialmente (com `PATCH`)
-   **URL:** `/api/ambientes/:id_ambiente`
-   **Método:** `PATCH`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "observacoes": "Adicionada observação sobre a ventilação."
    }
    ```
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "titulo": "Apartamento 101 - Sala Principal",
      "id_inspecao": 1,
      "id_planta_baixa": null,
      "criacao_em": "2024-05-20T10:30:00Z",
      "observacoes": "Adicionada observação sobre a ventilação."
    }
    ```

#### Deletar Ambiente
-   **URL:** `/api/ambientes/:id_ambiente`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

###  Ocorrências / Manifestações Patológicas 
Registros de problemas, achados ou manifestações patológicas encontradas em um ambiente.

#### Listar Ocorrências de um Ambiente
-   **URL:** `/api/ambientes/:id_ambiente/ocorrencias`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "titulo": "Fissura na Parede",
        "id_ambiente": 1,
        "id_tipo_patologia": 1,
        "descricao": "Fissura vertical próxima à janela.",
        "concluido_em": null
      }
    ]
    ```

#### Criar Nova Ocorrência em um Ambiente
-   **URL:** `/api/ambientes/:id_ambiente/ocorrencias`
-   **Método:** `POST`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "titulo": "Infiltração no Teto",
      "id_inspecao": 1, 
      "id_tipo_sistema": "Hidráulico", 
      "id_tipo_patologia": "Vazamento em Tubulação", 
      "descricao": "Mancha de umidade e gotejamento no teto do banheiro.",
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 2,
      "titulo": "Infiltração no Teto",
      "id_ambiente": 1,
      "id_inspecao": 1,
      "id_tipo_sistema": "Hidráulico",
      "id_tipo_patologia": "Vazamento em Tubulação",
      "descricao": "Mancha de umidade e gotejamento no teto do banheiro.",
      "concluido_em": null
    }
    ```

#### Obter Ocorrência Específica
-   **URL:** `/api/ocorrencias/:id_ocorrencia`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ocorrencia}`: `integer` - Identificador único da ocorrência.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "titulo": "Fissura na Parede",
      "id_ambiente": 1,
      "id_inspecao": 1,
      "id_tipo_sistema": "Revestimentos",
      "id_tipo_patologia": "Fissura Estrutural",
      "descricao": "Fissura vertical próxima à janela.",
      "concluido_em": null
    }
    ```

#### Atualizar Ocorrência 
-   **URL:** `/api/ocorrencias/:id_ocorrencia`
-   **Método:** `PUT`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ocorrencia}`: `integer` - Identificador único da ocorrência.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "titulo": "Fissura na Parede (Reclassificada)",
      "id_tipo_sistema": "Revestimentos",
      "id_tipo_patologia": "Fissura Estrutural",
      "descricao": "Fissura vertical próxima à janela. Reparo realizado e monitorado.",
      "concluido_em": "2024-05-25T10:00:00Z"
    }
    ```
-   **Response (200 OK):** (Objeto da ocorrência atualizado)

#### Atualizar Ocorrência Parcialmente 
-   **URL:** `/api/ocorrencias/:id_ocorrencia`
-   **Método:** `PATCH`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ocorrencia}`: `integer` - Identificador único da ocorrência.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "descricao": "Fissura vertical próxima à janela. Reparo realizado. Sem reincidência.",
      "concluido_em": "2024-05-28T10:00:00Z",
      "gravidade": "Baixa"
    }
    ```
-   **Response (200 OK):** (Objeto da ocorrência atualizado)

#### Deletar Ocorrência
-   **URL:** `/api/ocorrencias/:id_ocorrencia`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ocorrencia}`: `integer` - Identificador único da ocorrência.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

### Fotos 
Fotos associadas a ocorrências ou ambientes.

#### Listar Fotos de um Ambiente
-   **URL:** `/api/ambientes/:id_ambiente/fotos`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "id_ambiente": 1,
        "id_ocorrencia": 1, 
        "url": "/api/fotos/1/imagem", 
        "descricao": "Foto da fissura inicial"
      }
    ]
    ```

#### Adicionar Foto a um Ambiente
-   **URL:** `/api/ambientes/:id_ambiente/fotos`
-   **Método:** `POST`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_ambiente}`: `integer` - Identificador único do ambiente.
-   **Headers:**
    -   `Content-Type: multipart/form-data`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:** (multipart/form-data)
    -   `arquivo_imagem`: (o arquivo da imagem) 
    -   `id_ocorrencia` (opcional): `integer` - ID da ocorrência à qual a foto está ligada.
    -   `descricao` (opcional): `string` - Descrição da foto.
-   **Response (201 Created):**
    ```json
    {
      "id": 2,
      "id_ambiente": 1,
      "id_ocorrencia": 1, 
      "url": "/api/fotos/2/imagem", 
      "descricao": "Detalhe da fissura"
    }
    ```

#### Obter Metadados da Foto Específica
-   **URL:** `/api/fotos/:id_foto`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_foto}`: `integer` - Identificador único da foto.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "id_ambiente": 1,
      "id_ocorrencia": 1,
      "url": "/api/fotos/1/imagem",
      "descricao": "Foto da fissura inicial"
    }
    ```

#### Obter Imagem da Foto Específica
-   **URL:** `/api/fotos/:id_foto/imagem`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_foto}`: `integer` - Identificador único da foto.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):** (Conteúdo binário da imagem, com `Content-Type` apropriado, ex: `image/jpeg`)

#### Atualizar Metadados da Foto (com `PATCH`)
-   **URL:** `/api/fotos/:id_foto`
-   **Método:** `PATCH`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_foto}`: `integer` - Identificador único da foto.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "descricao": "Nova descrição para a foto.",
      "id_ocorrencia": 2 
    }
    ```
-   **Response (200 OK):** (Metadados da foto atualizados)

#### Deletar Foto
-   **URL:** `/api/fotos/:id_foto`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_foto}`: `integer` - Identificador único da foto.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

###  Relatórios 
Relatórios gerados para uma inspeção.

#### Listar Relatórios de uma Inspeção
-   **URL:** `/api/inspecoes/:id_inspecao/relatorios`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1,
        "id_inspecao": 1,
        "url": "[https://example.com/relatorios/inspecao_alpha_final.pdf]",
        "titulo": "Relatório Final Edifício Alpha"
      }
    ]
    ```

#### Adicionar Relatório a uma Inspeção
-   **URL:** `/api/inspecoes/:id_inspecao/relatorios`
-   **Método:** `POST`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Content-Type: application/json` 
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "url": "[https://example.com/relatorios/novo_relatorio.pdf]",
      "titulo": "Relatório Preliminar Edifício Beta" 
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 2,
      "id_inspecao": 1,
      "url": "[https://example.com/relatorios/novo_relatorio.pdf]",
      "titulo": "Relatório Preliminar Edifício Beta"
    }
    ```

#### Obter Relatório Específico
-   **URL:** `/api/relatorios/:id_relatorio`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_relatorio}`: `integer` - Identificador único do relatório.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    {
      "id": 1,
      "id_inspecao": 1,
      "url": "[https://example.com/relatorios/inspecao_alpha_final.pdf]",
      "titulo": "Relatório Final Edifício Alpha"
    }
    ```

#### Atualizar Metadados do Relatório 
-   **URL:** `/api/relatorios/:id_relatorio`
-   **Método:** `PATCH`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_relatorio}`: `integer` - Identificador único do relatório.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "titulo": "Relatório Final v1.1 - Edifício Alpha",
      "url": "[https://example.com/relatorios/inspecao_alpha_final_v1.1.pdf]"
    }
    ```
-   **Response (200 OK):** (Metadados do relatório atualizados)


#### Deletar Relatório
-   **URL:** `/api/relatorios/:id_relatorio`
-   **Método:** `DELETE`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_relatorio}`: `integer` - Identificador único do relatório.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (204 No Content)**

---

### Histórico de Status 
Log de mudanças de status de uma inspeção.

#### Listar Status de Inspeção Disponíveis 
-   **URL:** `/api/status-inspecao`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      { "nome": "Aberta" },
      { "nome": "Em Andamento" },
      { "nome": "Concluída" },
      { "nome": "Cancelada" }
    ]
    ```

#### Listar Histórico de Status de uma Inspeção 
-   **URL:** `/api/inspecoes/:id_inspecao/historico-status`
-   **Método:** `GET`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [
      {
        "id": 1, 
        "status_nome": "Aberta",
        "id_inspecao": 1,
        "adicionado_em": "2024-05-20T10:00:00Z"
      },
      {
        "id": 2,
        "status_nome": "Em Andamento",
        "id_inspecao": 1,
        "adicionado_em": "2024-05-21T09:00:00Z"
      }
    ]
    ```

#### Adicionar Registro de Status a uma Inspeção
Este endpoint registra uma nova entrada no histórico de status de uma inspeção, refletindo uma mudança no seu estado.
-   **URL:** `/api/inspecoes/:id_inspecao/historico-status`
-   **Método:** `POST`
-   **Parâmetros da URL (Path Parameters):**
    -   `{id_inspecao}`: `integer` - Identificador único da inspeção.
-   **Headers:**
    -   `Content-Type: application/json`
    -   `Authorization: Bearer <token_de_acesso>`
-   **Body:**
    ```json
    {
      "status_nome": "Concluída" 
    }
    ```
-   **Response (201 Created):**
    ```json
    {
      "id": 3,
      "status_nome": "Concluída",
      "id_inspecao": 1,
      "adicionado_em": "2024-05-26T17:00:00Z"
    }
    ```

---

### Tabelas de Lookup (Tipos)
Endpoints para listar valores de tabelas de tipo/lookup. Estas geralmente não possuem `POST`, `PUT`, `PATCH` ou `DELETE` via API pública.

#### Cargos 
-   **URL:** `/api/cargos`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [ 
      { "nome": "COORDENADOR" }, 
      { "nome": "MEMBRO" } 
    ]
    ```

#### Tipos de Edificação 
-   **URL:** `/api/tipos-edificacao`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [ 
      { "id": 1, "nome": "Residencial" }, 
      { "id": 2, "nome": "Comercial" } 
    ]
    ```

#### Tipos de Patologia 
-   **URL:** `/api/tipos-patologia`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [ 
      { "id": 1, "nome": "Fissura", "id_tipo_sistema": "Estrutural" }, 
      { "id": 2, "nome": "Infiltração", "id_tipo_sistema": "Hidráulico" } 
    ]
    ```

#### Tipos de Sistema 
-   **URL:** `/api/tipos-sistema`
-   **Método:** `GET`
-   **Headers:**
    -   `Authorization: Bearer <token_de_acesso>`
-   **Response (200 OK):**
    ```json
    [ 
      { "nome": "Estrutural" }, 
      { "nome": "Hidráulico" } 
    ]
    ```
---