<link rel="stylesheet" href="/css/inspection-details.css" />
<link rel="stylesheet" href="/css/toast.css" />

<div class="conteudo">
  <div class="image-banner">
    <img src="/assets/templates/inspection-banner.png" alt="Quadra Butantã" />
  </div>

  <div class="info-container">
    <div class="info">
      <div class="info-left">
        <h2 id="inspection-name">Carregando...</h2>
        <p id="inspection-address">Carregando endereço...</p>
        <div id="inspection-actions">
          <button
            id="start-inspection-btn"
            class="btn-secondary"
            style="display: none"
          >
            Iniciar inspeção
          </button>
          <button
            id="finish-inspection-btn"
            class="btn-secondary"
            style="display: none"
          >
            Finalizar inspeção
          </button>
        </div>
      </div>
    </div>

    <div class="filters-row">
      <div class="filters">
        <h3>Ambientes</h3>
        <div class="buttons">
          <button class="btn active" data-filter="all">Todos</button>
          <button class="btn" data-filter="aberta">Aberta</button>
          <button class="btn" data-filter="em-andamento">Em Andamento</button>
          <button class="btn" data-filter="concluida">Concluída</button>
        </div>
      </div>

      <div class="search-filter">
        <div class="search-input">
          <input
            type="text"
            placeholder="Buscar ambientes..."
            id="search-input"
          />
          <img
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cpath d='m21 21-4.35-4.35'%3E%3C/path%3E%3C/svg%3E"
            class="search-icon"
            alt="Buscar"
          />
        </div>
        <button class="filter-btn" id="advanced-filter">
          <img
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolygon points='22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3'%3E%3C/polygon%3E%3C/svg%3E"
            alt="Filtrar"
          />
        </button>
      </div>
    </div>

    <div class="loading" id="loading">Carregando ambientes...</div>

    <div class="cards" id="cards-container" style="display: none"></div>

    <div class="empty-state" id="empty-state" style="display: none">
      <h3>Nenhum ambiente encontrado</h3>
      <p>Tente ajustar os filtros ou criar um novo ambiente.</p>
    </div>
  </div>
</div>

<script src="/js/toast.js"></script>
<script src="/js/inspection-details.js"></script>
