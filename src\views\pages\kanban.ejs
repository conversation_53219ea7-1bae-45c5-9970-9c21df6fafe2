<link rel="stylesheet" href="/css/kanban.css" />

<div class="kanban-container">
  <div class="kanban-header">
    <h1 class="h4">Kanban de Inspeções</h1>
    <div class="kanban-actions">
      <button class="btn-outline" onclick="refreshKanban()">
        <img src="/assets/icons/refresh.svg" alt="Atualizar" />
        Atualizar
      </button>
      <button class="btn-outline" onclick="navigateTo('/home')">
        <img src="/assets/icons/arrow-left.svg" alt="Voltar" />
        Voltar
      </button>
    </div>
  </div>

  <div class="kanban-stats">
    <div class="stat-card">
      <div class="stat-number" id="total-inspections">0</div>
      <div class="stat-label">Total</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="open-inspections">0</div>
      <div class="stat-label">Abertas</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="progress-inspections">0</div>
      <div class="stat-label">Em Andamento</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="completed-inspections">0</div>
      <div class="stat-label">Concluídas</div>
    </div>
  </div>

  <div class="kanban-board">
    <div class="kanban-column" data-status="Aberta">
      <div class="column-header">
        <div class="column-title">
          <div class="status-indicator aberta"></div>
          <h3>Abertas</h3>
          <span class="count" id="count-aberta">0</span>
        </div>
      </div>
      <div class="column-content" id="column-aberta">
        <div class="loading-state">Carregando...</div>
      </div>
    </div>

    <div class="kanban-column" data-status="Em Andamento">
      <div class="column-header">
        <div class="column-title">
          <div class="status-indicator em-andamento"></div>
          <h3>Em Andamento</h3>
          <span class="count" id="count-em-andamento">0</span>
        </div>
      </div>
      <div class="column-content" id="column-em-andamento">
        <div class="loading-state">Carregando...</div>
      </div>
    </div>

    <div class="kanban-column" data-status="Concluída">
      <div class="column-header">
        <div class="column-title">
          <div class="status-indicator concluida"></div>
          <h3>Concluídas</h3>
          <span class="count" id="count-concluida">0</span>
        </div>
      </div>
      <div class="column-content" id="column-concluida">
        <div class="loading-state">Carregando...</div>
      </div>
    </div>
  </div>
</div>

<!-- Modal para mudança de status -->
<div class="modal-overlay" id="status-modal" style="display: none;">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Alterar Status da Inspeção</h3>
      <button class="modal-close" onclick="closeStatusModal()">&times;</button>
    </div>
    <div class="modal-body">
      <p>Inspeção: <strong id="modal-inspection-name"></strong></p>
      <div class="status-options">
        <button class="status-btn aberta" data-status="Aberta">Aberta</button>
        <button class="status-btn em-andamento" data-status="Em Andamento">Em Andamento</button>
        <button class="status-btn concluida" data-status="Concluída">Concluída</button>
        <button class="status-btn cancelada" data-status="Cancelada">Cancelada</button>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn-secondary" onclick="closeStatusModal()">Cancelar</button>
    </div>
  </div>
</div>

<script src="/js/kanban.js"></script>
