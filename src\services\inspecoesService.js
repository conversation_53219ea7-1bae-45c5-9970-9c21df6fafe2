const InspecaoModel = require('../models/inspecoesModel');
const StatusModel = require('../models/statusModel');
const HistoricoModel = require('../models/historicoModel');
const EquipeModel = require('../models/equipesModel');
const EnderecoModel = require('../models/enderecosModel');
const TipoEdificacaoModel = require('../models/tipoEdificacaoModel');

class InspecoesService {
    
    async listarInspecoes() {
        try {
            return await InspecaoModel.findAll();
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] listarInspecoes:', error);
            throw new Error('Erro ao listar inspeções.');
        }
    }

    async listarInspecoesComEnderecos() {
        try {
            return await InspecaoModel.findAllWithAddresses();
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] listarInspecoesComEnderecos:', error);
            throw new Error('Erro ao listar inspeções com endereços.');
        }
    }

    async listarInspecoesPorEquipe(id_equipe) {
        try {
            const todasInspecoes = await InspecaoModel.findAll();
            const inspecoesEquipe = todasInspecoes.filter(
                (i) => String(i.id_equipe) === String(id_equipe)
            );
            return inspecoesEquipe;
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] listarInspecoesPorEquipe:', error);
            throw new Error('Erro ao listar inspeções da equipe.');
        }
    }

    async validarDadosCriacao(dadosInspecao) {
        const { nome, url_planta, id_tipo_edificacao, id_equipe, id_endereco, id_projeto } = dadosInspecao;

        if (!nome || !id_tipo_edificacao || !id_equipe || !id_endereco) {
            throw new Error('Campos obrigatórios: nome, id_tipo_edificacao, id_equipe, id_endereco.');
        }

        if (nome.trim().length === 0) {
            throw new Error('Nome da inspeção não pode estar vazio.');
        }

        const tipoEdificacao = await TipoEdificacaoModel.findById(id_tipo_edificacao);
        if (!tipoEdificacao) {
            throw new Error('Tipo de edificação não encontrado.');
        }

        const equipe = await EquipeModel.findById(id_equipe);
        if (!equipe) {
            throw new Error('Equipe não encontrada.');
        }

        const endereco = await EnderecoModel.findById(id_endereco);
        if (!endereco) {
            throw new Error('Endereço não encontrado.');
        }

        if (url_planta && url_planta.trim().length > 0) {
            try {
                new URL(url_planta);
            } catch (error) {
                throw new Error('URL da planta inválida.');
            }
        }

        return {
            nome: nome.trim(),
            url_planta: url_planta?.trim() || null,
            id_tipo_edificacao: id_tipo_edificacao,
            id_equipe: parseInt(id_equipe),
            id_endereco: parseInt(id_endereco),
            id_projeto: id_projeto ? parseInt(id_projeto) : null
        };
    }

    async criarInspecao(dadosInspecao) {
        try {
            
            const dadosValidados = await this.validarDadosCriacao(dadosInspecao);

            const novaInspecao = await InspecaoModel.create(dadosValidados);

            await HistoricoModel.create({
                id_inspecao: novaInspecao.id,
                status_id: 'Aberta'
            });

            return novaInspecao;
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] criarInspecao:', error);
            throw error;
        }
    }

    async buscarHistoricoStatus(id_inspecao) {
        try {
            const historico = await HistoricoModel.findByInspecaoId(id_inspecao);
            return historico || [];
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] buscarHistoricoStatus:', error);
            return [];
        }
    }

    async obterStatusAtual(id_inspecao) {
        try {
            const historico = await this.buscarHistoricoStatus(id_inspecao);
            
            if (historico && historico.length > 0) {
                
                return historico[0].status_id;
            }
            
            return 'Aberta';
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] obterStatusAtual:', error);
            return 'Aberta';
        }
    }

    async obterInspecaoPorId(id_inspecao) {
        try {
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error('Inspeção não encontrada.');
            }

            const statusAtual = await this.obterStatusAtual(id_inspecao);

            const inspecaoComStatus = {
                ...inspecao,
                analise: statusAtual
            };

            return inspecaoComStatus;
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] obterInspecaoPorId:', error);
            throw error;
        }
    }

    async validarDadosAtualizacao(dadosInspecao) {
        const { nome, url_planta, id_tipo_edificacao, id_equipe, id_endereco } = dadosInspecao;

        const dadosValidados = {};

        if (nome !== undefined) {
            if (!nome || nome.trim().length === 0) {
                throw new Error('Nome da inspeção não pode estar vazio.');
            }
            dadosValidados.nome = nome.trim();
        }

        if (url_planta !== undefined) {
            if (url_planta && url_planta.trim().length > 0) {
                try {
                    new URL(url_planta);
                    dadosValidados.url_planta = url_planta.trim();
                } catch (error) {
                    throw new Error('URL da planta inválida.');
                }
            } else {
                dadosValidados.url_planta = null;
            }
        }

        if (id_tipo_edificacao !== undefined) {
            const tipoEdificacao = await TipoEdificacaoModel.findById(id_tipo_edificacao);
            if (!tipoEdificacao) {
                throw new Error('Tipo de edificação não encontrado.');
            }
            dadosValidados.id_tipo_edificacao = parseInt(id_tipo_edificacao);
        }

        if (id_equipe !== undefined) {
            const equipe = await EquipeModel.findById(id_equipe);
            if (!equipe) {
                throw new Error('Equipe não encontrada.');
            }
            dadosValidados.id_equipe = parseInt(id_equipe);
        }

        if (id_endereco !== undefined) {
            const endereco = await EnderecoModel.findById(id_endereco);
            if (!endereco) {
                throw new Error('Endereço não encontrado.');
            }
            dadosValidados.id_endereco = parseInt(id_endereco);
        }

        return dadosValidados;
    }

    async atualizarInspecao(id_inspecao, dadosInspecao) {
        try {
            
            await this.obterInspecaoPorId(id_inspecao);

            const dadosValidados = await this.validarDadosAtualizacao(dadosInspecao);

            const inspecaoAtualizada = await InspecaoModel.update(id_inspecao, dadosValidados);
            if (!inspecaoAtualizada) {
                throw new Error('Inspeção não encontrada para atualização.');
            }

            return inspecaoAtualizada;
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] atualizarInspecao:', error);
            throw error;
        }
    }

    async atualizarInspecaoStatus(id_inspecao, status_nome) {
        try {
            
            if (!status_nome) {
                throw new Error('O nome do status é obrigatório.');
            }

            const status = await StatusModel.findByName(status_nome);
            if (!status) {
                throw new Error(`Status '${status_nome}' não encontrado.`);
            }

            await this.obterInspecaoPorId(id_inspecao);

            const novoHistorico = await HistoricoModel.create({
                id_inspecao: parseInt(id_inspecao),
                status_id: status.nome
            });

            return {
                message: 'Status da inspeção atualizado com sucesso!',
                historico: novoHistorico
            };
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] atualizarInspecaoStatus:', error);
            throw error;
        }
    }

    async verificarPodeDeletar(id_inspecao) {
        try {

            return true;
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] verificarPodeDeletar:', error);
            throw error;
        }
    }

    async deletarInspecao(id_inspecao) {
        try {
            
            await this.obterInspecaoPorId(id_inspecao);

            await this.verificarPodeDeletar(id_inspecao);

            await HistoricoModel.deleteByInspecaoId(id_inspecao);

            const deletada = await InspecaoModel.remove(id_inspecao);
            if (!deletada) {
                throw new Error('Inspeção não encontrada para deleção.');
            }

            return true;
        } catch (error) {
            console.error('[INSPECOES_SERVICE_ERROR] deletarInspecao:', error);
            throw error;
        }
    }
}

module.exports = new InspecoesService();