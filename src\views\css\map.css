/* Mapa de Inspeções - Design Moderno */
.container {
  padding: 16px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
}

.near {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.map-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.info-card {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--primary-foreground));
  opacity: 0.8;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: var(--primary);
}

.info-label {
  font-size: 14px;
  color: var(--muted-foreground);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 32px;
  font-weight: 800;
  color: var(--foreground);
  line-height: 1;
}

.map-container {
  position: relative;
  width: 100%;
  height: 70vh;
  min-height: 500px;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--border);
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  background: var(--background);
}

#map {
  width: 100%;
  height: 100%;
  border-radius: 16px;
}

/* Marcadores otimizados */
.marker-dot {
  width: 20px;
  height: 20px;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 3px 10px rgba(0,0,0,0.3);
  transition: all 0.2s ease;
  cursor: pointer;
}

.marker-dot:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 15px rgba(0,0,0,0.4);
}

/* Controles do mapa */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.leaflet-control-zoom a {
  background: var(--background) !important;
  color: var(--foreground) !important;
  border: 1px solid var(--border) !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.leaflet-control-zoom a:hover {
  background: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border-color: var(--primary) !important;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(var(--background-rgb), 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  z-index: 1000;
  border-radius: 16px;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--muted-foreground);
  font-weight: 500;
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Animações para toasts */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.legend {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transition: all 0.2s ease;
}

.legend:hover {
  box-shadow: 0 6px 20px rgba(0,0,0,0.12);
}

.legend h3 {
  margin: 0 0 16px 0;
  color: var(--foreground);
  font-weight: 700;
  font-size: 18px;
}

.legend-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.legend-item:hover {
  background: var(--muted);
}

.marker-sample {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
  flex-shrink: 0;
}

.marker-sample.aberta {
  background-color: #ef4444;
}

.marker-sample.em-andamento {
  background-color: #f59e0b;
}

.marker-sample.concluida {
  background-color: #10b981;
}

.legend-item span {
  font-weight: 500;
  color: var(--foreground);
  font-size: 14px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-overlay.active {
  display: flex;
}

.modal-content {
  background: var(--background);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border);
}

.modal-header h3 {
  margin: 0;
  color: var(--foreground);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--muted-foreground);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background-color: var(--muted);
}

.modal-body {
  padding: 20px;
}

.inspection-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.detail-label {
  font-weight: 600;
  color: var(--muted-foreground);
  min-width: 80px;
}

.detail-value {
  color: var(--foreground);
  text-align: right;
  flex: 1;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--border);
  justify-content: flex-end;
}

/* Botões melhorados */
.btn {
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
}

.btn-secondary {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: 1px solid var(--border);
}

.btn-outline {
  background: transparent;
  color: var(--foreground);
  border: 2px solid var(--border);
}

.btn-outline:hover {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
}

/* Responsividade melhorada */
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .near {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .map-info {
    grid-template-columns: 1fr;
  }

  .info-card {
    text-align: center;
  }

  .map-container {
    height: 60vh;
    min-height: 400px;
    border-radius: 12px;
  }

  .legend-items {
    grid-template-columns: 1fr;
  }

  .modal-content {
    margin: 16px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-footer {
    flex-direction: column;
    gap: 8px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Melhorias para dark mode */
[data-theme="dark"] .leaflet-control-zoom a {
  background: var(--background) !important;
  color: var(--foreground) !important;
  border-color: var(--border) !important;
}

[data-theme="dark"] .leaflet-popup-content-wrapper {
  background: var(--background) !important;
  color: var(--foreground) !important;
}

[data-theme="dark"] .leaflet-popup-tip {
  background: var(--background) !important;
}

/* Otimizações de performance */
.custom-marker {
  will-change: transform;
}

.marker-dot {
  will-change: transform, box-shadow;
}

.map-container {
  will-change: auto;
}

/* Preloader para melhor UX */
.map-preloader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}

/* Smooth transitions */
* {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-row {
    flex-direction: column;
    gap: 4px;
  }

  .detail-value {
    text-align: left;
  }
}

/* Dark mode */
[data-theme="dark"] .map-container {
  border-color: var(--border);
}

[data-theme="dark"] .marker-sample {
  border-color: var(--background);
}

[data-theme="dark"] .modal-overlay {
  background: rgba(0, 0, 0, 0.7);
}
