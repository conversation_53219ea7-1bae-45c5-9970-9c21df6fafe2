/* Mapa de Inspeções */
.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.near {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 12px;
}

.map-info {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.info-card {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 150px;
}

.info-label {
  font-size: 14px;
  color: var(--muted-foreground);
  font-weight: 500;
}

.info-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--foreground);
}

.map-container {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border);
  margin-bottom: 20px;
}

.map {
  width: 100%;
  height: 100%;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--background);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.legend {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 16px;
}

.legend h3 {
  margin: 0 0 12px 0;
  color: var(--foreground);
}

.legend-items {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.marker-sample {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.marker-sample.aberta {
  background-color: #ef4444;
}

.marker-sample.em-andamento {
  background-color: #f59e0b;
}

.marker-sample.concluida {
  background-color: #10b981;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-overlay.active {
  display: flex;
}

.modal-content {
  background: var(--background);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border);
}

.modal-header h3 {
  margin: 0;
  color: var(--foreground);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--muted-foreground);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background-color: var(--muted);
}

.modal-body {
  padding: 20px;
}

.inspection-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.detail-label {
  font-weight: 600;
  color: var(--muted-foreground);
  min-width: 80px;
}

.detail-value {
  color: var(--foreground);
  text-align: right;
  flex: 1;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--border);
  justify-content: flex-end;
}

/* Responsividade */
@media (max-width: 768px) {
  .near {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .actions {
    justify-content: center;
  }

  .map-info {
    justify-content: center;
  }

  .info-card {
    flex: 1;
    min-width: 120px;
  }

  .map-container {
    height: 400px;
  }

  .legend-items {
    justify-content: center;
  }

  .modal-content {
    margin: 20px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .detail-row {
    flex-direction: column;
    gap: 4px;
  }

  .detail-value {
    text-align: left;
  }
}

/* Dark mode */
[data-theme="dark"] .map-container {
  border-color: var(--border);
}

[data-theme="dark"] .marker-sample {
  border-color: var(--background);
}

[data-theme="dark"] .modal-overlay {
  background: rgba(0, 0, 0, 0.7);
}
