const express = require('express');
const path = require('path');
const router = express.Router();

router.get('/', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Entrar',
    contentPage: path.join(__dirname, '../views/pages/login'),
    useBackButton: false,
    url: req.path,
  });
});

router.get('/verification', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Código de verificação',
    contentPage: path.join(__dirname, '../views/pages/verification-code'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/create-password', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: '<PERSON><PERSON><PERSON>',
    contentPage: path.join(__dirname, '../views/pages/create-password'),
    useBackButton: false,
    url: req.path,
  });
});

router.get('/home', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Início',
    contentPage: path.join(__dirname, '../views/pages/home'),
    useBackButton: false,
    url: req.path,
  });
});

router.get('/my-teams', async (req, res) => {
  const EquipeModel = require('../models/equipesModel');
  try {
    const equipesDb = await EquipeModel.findAll();
    const equipes = await Promise.all(equipesDb.map(async (equipe) => {
      const membros = await EquipeModel.findUsersByEquipeId(equipe.id);
      return {
        id: equipe.id,
        nome: equipe.nome,
        codigo: equipe.id, 
        membros: membros.map(m => ({
          id: m.id_usuario,
          nome: m.nome_usuario,
          cargo: m.cargo_nome
        }))
      };
    }));

    res.render(
      require('path').join(__dirname, '../views/layout/main'),
      {
        pageTitle: 'Minhas equipes',
        contentPage: require('path').join(__dirname, '../views/pages/my-teams'),
        useBackButton: true,
        url: req.path,
        equipes 
      }
    );
  } catch (error) {
    res.render(
      require('path').join(__dirname, '../views/layout/main'),
      {
        pageTitle: 'Minhas equipes',
        contentPage: require('path').join(__dirname, '../views/pages/my-teams'),
        useBackButton: true,
        url: req.path,
        equipes: [],
        error: 'Erro ao carregar equipes'
      }
    );
  }
});

router.get('/profile', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Perfil',
    contentPage: path.join(__dirname, '../views/pages/profile'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/reports', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Relatórios',
    contentPage: path.join(__dirname, '../views/pages/reports'),
    useBackButton: false,
    url: req.path,
  });
});

router.get('/report-details/:reportId', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Detalhes do Relatório',
    contentPage: path.join(__dirname, '../views/pages/report-details'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/inspection-details/:inspectionId', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Detalhes da Inspeção',
    contentPage: path.join(__dirname, '../views/pages/inspection-details'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/inspection-details/:inspectionId/ambient-details/:ambientId', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Detalhes do Ambiente',
    contentPage: path.join(__dirname, '../views/pages/ambient-details'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/inspection-details/:inspectionId/ambient-details/:ambientId/occurrence-details/:occurrenceId', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Detalhes da Ocorrência',
    contentPage: path.join(__dirname, '../views/pages/occurrence-details'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/add-ambient/', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Adicionar Ambiente',
    contentPage: path.join(__dirname, '../views/pages/add-ambient'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/add-occurrence/', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Adicionar Ocorrência',
    contentPage: path.join(__dirname, '../views/pages/add-occurrence'),
    useBackButton: true,
    url: req.path,
  });
});

router.get("/recover-password", (req, res) => {
  res.render(path.join(__dirname, "../views/layout/main"), {
    pageTitle: "Recuperar Senha",
    contentPage: path.join(__dirname, "../views/pages/recover-password"),
    useBackButton: false,
    url: req.path,
  });
});

router.get("/verification-code", (req, res) => {
  res.render(path.join(__dirname, "../views/layout/main"), {
    pageTitle: "Verificar Código",
    contentPage: path.join(__dirname, "../views/pages/verification-code"),
    useBackButton: false,
    url: req.path,
  });
});

router.get("/create-password", (req, res) => {
  res.render(path.join(__dirname, "../views/layout/main"), {
    pageTitle: "Criar Nova Senha",
    contentPage: path.join(__dirname, "../views/pages/create-password"),
    useBackButton: false,
    url: req.path,
  });
});

router.get('/adm/add-user', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Adicionar Usuário',
    contentPage: path.join(__dirname, '../views/pages/adm/add-user'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/adm/add-inspection', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Adicionar Inspeção',
    contentPage: path.join(__dirname, '../views/pages/adm/add-inspection'),
    useBackButton: true,
    url: req.path,
  });
});

router.get('/recover-password', (req, res) => {
  res.render(path.join(__dirname, '../views/layout/main'), {
    pageTitle: 'Recuperar Senha',
    contentPage: path.join(__dirname, '../views/pages/recover-password'),
    useBackButton: true
  });
});

module.exports = router;