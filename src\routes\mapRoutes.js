const express = require('express');
const router = express.Router();
const mapController = require('../controllers/mapController');
const authMiddleware = require('../middlewares/authMiddleware');

// Aplicar middleware de autenticação em todas as rotas
router.use(authMiddleware);

// Rota para renderizar a página do mapa
router.get('/', mapController.renderMapPage);

// API para obter inspeções com coordenadas
router.get('/api/inspections', mapController.getInspectionsWithCoordinates);

// API para obter coordenadas de uma inspeção específica
router.get('/api/inspections/:id/coordinates', mapController.getInspectionCoordinates);

module.exports = router;
