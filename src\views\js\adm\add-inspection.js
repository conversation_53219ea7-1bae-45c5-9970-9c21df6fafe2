document.addEventListener('DOMContentLoaded', async () => {
  const addInspectionForm = document.getElementById('addInspectionForm');
  const messageArea = document.getElementById('messageArea');
  const imageInput = document.getElementById('imageUpload');
  const uploadArea = document.querySelector('.upload-area');
  const initialUploadHTML = uploadArea.innerHTML;
  const token = localStorage.getItem('authToken');

  if (!token) {
    alert("Acesso negado. Por favor, faça o login.");
    window.location.href = '/';
    return;
  }
  const apiHeaders = { 'Authorization': `Bearer ${token}` };

  imageInput.addEventListener('change', () => {
    if (imageInput.files.length > 0) {
      const firstFile = imageInput.files[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        const fileCountText = imageInput.files.length > 1 ? `${imageInput.files.length} imagens selecionadas` : firstFile.name;
        uploadArea.innerHTML = `
                    <img src="${e.target.result}" alt="Pré-visualização" class="image-preview">
                    <p class="preview-info">${fileCountText}</p>
                    <button type="button" class="remove-selection-btn">Remover seleção</button>
                `;
        uploadArea.querySelector('.remove-selection-btn').addEventListener('click', (event) => {
          event.preventDefault();
          imageInput.value = '';
          uploadArea.innerHTML = initialUploadHTML;
        });
      };
      reader.readAsDataURL(firstFile);
    } else {
      uploadArea.innerHTML = initialUploadHTML;
    }
  });

  const tipoEdificacaoSelect = document.getElementById('tipoEdificacaoSelect');
  const equipeSelect = document.getElementById('equipeSelect');
  const cepInput = document.getElementById('cep');

  async function fetchData(url) {
    try {
      const response = await fetch(url, { headers: apiHeaders });
      if (response.status === 401) {
        window.location.href = '/';
        return [];
      }
      if (!response.ok) throw new Error(`Erro ao buscar dados de ${url}`);
      return await response.json();
    } catch (error) {
      showMessage(error.message, 'error');
      return [];
    }
  }

  function populateSelect(selectElement, items, placeholder, valueField = 'id', textField = 'nome') {
    selectElement.innerHTML = `<option value="" disabled selected>${placeholder}</option>`;
    items.forEach(item => {
      const option = document.createElement('option');
      option.value = item[valueField];
      option.textContent = item[textField];
      selectElement.appendChild(option);
    });
  }

  async function initializeForm() {
    const [tiposEdificacao, equipes] = await Promise.all([
      fetchData('/api/lookup/tipos-edificacao'),
      fetchData('/api/equipes')
    ]);
    populateSelect(tipoEdificacaoSelect, tiposEdificacao, 'Selecione o tipo de edificação', 'nome', 'nome');
    populateSelect(equipeSelect, equipes, 'Selecione uma equipe', 'id', 'nome');
  }

  cepInput.addEventListener('blur', async () => {
    const cep = cepInput.value.replace(/\D/g, '');
    if (cep.length !== 8) return;
    try {
      const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
      if (!response.ok) throw new Error('Não foi possível conectar ao serviço de CEP.');
      const data = await response.json();
      if (data.erro) {
        ['logradouro', 'bairro', 'cidade', 'uf'].forEach(id => document.getElementById(id).value = '');
        throw new Error('CEP não encontrado.');
      }
      document.getElementById('logradouro').value = data.logradouro;
      document.getElementById('bairro').value = data.bairro;
      document.getElementById('cidade').value = data.localidade;
      document.getElementById('uf').value = data.uf;
    } catch (error) {
      showMessage(error.message, 'error');
    }
  });

  addInspectionForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    messageArea.className = 'message-area';

    const addressData = {
      cep: document.getElementById('cep').value,
      numero: document.getElementById('numero').value,
      complemento: document.getElementById('complemento').value,
      referencia: document.getElementById('referencia').value
    };

    const codigoProjeto = document.getElementById('codigoProjeto').value;

    const inspectionBaseData = {
      nome: document.getElementById('nome').value,
      id_tipo_edificacao: document.getElementById('tipoEdificacaoSelect').value,
      id_equipe: parseInt(document.getElementById('equipeSelect').value, 10),
      url_planta: document.getElementById('url_planta').value,
    };

    if (!inspectionBaseData.nome || !codigoProjeto || !inspectionBaseData.id_tipo_edificacao || !inspectionBaseData.id_equipe || !addressData.cep || !addressData.numero) {
      showWarningToast('Todos os campos obrigatórios devem ser preenchidos.');
      showMessage('Todos os campos obrigatórios devem ser preenchidos.', 'error');
      return;
    }

    try {
      // 1. Criar o projeto primeiro
      const projetoData = {
        codigo_projeto: codigoProjeto,
        nome: inspectionBaseData.nome,
        descricao: `Projeto para a inspeção: ${inspectionBaseData.nome}`
      };

      const projetoResponse = await fetch('/api/projetos', {
        method: 'POST',
        headers: { ...apiHeaders, 'Content-Type': 'application/json' },
        body: JSON.stringify(projetoData)
      });

      const newProjeto = await projetoResponse.json();
      if (!projetoResponse.ok) throw new Error(newProjeto.message || 'Falha ao criar o projeto.');

      // 2. Criar o endereço
      const addressResponse = await fetch('/api/enderecos', {
        method: 'POST',
        headers: { ...apiHeaders, 'Content-Type': 'application/json' },
        body: JSON.stringify(addressData)
      });

      const newAddress = await addressResponse.json();
      if (!addressResponse.ok) throw new Error(newAddress.message || 'Projeto criado, mas falha ao criar o endereço.');

      // 3. Criar a inspeção com referência ao projeto
      const inspectionPayload = {
        ...inspectionBaseData,
        id_endereco: newAddress.id,
        id_projeto: newProjeto.id
      };

      const inspectionResponse = await fetch('/api/inspecoes', {
        method: 'POST',
        headers: { ...apiHeaders, 'Content-Type': 'application/json' },
        body: JSON.stringify(inspectionPayload)
      });

      const newInspection = await inspectionResponse.json();
      if (!inspectionResponse.ok) throw new Error(newInspection.message || 'Projeto e endereço criados, mas falha ao criar a inspeção.');

      if (imageInput.files.length > 0) {
        const fotosFormData = new FormData();
        for (const file of imageInput.files) {
          fotosFormData.append('fotos', file);
        }
        fotosFormData.append('id_inspecao', newInspection.id);

        const responseFotos = await fetch(`/api/fotos/upload/inspecao`, {
          method: 'POST',
          headers: apiHeaders,
          body: fotosFormData
        });

        if (!responseFotos.ok) {
          const errorFotoData = await responseFotos.json();
          throw new Error(errorFotoData.message || "Inspeção criada, mas falha ao enviar fotos.");
        }
      }

      showSuccessToast('Inspeção criada com sucesso!');
      showMessage('Inspeção criada com sucesso!', 'success');
      addInspectionForm.reset();
      uploadArea.innerHTML = initialUploadHTML;

    } catch (error) {
      showErrorToast(error.message);
      showMessage(error.message, 'error');
    }
  });

  function showMessage(message, type) {
    messageArea.textContent = message;
    messageArea.className = `message-area ${type}`;
  }

  initializeForm();
});