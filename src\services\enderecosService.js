const EnderecoModel = require('../models/enderecosModel');

class EnderecosService {
    
    async listarEnderecos() {
        try {
            return await EnderecoModel.findAll();
        } catch (error) {
            console.error('[ENDERECOS_SERVICE_ERROR] listarEnderecos:', error);
            throw new Error('Erro ao listar endereços.');
        }
    }

    validarDadosCriacao(dadosEndereco) {
        const { cep, numero, complemento, referencia } = dadosEndereco;
        
        if (!cep || !numero) {
            throw new Error('CEP e Número são obrigatórios.');
        }

        const cepLimpo = cep.replace(/\D/g, '');
        if (cepLimpo.length !== 8) {
            throw new Error('CEP deve ter 8 dígitos.');
        }

        if (isNaN(numero) || numero <= 0) {
            throw new Error('Número deve ser um valor positivo.');
        }

        return {
            cep: cepLimpo,
            numero: parseInt(numero),
            complemento: complemento?.trim() || null,
            referencia: referencia?.trim() || null
        };
    }

    async criarEndereco(dadosEndereco) {
        try {
            
            const dadosValidados = this.validarDadosCriacao(dadosEndereco);

            const novoEndereco = await EnderecoModel.create(dadosValidados);
            return novoEndereco;
        } catch (error) {
            console.error('[ENDERECOS_SERVICE_ERROR] criarEndereco:', error);
            throw error;
        }
    }

    async obterEnderecoPorId(id_endereco) {
        try {
            const endereco = await EnderecoModel.findById(id_endereco);
            if (!endereco) {
                throw new Error('Endereço não encontrado.');
            }
            return endereco;
        } catch (error) {
            console.error('[ENDERECOS_SERVICE_ERROR] obterEnderecoPorId:', error);
            throw error;
        }
    }

    validarDadosAtualizacao(dadosEndereco) {
        const { cep, numero, complemento, referencia } = dadosEndereco;
        const dadosValidados = {};

        if (cep !== undefined) {
            const cepLimpo = cep.replace(/\D/g, '');
            if (cepLimpo.length !== 8) {
                throw new Error('CEP deve ter 8 dígitos.');
            }
            dadosValidados.cep = cepLimpo;
        }

        if (numero !== undefined) {
            if (isNaN(numero) || numero <= 0) {
                throw new Error('Número deve ser um valor positivo.');
            }
            dadosValidados.numero = parseInt(numero);
        }

        if (complemento !== undefined) {
            dadosValidados.complemento = complemento?.trim() || null;
        }

        if (referencia !== undefined) {
            dadosValidados.referencia = referencia?.trim() || null;
        }

        return dadosValidados;
    }

    async atualizarEndereco(id_endereco, dadosEndereco) {
        try {
            
            await this.obterEnderecoPorId(id_endereco);

            const dadosValidados = this.validarDadosAtualizacao(dadosEndereco);

            const enderecoAtualizado = await EnderecoModel.update(id_endereco, dadosValidados);
            if (!enderecoAtualizado) {
                throw new Error('Endereço não encontrado para atualização.');
            }

            return enderecoAtualizado;
        } catch (error) {
            console.error('[ENDERECOS_SERVICE_ERROR] atualizarEndereco:', error);
            throw error;
        }
    }

    async verificarPodeDeletar(id_endereco) {
        try {

            return true;
        } catch (error) {
            console.error('[ENDERECOS_SERVICE_ERROR] verificarPodeDeletar:', error);
            throw error;
        }
    }

    async deletarEndereco(id_endereco) {
        try {
            
            await this.obterEnderecoPorId(id_endereco);

            await this.verificarPodeDeletar(id_endereco);

            const deletado = await EnderecoModel.remove(id_endereco);
            if (!deletado) {
                throw new Error('Endereço não encontrado para deleção.');
            }

            return true;
        } catch (error) {
            console.error('[ENDERECOS_SERVICE_ERROR] deletarEndereco:', error);
            throw error;
        }
    }

    async buscarPorCep(cep) {
        try {
            const cepLimpo = cep.replace(/\D/g, '');
            if (cepLimpo.length !== 8) {
                throw new Error('CEP deve ter 8 dígitos.');
            }

            const enderecos = await EnderecoModel.findByCep(cepLimpo);
            return enderecos || [];
        } catch (error) {
            console.error('[ENDERECOS_SERVICE_ERROR] buscarPorCep:', error);
            throw error;
        }
    }
}

module.exports = new EnderecosService();