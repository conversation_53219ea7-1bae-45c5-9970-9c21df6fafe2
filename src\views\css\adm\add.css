.form-page-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 2rem;
  height: 100%;
  overflow-y: auto;
}

.form-wrapper {
  background-color: var(--background-color);
  padding: 2rem;
  border-radius: 0.75rem;
  width: 100%;
  max-width: 40rem;
  border: 1px solid var(--muted-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-title {
  margin-bottom: 2rem;
  text-align: center;
  color: var(--foreground-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.form-body {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--foreground-color);
}

.form-body input,
.form-body select {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid var(--muted-foreground-color);
  font-size: 1rem;
  background-color: var(--background-color);
  color: var(--foreground-color);
}

.form-body input:focus,
.form-body select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 85, 140, 0.2);
}

.form-body input[readonly] {
  background-color: var(--muted-color);
  cursor: not-allowed;
}

.form-footer {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.btn-primary {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: var(--primary-hover-color);
}

.message-area {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  display: none;
}

.message-area.success {
  display: block;
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.message-area.error {
  display: block;
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

option {
  padding: 0.5rem;
  background-color: var(--background-color);
  color: var(--foreground-color);
}
