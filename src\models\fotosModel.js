const db = require('../config/db');

const Foto = {
  async create({ id_ambiente, id_ocorrencia, id_inspecao, url }) {

    const query = `
      INSERT INTO fotos (id_ambiente, id_ocorrencia, url)
      VALUES ($1, $2, $3)
      RETURNING *;
    `;
    const { rows } = await db.query(query, [id_ambiente, id_ocorrencia, url]);
    return rows[0];
  },

  async findAllByAmbienteId(id_ambiente, id_ocorrencia_filter = null) {
    let query = 'SELECT id, id_ambiente, id_ocorrencia, url FROM fotos WHERE id_ambiente = $1';
    const params = [id_ambiente];
    if (id_ocorrencia_filter) {
      query += ' AND id_ocorrencia = $2';
      params.push(id_ocorrencia_filter);
    }
    query += ' ORDER BY id;';
    const { rows } = await db.query(query, params);
    return rows;
  },

  async findAllByOcorrenciaId(id_ocorrencia) {
    const query = 'SELECT id, id_ambiente, id_ocorrencia, url FROM fotos WHERE id_ocorrencia = $1 ORDER BY id;';
    const { rows } = await db.query(query, [id_ocorrencia]);
    return rows;
  },

  async findById(id) {
    const query = 'SELECT id, id_ambiente, id_ocorrencia, url FROM fotos WHERE id = $1;';
    const { rows } = await db.query(query, [id]);
    return rows[0];
  },

  async update(id, { id_ocorrencia }) {
    const query = `
      UPDATE fotos 
      SET id_ocorrencia = $1
      WHERE id = $2
      RETURNING *;
    `;
    const { rows } = await db.query(query, [id_ocorrencia, id]);
    return rows[0];
  },

  async findAllByInspecaoId(id_inspecao) {
    
    const query = `
      SELECT f.id, f.id_ambiente, f.id_ocorrencia, f.url
      FROM fotos f
      INNER JOIN ambientes a ON f.id_ambiente = a.id
      WHERE a.id_inspecao = $1
      ORDER BY f.id;
    `;
    const { rows } = await db.query(query, [id_inspecao]);
    return rows;
  },

  async findAllByInspecaoIdWithAmbiente(id_inspecao) {
    
    const query = `
      SELECT f.id, f.id_ambiente, f.id_ocorrencia, f.url
      FROM fotos f
      INNER JOIN ambientes a ON f.id_ambiente = a.id
      WHERE f.id_ambiente IS NOT NULL AND a.id_inspecao = $1
      ORDER BY f.id;
    `;
    const { rows } = await db.query(query, [id_inspecao]);
    return rows;
  },

  async remove(id) {
    const query = 'DELETE FROM fotos WHERE id = $1 RETURNING *;';
    const { rows } = await db.query(query, [id]);
    return rows[0];
  }
};

module.exports = Foto;