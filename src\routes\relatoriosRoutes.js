const express = require("express");
const router = express.Router();
const relatorioController = require("../controllers/relatoriosController");

router.get("/", relatorioController.listarRelatorios);
router.get("/:id_relatorio", relatorioController.obterRelatorioPorId);
router.get("/:id_relatorio/preview", relatorioController.gerarPreviewRelatorio);
router.get("/:id_relatorio/debug-fotos", relatorioController.debugFotos);
router.delete("/:id_relatorio", relatorioController.deletarRelatorio);

router.get("/:id_relatorio/download", relatorioController.downloadRelatorioPDF);

module.exports = router;