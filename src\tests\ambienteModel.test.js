const db = require('../config/db');
jest.mock('../config/db'); 

const Ambiente = require('../models/ambientesModel');

describe('Model: Ambiente', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar um ambiente', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Sala' }] });
    const result = await Ambiente.create({ titulo: 'Sala', id_usuario: 1, id_inspecao: 1, id_ambiente_pai: null, observacoes: '' });
    expect(result.titulo).toBe('Sala');
  });

  it('deve tratar erro ao criar ambiente', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao criar ambiente:'));
    await expect(Ambiente.create({ titulo: 'Sala', id_usuario: 1, id_inspecao: 1, id_ambiente_pai: null, observacoes: '' }))
      .rejects.toThrow('Erro ao criar ambiente:');
  });

  it('deve buscar ambientes por inspeção', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Sala' }] });
    const result = await Ambiente.findAllByInspecaoId(1);
    expect(result[0].titulo).toBe('Sala');
  });

  it('deve tratar erro ao buscar ambientes por inspeção', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar ambientes por ID da inspeção:'));
    await expect(Ambiente.findAllByInspecaoId(1)).rejects.toThrow('Erro ao buscar ambientes por ID da inspeção:');
  });

  it('deve buscar ambiente por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Sala' }] });
    const result = await Ambiente.findById(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar undefined se ambiente não encontrado', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Ambiente.findById(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao buscar ambiente por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar ambiente por ID:'));
    await expect(Ambiente.findById(1)).rejects.toThrow('Erro ao buscar ambiente por ID:');
  });

  it('deve atualizar um ambiente', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Sala Nova' }] });
    const result = await Ambiente.update(1, { titulo: 'Sala Nova', id_usuario: 1, id_ambiente_pai: null, observacoes: 'obs' });
    expect(result.titulo).toBe('Sala Nova');
  });

  it('deve tratar erro ao atualizar ambiente', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar ambiente:'));
    await expect(Ambiente.update(1, { titulo: 'Sala Nova', id_usuario: 1, id_ambiente_pai: null, observacoes: 'obs' }))
      .rejects.toThrow('Erro ao atualizar ambiente:');
  });

  it('deve remover um ambiente', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Ambiente.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve tratar erro ao remover ambiente', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover ambiente:'));
    await expect(Ambiente.remove(1)).rejects.toThrow('Erro ao remover ambiente:');
  });
});