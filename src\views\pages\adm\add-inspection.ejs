<link rel="stylesheet" href="/css/adm/add-inspection.css">
<link rel="stylesheet" href="/css/toast.css">

<div class="form-page-container">
  <div class="form-wrapper">
    <h1 class="h4 form-title">Criar Nova Inspeção</h1>

    <div id="messageArea" class="message-area"></div>

    <form id="addInspectionForm" class="form-body" novalidate>

      <div class="form-group">
        <label for="nome" class="form-label">Nome da Inspeção</label>
        <input type="text" id="nome" name="nome" placeholder="Ex: Inspeção Estrutural Bloco A" required>
      </div>

      <div class="form-group">
        <label for="codigoProjeto" class="form-label">Número do Projeto</label>
        <input type="text" id="codigoProjeto" name="codigo_projeto" placeholder="Ex: PROJ-2024-001" required>
        <small class="form-help">Código único que identifica o projeto desta inspeção</small>
      </div>

      <div class="form-group">
        <label for="tipoEdificacaoSelect" class="form-label">Tipo de Edificação</label>
        <select id="tipoEdificacaoSelect" name="id_tipo_edificacao" required>
          <option value="" disabled selected>Carregando...</option>
        </select>
      </div>

      <div class="form-group">
        <label for="equipeSelect" class="form-label">Equipe Responsável</label>
        <select id="equipeSelect" name="id_equipe">
          <option value="" disabled selected>Carregando...</option>
        </select>
      </div>

      <div class="form-group">
        <label for="imageUpload" class="upload-area">
          <div class="upload-icons">
            <div class="icon-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z">
                </path>
                <circle cx="12" cy="13" r="4"></circle>
              </svg>
              <span>Abrir câmera</span>
            </div>
            <div class="icon-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                <polyline points="21 15 16 10 5 21"></polyline>
              </svg>
              <span>Adicionar imagem</span>
            </div>
          </div>
          <span class="upload-text">Escolha ou arraste uma imagem aqui</span>
          <input type="file" id="imageUpload" name="fotos" accept="image/*" multiple>
        </label>
      </div>

      <div class="form-group">
        <label for="cep" class="form-label">CEP</label>
        <input type="text" id="cep" name="cep" placeholder="Digite o CEP para buscar o endereço" required>
      </div>

      <div class="form-group">
        <label for="logradouro" class="form-label">Logradouro</label>
        <input type="text" id="logradouro" name="logradouro" placeholder="Preenchido automaticamente" readonly>
      </div>

      <div class="form-group">
        <label for="bairro" class="form-label">Bairro</label>
        <input type="text" id="bairro" name="bairro" placeholder="Preenchido automaticamente" readonly>
      </div>

      <div class="form-group">
        <label for="cidade" class="form-label">Cidade</label>
        <input type="text" id="cidade" name="cidade" placeholder="Preenchido automaticamente" readonly>
      </div>

      <div class="form-group">
        <label for="uf" class="form-label">Estado (UF)</label>
        <input type="text" id="uf" name="uf" placeholder="Preenchido automaticamente" readonly>
      </div>

      <div class="form-group">
        <label for="numero" class="form-label">Número</label>
        <input type="text" id="numero" name="numero" placeholder="Ex: 123" required>
      </div>

      <div class="form-group">
        <label for="complemento" class="form-label">Complemento (Opcional)</label>
        <input type="text" id="complemento" name="complemento" placeholder="Ex: Apto 42, Bloco C">
      </div>

      <div class="form-group">
        <label for="referencia" class="form-label">Ponto de Referência (Opcional)</label>
        <input type="text" id="referencia" name="referencia" placeholder="Ex: Próximo ao metrô">
      </div>

      <div class="form-group">
        <label for="url_planta" class="form-label">URL da Planta (Opcional)</label>
        <input type="url" id="url_planta" name="url_planta" placeholder="https://exemplo.com/planta.pdf">
      </div>

      <div class="form-footer">
        <button type="submit" class="btn-primary">Criar Inspeção</button>
      </div>
    </form>
  </div>
</div>

<script src="/js/toast.js"></script>
<script src="/js/adm/add-inspection.js"></script>