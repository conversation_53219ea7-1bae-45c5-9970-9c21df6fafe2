document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem('authToken');
    if (!token) {
        alert("Acesso negado. Por favor, faça o login.");
        window.location.href = '/';
        return;
    }
    const apiHeaders = { 'Authorization': `Bearer ${token}` };

    const form = document.getElementById('addOccurrenceForm');
    const inspecaoSelect = document.getElementById('inspecaoSelect');
    const ambienteGroup = document.getElementById('ambienteGroup');
    const ambienteSelect = document.getElementById('ambienteSelect');
    const systemTypeTagsContainer = document.getElementById('system-type-tags');
    const pathologyTagsContainer = document.getElementById('pathology-tags');
    const messageArea = document.getElementById('messageArea');
    const imageInput = document.getElementById('imageUpload');
    const uploadArea = document.querySelector('.upload-area');
    const initialUploadHTML = uploadArea.innerHTML;

    imageInput.addEventListener('change', () => {
        if (imageInput.files.length > 0) {
            const firstFile = imageInput.files[0];
            const reader = new FileReader();
            reader.onload = (e) => {
                const fileCountText = imageInput.files.length > 1
                    ? `${imageInput.files.length} imagens selecionadas`
                    : firstFile.name;
                uploadArea.innerHTML = `
                    <img src="${e.target.result}" alt="Pré-visualização" class="image-preview">
                    <p class="preview-info">${fileCountText}</p>
                    <button type="button" class="remove-selection-btn">Remover seleção</button>
                `;
                uploadArea.querySelector('.remove-selection-btn').addEventListener('click', (event) => {
                    event.preventDefault();
                    imageInput.value = '';
                    uploadArea.innerHTML = initialUploadHTML;
                });
            };
            reader.readAsDataURL(firstFile);
        } else {
            uploadArea.innerHTML = initialUploadHTML;
        }
    });

    async function fetchData(url) {
        try {
            const response = await fetch(url, { headers: apiHeaders });
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: `Erro HTTP ${response.status}` }));
                throw new Error(errorData.message);
            }
            return await response.json();
        } catch (error) {
            messageArea.textContent = `Não foi possível carregar dados: ${error.message}`;
            messageArea.className = 'message-area error';
            return [];
        }
    }

    function populateSelect(selectElement, items, placeholderText, valueField = 'id', textField = 'nome') {
        selectElement.innerHTML = `<option value="" disabled selected>${placeholderText}</option>`;
        if (items && items.length > 0) {
            items.forEach(item => {
                const option = document.createElement('option');
                option.value = item[valueField];
                option.textContent = item[textField];
                selectElement.appendChild(option);
            });
        }
    }

    function populateTags(container, items, isMultiSelect = false) {
        const placeholder = container.querySelector('.loading-text');
        if (placeholder) placeholder.remove();

        const existingTags = container.querySelectorAll('.tag-select, .tag-select-grid');
        existingTags.forEach(tag => tag.remove());

        if (items && items.length > 0) {
            items.forEach(item => {
                const tag = document.createElement('button');
                tag.type = 'button';
                tag.className = isMultiSelect ? 'tag-select-grid' : 'tag-select';
                tag.dataset.value = item.id;
                tag.textContent = item.nome;
                tag.addEventListener('click', () => {
                    if (!isMultiSelect) {
                        container.querySelectorAll('.tag-select.active').forEach(t => t.classList.remove('active'));
                        tag.classList.add('active');
                    } else {
                        tag.classList.toggle('active');
                    }
                });
                const addButton = container.querySelector('.btn-add-new');
                if (addButton) {
                    container.insertBefore(tag, addButton);
                } else {
                    container.appendChild(tag);
                }
            });
        }

        // Habilitar o botão "Adicionar novo" se existir
        const addNewBtn = container.querySelector('.btn-add-new');
        if (addNewBtn) {
            addNewBtn.disabled = false;
            addNewBtn.style.opacity = '1';
            addNewBtn.style.cursor = 'pointer';
        }
    }

    async function initializeForm() {
        
        const urlParams = new URLSearchParams(window.location.search);
        const inspectionId = urlParams.get('inspectionId');
        const ambientId = urlParams.get('ambientId');

        const fetchPromises = [
            fetchData('/api/inspecoes').then(data => {
                populateSelect(inspecaoSelect, data, 'Selecione uma inspeção...', 'id', 'nome');
                
                if (inspectionId) {
                    inspecaoSelect.value = inspectionId;
                    
                    inspecaoSelect.dispatchEvent(new Event('change'));
                }
            }),
            fetchData('/api/lookup/tipos-sistema').then(data => populateTags(systemTypeTagsContainer, data, false)),
            fetchData('/api/lookup/tipos-patologia').then(data => populateTags(pathologyTagsContainer, data, false))
        ];

        await Promise.all(fetchPromises);

        if (inspectionId && ambientId) {
            
            setTimeout(() => {
                ambienteSelect.value = ambientId;
            }, 500);
        }
    }

    inspecaoSelect.addEventListener('change', async () => {
        const id_inspecao = inspecaoSelect.value;
        ambienteSelect.innerHTML = '<option value="">Carregando...</option>';
        ambienteSelect.disabled = true;
        ambienteGroup.style.display = 'none';
        if (id_inspecao) {
            ambienteGroup.style.display = 'flex';
            const ambientes = await fetchData(`/api/inspecoes/${id_inspecao}/ambientes`);
            populateSelect(ambienteSelect, ambientes, 'Selecione um ambiente...', 'id', 'titulo');
            ambienteSelect.disabled = !ambientes || ambientes.length === 0;
        }
    });

    form.addEventListener('submit', async (event) => {
        event.preventDefault();
        const id_inspecao = inspecaoSelect.value;
        const id_ambiente = ambienteSelect.value;
        if (!id_inspecao || !id_ambiente) {
            showWarningToast('Por favor, selecione uma Inspeção e um Local (Ambiente) válidos.');
            return;
        }

        const selectedSystemTag = systemTypeTagsContainer.querySelector('.tag-select.active');
        const selectedPathologyTags = pathologyTagsContainer.querySelectorAll('.tag-select.active');

        const occurrenceData = {
            titulo: document.getElementById('title').value,
            dataocm: document.getElementById('dataOcorrencia').value,
            descricao: document.getElementById('description').value,
            id_inspecao: parseInt(id_inspecao),
            id_tipo_sistema: selectedSystemTag ? selectedSystemTag.dataset.value : null,
            id_tipo_patologia: selectedPathologyTags?.length > 0 ? selectedPathologyTags[0].dataset.value : null,
        };

        if (!occurrenceData.titulo || !occurrenceData.dataocm || !occurrenceData.id_tipo_sistema || !occurrenceData.id_tipo_patologia) {
            showWarningToast('Por favor, preencha todos os campos obrigatórios: Título, Data, Tipo de Sistema e pelo menos uma Patologia.');
            return;
        }

        try {
            const responseOcorrencia = await fetch(`/api/ambientes/${id_ambiente}/ocorrencias`, {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
                body: JSON.stringify(occurrenceData)
            });
            const ocorrenciaCriada = await responseOcorrencia.json();
            if (!responseOcorrencia.ok) throw new Error(ocorrenciaCriada.message || "Erro desconhecido ao criar ocorrência.");

            if (imageInput.files.length > 0) {
                const id_ocorrencia_criada = ocorrenciaCriada.id;
                const fotosFormData = new FormData();

                fotosFormData.append('id_ambiente', id_ambiente);
                fotosFormData.append('id_ocorrencia', id_ocorrencia_criada);

                Array.from(imageInput.files).forEach(file => {
                    fotosFormData.append('fotos', file);
                });

                try {
                    const responseFotos = await fetch('/api/fotos/upload/ambiente_ocorrencia', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                            
                        },
                        body: fotosFormData
                    });

                    if (!responseFotos.ok) {
                        throw new Error(`Erro ao enviar fotos: ${responseFotos.status}`);
                    }

                    const fotoResult = await responseFotos.json();
                } catch (error) {
                    throw new Error('Falha ao enviar as fotos');
                }
            }
            showSuccessToast("Ocorrência criada com sucesso! Redirecionando...", 2000);
            setTimeout(() => {
                window.location.href = `/inspection-details/${id_inspecao}`;
            }, 2000);
        } catch (error) {
            showErrorToast(`Falha no processo: ${error.message}`);
        }
    });

    // ===== FUNÇÕES DOS MODAIS =====

    // Modal de Tipo de Sistema
    function openSystemTypeModal() {
        document.getElementById('systemTypeModal').classList.add('active');
        document.getElementById('systemTypeName').focus();
    }

    function closeSystemTypeModal() {
        document.getElementById('systemTypeModal').classList.remove('active');
        document.getElementById('systemTypeForm').reset();
    }

    async function saveSystemType() {
        const nome = document.getElementById('systemTypeName').value.trim();
        const descricao = document.getElementById('systemTypeDescription').value.trim();

        if (!nome) {
            showWarningToast('Por favor, insira o nome do tipo de sistema.');
            return;
        }

        try {
            if (!token) {
                showErrorToast('Token de autenticação não encontrado. Faça login novamente.');
                window.location.href = '/';
                return;
            }

            console.log('Enviando requisição para criar tipo de sistema:', { nome, descricao });

            const response = await fetch('/api/lookup/tipos-sistema', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ nome, descricao })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('Erro na resposta:', response.status, errorData);
                throw new Error(errorData.message || `Erro ${response.status} ao criar tipo de sistema`);
            }

            const newSystemType = await response.json();

            // Recarregar os tipos de sistema
            const systemTypesResponse = await fetch('/api/lookup/tipos-sistema', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            const systemTypes = await systemTypesResponse.json();
            populateTags(document.getElementById('system-type-tags'), systemTypes, false);

            // Selecionar automaticamente o novo tipo criado
            setTimeout(() => {
                const newTag = document.querySelector(`#system-type-tags .tag-select[data-value="${newSystemType.id}"]`);
                if (newTag) {
                    newTag.click();
                }
            }, 100);

            closeSystemTypeModal();
            showSuccessToast('Tipo de sistema criado com sucesso!');
        } catch (error) {
            showErrorToast('Erro ao criar tipo de sistema: ' + error.message);
        }
    }

    // Modal de Tipo de Patologia
    function openPathologyTypeModal() {
        document.getElementById('pathologyTypeModal').classList.add('active');
        document.getElementById('pathologyTypeName').focus();
    }

    function closePathologyTypeModal() {
        document.getElementById('pathologyTypeModal').classList.remove('active');
        document.getElementById('pathologyTypeForm').reset();
    }

    async function savePathologyType() {
        const nome = document.getElementById('pathologyTypeName').value.trim();
        const descricao = document.getElementById('pathologyTypeDescription').value.trim();

        if (!nome) {
            showWarningToast('Por favor, insira o nome da patologia.');
            return;
        }

        try {
            if (!token) {
                showErrorToast('Token de autenticação não encontrado. Faça login novamente.');
                window.location.href = '/';
                return;
            }

            console.log('Enviando requisição para criar tipo de patologia:', { nome, descricao });

            const response = await fetch('/api/lookup/tipos-patologia', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ nome, descricao })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('Erro na resposta:', response.status, errorData);
                throw new Error(errorData.message || `Erro ${response.status} ao criar tipo de patologia`);
            }

            const newPathologyType = await response.json();

            // Recarregar os tipos de patologia
            const pathologyTypesResponse = await fetch('/api/lookup/tipos-patologia', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            const pathologyTypes = await pathologyTypesResponse.json();
            populateTags(document.getElementById('pathology-tags'), pathologyTypes, false);

            // Selecionar automaticamente o novo tipo criado
            setTimeout(() => {
                const newTag = document.querySelector(`#pathology-tags .tag-select-grid[data-value="${newPathologyType.id}"]`);
                if (newTag) {
                    newTag.click();
                }
            }, 100);

            closePathologyTypeModal();
            showSuccessToast('Tipo de patologia criado com sucesso!');
        } catch (error) {
            showErrorToast('Erro ao criar tipo de patologia: ' + error.message);
        }
    }

    // Tornar as funções globais para serem acessíveis pelos botões HTML
    window.openSystemTypeModal = openSystemTypeModal;
    window.closeSystemTypeModal = closeSystemTypeModal;
    window.saveSystemType = saveSystemType;
    window.openPathologyTypeModal = openPathologyTypeModal;
    window.closePathologyTypeModal = closePathologyTypeModal;
    window.savePathologyType = savePathologyType;

    // Event listeners para os botões "Adicionar novo"
    document.querySelector('#system-type-tags .btn-add-new').addEventListener('click', openSystemTypeModal);
    document.querySelector('.btn-add-new-square').addEventListener('click', openPathologyTypeModal);

    initializeForm();
});

// Fechar modais ao clicar fora
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal-overlay')) {
        if (event.target.id === 'systemTypeModal') {
            window.closeSystemTypeModal();
        } else if (event.target.id === 'pathologyTypeModal') {
            window.closePathologyTypeModal();
        }
    }
});

// Fechar modais com ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        window.closeSystemTypeModal();
        window.closePathologyTypeModal();
    }
});