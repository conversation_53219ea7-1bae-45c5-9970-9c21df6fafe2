let map;
let markers = [];
let inspectionsData = [];
let currentInspection = null;

// Inicializar mapa
function initMap() {
  // Coordenadas do Brasil (centro aproximado)
  const brazilCenter = [-14.2350, -51.9253];
  
  map = L.map('map').setView(brazilCenter, 4);
  
  // Adicionar tiles do OpenStreetMap
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors',
    maxZoom: 19
  }).addTo(map);
  
  // Carregar dados das inspeções
  loadInspections();
}

// Carregar inspeções com coordenadas
async function loadInspections() {
  try {
    const token = localStorage.getItem('authToken');
    if (!token) {
      window.location.href = '/';
      return;
    }

    const response = await fetch('/map/api/inspections', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 401) {
      window.location.href = '/';
      return;
    }

    if (!response.ok) {
      throw new Error('Erro ao carregar inspeções');
    }

    const data = await response.json();
    
    if (data.success) {
      inspectionsData = data.data;
      updateMapInfo(data);
      addMarkersToMap(inspectionsData);
      hideLoading();
      
      // Centralizar mapa nas inspeções se houver dados
      if (inspectionsData.length > 0) {
        centerMapOnInspections();
      }
    } else {
      throw new Error(data.message || 'Erro ao carregar dados');
    }
    
  } catch (error) {
    console.error('Erro ao carregar inspeções:', error);
    hideLoading();
    showError('Erro ao carregar dados do mapa: ' + error.message);
  }
}

// Atualizar informações do mapa
function updateMapInfo(data) {
  document.getElementById('total-inspections').textContent = 
    (data.data.length + data.totalWithoutCoordinates).toString();
  document.getElementById('located-inspections').textContent = 
    data.data.length.toString();
  document.getElementById('unlocated-inspections').textContent = 
    data.totalWithoutCoordinates.toString();
}

// Adicionar marcadores ao mapa
function addMarkersToMap(inspections) {
  // Limpar marcadores existentes
  markers.forEach(marker => map.removeLayer(marker));
  markers = [];
  
  inspections.forEach(inspection => {
    if (inspection.latitude && inspection.longitude) {
      const marker = createMarker(inspection);
      markers.push(marker);
      marker.addTo(map);
    }
  });
}

// Criar marcador para inspeção
function createMarker(inspection) {
  const statusColor = getStatusColor(inspection.status);
  
  // Criar ícone customizado
  const customIcon = L.divIcon({
    className: 'custom-marker',
    html: `<div style="
      width: 20px;
      height: 20px;
      background-color: ${statusColor};
      border: 2px solid white;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    "></div>`,
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  });
  
  const marker = L.marker([inspection.latitude, inspection.longitude], {
    icon: customIcon
  });
  
  // Adicionar popup
  const popupContent = `
    <div style="min-width: 200px;">
      <h4 style="margin: 0 0 8px 0; color: #333;">${inspection.nome}</h4>
      <p style="margin: 4px 0; color: #666;"><strong>Status:</strong> ${inspection.status}</p>
      <p style="margin: 4px 0; color: #666;"><strong>Endereço:</strong> ${inspection.endereco_completo}</p>
      <div style="margin-top: 12px; display: flex; gap: 8px;">
        <button onclick="openInspectionModal(${inspection.id})" 
                style="background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
          Ver Detalhes
        </button>
        <button onclick="openGPSForInspection(${inspection.latitude}, ${inspection.longitude}, '${inspection.endereco_completo}')" 
                style="background: #10b981; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
          Abrir GPS
        </button>
      </div>
    </div>
  `;
  
  marker.bindPopup(popupContent);
  
  // Adicionar evento de clique
  marker.on('click', () => {
    currentInspection = inspection;
  });
  
  return marker;
}

// Obter cor do status
function getStatusColor(status) {
  const statusNormalized = status?.toLowerCase().replace(/\s+/g, '-') || 'aberta';
  
  switch (statusNormalized) {
    case 'concluida':
    case 'concluída':
      return '#10b981'; // Verde
    case 'em-andamento':
      return '#f59e0b'; // Amarelo
    case 'aberta':
    default:
      return '#ef4444'; // Vermelho
  }
}

// Centralizar mapa nas inspeções
function centerMapOnInspections() {
  if (inspectionsData.length === 0) return;
  
  const group = new L.featureGroup(markers);
  map.fitBounds(group.getBounds().pad(0.1));
}

// Abrir modal de detalhes da inspeção
function openInspectionModal(inspectionId) {
  const inspection = inspectionsData.find(i => i.id === inspectionId);
  if (!inspection) return;
  
  currentInspection = inspection;
  
  document.getElementById('modal-name').textContent = inspection.nome;
  document.getElementById('modal-status').textContent = inspection.status;
  document.getElementById('modal-address').textContent = inspection.endereco_completo;
  document.getElementById('modal-date').textContent = 
    new Date(inspection.criado_em).toLocaleDateString('pt-BR');
  
  document.getElementById('inspection-modal').classList.add('active');
}

// Fechar modal
function closeInspectionModal() {
  document.getElementById('inspection-modal').classList.remove('active');
  currentInspection = null;
}

// Abrir GPS
function openGPS() {
  if (!currentInspection) return;
  openGPSForInspection(
    currentInspection.latitude, 
    currentInspection.longitude, 
    currentInspection.endereco_completo
  );
}

// Abrir GPS para inspeção específica
function openGPSForInspection(lat, lng, address) {
  // Detectar dispositivo e abrir app apropriado
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  if (/android/i.test(userAgent)) {
    // Android - Google Maps
    window.open(`https://maps.google.com/maps?daddr=${lat},${lng}`, '_blank');
  } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    // iOS - Apple Maps
    window.open(`https://maps.apple.com/?daddr=${lat},${lng}`, '_blank');
  } else {
    // Desktop - Google Maps
    window.open(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`, '_blank');
  }
}

// Ver detalhes da inspeção
function viewInspectionDetails() {
  if (!currentInspection) return;
  window.location.href = `/inspection-details/${currentInspection.id}`;
}

// Atualizar mapa
function refreshMap() {
  showLoading();
  loadInspections();
}

// Centralizar mapa
function centerMap() {
  centerMapOnInspections();
}

// Mostrar loading
function showLoading() {
  document.getElementById('map-loading').style.display = 'flex';
}

// Esconder loading
function hideLoading() {
  document.getElementById('map-loading').style.display = 'none';
}

// Mostrar erro
function showError(message) {
  alert(message); // Pode ser substituído por um toast mais elegante
}

// Navegação
function navigateTo(url) {
  window.location.href = url;
}

// Fechar modal ao clicar fora
document.getElementById('inspection-modal').addEventListener('click', (e) => {
  if (e.target.id === 'inspection-modal') {
    closeInspectionModal();
  }
});

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
  // Carregar Leaflet CSS e JS
  const leafletCSS = document.createElement('link');
  leafletCSS.rel = 'stylesheet';
  leafletCSS.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
  document.head.appendChild(leafletCSS);
  
  const leafletJS = document.createElement('script');
  leafletJS.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
  leafletJS.onload = initMap;
  document.head.appendChild(leafletJS);
});
