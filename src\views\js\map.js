let map;
let markers = [];
let inspectionsData = [];
let currentInspection = null;
let loadingTimeout;

// Cache para melhorar performance
const mapCache = {
  inspections: null,
  lastUpdate: null,
  cacheTime: 5 * 60 * 1000 // 5 minutos
};

// Inicializar mapa com loading otimizado
function initMap() {
  // Mostrar loading
  showLoading(true);

  // Coordenadas do Brasil (centro aproximado)
  const brazilCenter = [-14.2350, -51.9253];

  map = L.map('map', {
    preferCanvas: true, // Melhor performance para muitos marcadores
    zoomControl: false // Remover controles padrão para adicionar customizados
  }).setView(brazilCenter, 4);

  // Adicionar controles customizados
  L.control.zoom({
    position: 'bottomright'
  }).addTo(map);

  // Adicionar tiles do OpenStreetMap com configurações otimizadas
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors',
    maxZoom: 19,
    minZoom: 3,
    updateWhenIdle: true, // Atualizar apenas quando parar de mover
    keepBuffer: 2 // Manter tiles em cache
  }).addTo(map);

  // Carregar dados das inspeções de forma assíncrona
  setTimeout(() => {
    loadInspections();
  }, 100);
}

// Carregar inspeções com coordenadas (otimizado com cache)
async function loadInspections() {
  try {
    // Verificar cache primeiro
    const now = Date.now();
    if (mapCache.inspections && mapCache.lastUpdate &&
        (now - mapCache.lastUpdate) < mapCache.cacheTime) {
      console.log('Usando dados do cache');
      processInspectionsData(mapCache.inspections);
      showLoading(false);
      return;
    }

    const token = localStorage.getItem('authToken');
    if (!token) {
      window.location.href = '/';
      return;
    }

    // Timeout para requisições lentas
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 segundos

    const response = await fetch('/map/api/inspections', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.status === 401) {
      window.location.href = '/';
      return;
    }

    if (!response.ok) {
      throw new Error('Erro ao carregar inspeções');
    }

    const data = await response.json();

    if (data.success) {
      // Salvar no cache
      mapCache.inspections = data.data;
      mapCache.lastUpdate = now;

      processInspectionsData(data.data);

    } else {
      throw new Error(data.message || 'Erro ao carregar dados');
    }
    
  } catch (error) {
    console.error('Erro ao carregar inspeções:', error);
    showError('Erro ao carregar dados do mapa: ' + error.message);
  } finally {
    showLoading(false);
  }
}

// Processar dados das inspeções (função separada para reutilização)
function processInspectionsData(data) {
  inspectionsData = data;

  // Atualizar estatísticas de forma otimizada
  updateMapStats(data);

  // Adicionar marcadores ao mapa de forma assíncrona
  requestAnimationFrame(() => {
    addMarkersToMap(data);

    // Ajustar zoom para mostrar todos os marcadores
    if (markers.length > 0) {
      setTimeout(() => {
        const group = new L.featureGroup(markers);
        map.fitBounds(group.getBounds().pad(0.1));
      }, 100);
    }
  });
}

// Atualizar estatísticas do mapa (otimizado)
function updateMapStats(data) {
  const totalElement = document.getElementById('total-inspections');
  const locatedElement = document.getElementById('located-inspections');
  const unlocatedElement = document.getElementById('unlocated-inspections');

  if (totalElement) totalElement.textContent = data.length.toString();
  if (locatedElement) locatedElement.textContent = data.filter(i => i.latitude && i.longitude).length.toString();
  if (unlocatedElement) unlocatedElement.textContent = data.filter(i => !i.latitude || !i.longitude).length.toString();
}

// Adicionar marcadores ao mapa (otimizado com batching)
function addMarkersToMap(inspections) {
  // Limpar marcadores existentes de forma eficiente
  if (markers.length > 0) {
    const markerGroup = new L.featureGroup(markers);
    map.removeLayer(markerGroup);
  }
  markers = [];

  // Filtrar inspeções com coordenadas
  const validInspections = inspections.filter(i => i.latitude && i.longitude);

  // Criar marcadores em lotes para melhor performance
  const batchSize = 50;
  let currentBatch = 0;

  function processBatch() {
    const start = currentBatch * batchSize;
    const end = Math.min(start + batchSize, validInspections.length);

    for (let i = start; i < end; i++) {
      const inspection = validInspections[i];
      const marker = createMarker(inspection);
      markers.push(marker);
      marker.addTo(map);
    }

    currentBatch++;

    // Continuar processamento se houver mais dados
    if (end < validInspections.length) {
      requestAnimationFrame(processBatch);
    }
  }

  // Iniciar processamento
  if (validInspections.length > 0) {
    processBatch();
  }
}

// Cache de ícones para melhor performance
const iconCache = new Map();

// Criar marcador para inspeção (otimizado)
function createMarker(inspection) {
  const statusColor = getStatusColor(inspection.status);

  // Usar cache de ícones
  let customIcon = iconCache.get(statusColor);
  if (!customIcon) {
    customIcon = L.divIcon({
      className: 'custom-marker',
      html: `<div class="marker-dot" style="background-color: ${statusColor}"></div>`,
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });
    iconCache.set(statusColor, customIcon);
  }
  
  const marker = L.marker([inspection.latitude, inspection.longitude], {
    icon: customIcon
  });
  
  // Adicionar popup
  const popupContent = `
    <div style="min-width: 200px;">
      <h4 style="margin: 0 0 8px 0; color: #333;">${inspection.nome}</h4>
      <p style="margin: 4px 0; color: #666;"><strong>Status:</strong> ${inspection.status}</p>
      <p style="margin: 4px 0; color: #666;"><strong>Endereço:</strong> ${inspection.endereco_completo}</p>
      <div style="margin-top: 12px; display: flex; gap: 8px;">
        <button onclick="openInspectionModal(${inspection.id})" 
                style="background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
          Ver Detalhes
        </button>
        <button onclick="openGPSForInspection(${inspection.latitude}, ${inspection.longitude}, '${inspection.endereco_completo}')" 
                style="background: #10b981; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
          Abrir GPS
        </button>
      </div>
    </div>
  `;
  
  marker.bindPopup(popupContent);
  
  // Adicionar evento de clique
  marker.on('click', () => {
    currentInspection = inspection;
  });
  
  return marker;
}

// Obter cor do status
function getStatusColor(status) {
  const statusNormalized = status?.toLowerCase().replace(/\s+/g, '-') || 'aberta';
  
  switch (statusNormalized) {
    case 'concluida':
    case 'concluída':
      return '#10b981'; // Verde
    case 'em-andamento':
      return '#f59e0b'; // Amarelo
    case 'aberta':
    default:
      return '#ef4444'; // Vermelho
  }
}

// Centralizar mapa nas inspeções
function centerMapOnInspections() {
  if (inspectionsData.length === 0) return;
  
  const group = new L.featureGroup(markers);
  map.fitBounds(group.getBounds().pad(0.1));
}

// Abrir modal de detalhes da inspeção
function openInspectionModal(inspectionId) {
  const inspection = inspectionsData.find(i => i.id === inspectionId);
  if (!inspection) return;
  
  currentInspection = inspection;
  
  document.getElementById('modal-name').textContent = inspection.nome;
  document.getElementById('modal-status').textContent = inspection.status;
  document.getElementById('modal-address').textContent = inspection.endereco_completo;
  document.getElementById('modal-date').textContent = 
    new Date(inspection.criado_em).toLocaleDateString('pt-BR');
  
  document.getElementById('inspection-modal').classList.add('active');
}

// Fechar modal
function closeInspectionModal() {
  document.getElementById('inspection-modal').classList.remove('active');
  currentInspection = null;
}

// Abrir GPS
function openGPS() {
  if (!currentInspection) return;
  openGPSForInspection(
    currentInspection.latitude, 
    currentInspection.longitude, 
    currentInspection.endereco_completo
  );
}

// Abrir GPS para inspeção específica
function openGPSForInspection(lat, lng, address) {
  // Detectar dispositivo e abrir app apropriado
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  if (/android/i.test(userAgent)) {
    // Android - Google Maps
    window.open(`https://maps.google.com/maps?daddr=${lat},${lng}`, '_blank');
  } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    // iOS - Apple Maps
    window.open(`https://maps.apple.com/?daddr=${lat},${lng}`, '_blank');
  } else {
    // Desktop - Google Maps
    window.open(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`, '_blank');
  }
}

// Ver detalhes da inspeção
function viewInspectionDetails() {
  if (!currentInspection) return;
  window.location.href = `/inspection-details/${currentInspection.id}`;
}

// Atualizar mapa
function refreshMap() {
  showLoading();
  loadInspections();
}

// Centralizar mapa
function centerMap() {
  centerMapOnInspections();
}

// Mostrar/esconder loading (otimizado)
function showLoading(show = true) {
  const loadingElement = document.getElementById('map-loading');
  if (loadingElement) {
    loadingElement.style.display = show ? 'flex' : 'none';
  }
}

// Mostrar erro com toast elegante
function showError(message) {
  // Criar toast de erro
  const toast = document.createElement('div');
  toast.className = 'error-toast';
  toast.textContent = message;
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ef4444;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    font-weight: 500;
    max-width: 300px;
    animation: slideIn 0.3s ease-out;
  `;

  document.body.appendChild(toast);

  // Remover após 5 segundos
  setTimeout(() => {
    toast.style.animation = 'slideOut 0.3s ease-in';
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }, 5000);
}

// Navegação
function navigateTo(url) {
  window.location.href = url;
}

// Fechar modal ao clicar fora
document.getElementById('inspection-modal').addEventListener('click', (e) => {
  if (e.target.id === 'inspection-modal') {
    closeInspectionModal();
  }
});

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
  // Carregar Leaflet CSS e JS
  const leafletCSS = document.createElement('link');
  leafletCSS.rel = 'stylesheet';
  leafletCSS.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
  document.head.appendChild(leafletCSS);
  
  const leafletJS = document.createElement('script');
  leafletJS.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
  leafletJS.onload = initMap;
  document.head.appendChild(leafletJS);
});
