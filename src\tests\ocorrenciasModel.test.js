const db = require('../config/db');
jest.mock('../config/db'); 

const Ocorrencia = require('../models/ocorrenciasModel');

describe('Model: Ocorrencia', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar uma ocorrência', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Teste Ocorrencia' }] });
    const result = await Ocorrencia.create({
      titulo: 'Teste Ocorrencia',
      id_inspecao: 1,
      id_ambiente: 1,
      id_tipo_sistema: 1,
      id_tipo_patologia: 1,
      descricao: 'desc',
      concluido_em: null
    });
    expect(result.titulo).toBe('Teste Ocorrencia');
  });

  it('deve tratar erro ao criar ocorrência', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao criar ocorrência no modelo:'));
    await expect(Ocorrencia.create({
      titulo: 'Teste',
      id_inspecao: 1,
      id_ambiente: 1,
      id_tipo_sistema: 1,
      id_tipo_patologia: 1,
      descricao: 'desc',
      concluido_em: null
    })).rejects.toThrow('Erro ao criar ocorrência no modelo:');
  });

  it('deve buscar ocorrência por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Ocorrencia X' }] });
    const result = await Ocorrencia.findById(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar undefined se ocorrência não encontrada', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Ocorrencia.findById(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao buscar ocorrência por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar ocorrência detalhada por ID:'));
    await expect(Ocorrencia.findById(1)).rejects.toThrow('Erro ao buscar ocorrência detalhada por ID:');
  });

  it('deve atualizar uma ocorrência', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Atualizada' }] });
    const result = await Ocorrencia.update(1, {
      titulo: 'Atualizada',
      id_tipo_sistema: 1,
      id_tipo_patologia: 1,
      descricao: 'desc',
      concluido_em: null,
      dataocm: null
    });
    expect(result.titulo).toBe('Atualizada');
  });

  it('deve tratar erro ao atualizar ocorrência', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar ocorrência:'));
    await expect(Ocorrencia.update(1, {
      titulo: 'Atualizada',
      id_tipo_sistema: 1,
      id_tipo_patologia: 1,
      descricao: 'desc',
      concluido_em: null,
      dataocm: null
    })).rejects.toThrow('Erro ao atualizar ocorrência:');
  });

  it('deve remover uma ocorrência', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Ocorrencia.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve tratar erro ao remover ocorrência', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover ocorrência:'));
    await expect(Ocorrencia.remove(1)).rejects.toThrow('Erro ao remover ocorrência:');
  });

  it('deve buscar todas as ocorrências por ambiente', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Ocorrencia X' }] });
    const result = await Ocorrencia.findAllByAmbienteId(1);
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].titulo).toBe('Ocorrencia X');
  });

  it('deve tratar erro ao buscar ocorrências por ambiente', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar ocorrências por ID do ambiente:'));
    await expect(Ocorrencia.findAllByAmbienteId(1)).rejects.toThrow('Erro ao buscar ocorrências por ID do ambiente:');
  });
});