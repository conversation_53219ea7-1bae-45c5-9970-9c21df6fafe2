document.addEventListener('DOMContentLoaded', () => {
  const addUserForm = document.getElementById('addUserForm');
  const messageArea = document.getElementById('messageArea');

  const token = localStorage.getItem('authToken');
  if (!token) {
    showErrorToast("Acesso negado. Apenas usuários autenticados podem adicionar novos usuários.");
    setTimeout(() => {
      window.location.href = '/';
    }, 2000);
    return;
  }

  addUserForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    messageArea.textContent = '';
    messageArea.className = 'message-area';

    const nome = document.getElementById('nome').value;
    const email = document.getElementById('email').value;
    const senha = document.getElementById('senha').value;
    const confirmarSenha = document.getElementById('confirmarSenha').value;

    if (!nome || !email || !senha || !confirmarSenha) {
      showWarningToast('Todos os campos são obrigatórios.');
      showMessage('Todos os campos são obrigatórios.', 'error');
      return;
    }

    if (senha.length < 8) {
      showWarningToast('A senha deve ter no mínimo 8 caracteres.');
      showMessage('A senha deve ter no mínimo 8 caracteres.', 'error');
      return;
    }

    if (senha !== confirmarSenha) {
      showWarningToast('As senhas não coincidem.');
      showMessage('As senhas não coincidem.', 'error');
      return;
    }

    try {
      const response = await fetch('/api/usuarios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ nome, email, senha })
      });

      const data = await response.json();

      if (response.ok) {
        showSuccessToast('Usuário criado com sucesso!');
        showMessage('Usuário criado com sucesso!', 'success');
        addUserForm.reset();
      } else {
        showErrorToast(data.message || 'Ocorreu um erro ao criar o usuário.');
        showMessage(data.message || 'Ocorreu um erro ao criar o usuário.', 'error');
      }
    } catch (error) {
      showErrorToast('Falha na comunicação com o servidor. Tente novamente.');
      showMessage('Falha na comunicação com o servidor. Tente novamente.', 'error');
    }
  });

  function showMessage(message, type) {
    messageArea.textContent = message;
    messageArea.className = `message-area ${type}`;
  }
});