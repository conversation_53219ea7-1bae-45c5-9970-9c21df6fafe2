const OcorrenciaModel = require('../models/ocorrenciasModel');
const AmbienteModel = require('../models/ambientesModel');
const InspecaoModel = require('../models/inspecoesModel');
const TipoSistemaModel = require('../models/tipoSistemaModel');
const TipoPatologiaModel = require('../models/tipoPatologiaModel');

class OcorrenciasService {
    
    async validarDadosCriacao(dadosOcorrencia) {
        const { titulo, id_inspecao, id_ambiente, id_tipo_sistema, id_tipo_patologia, descricao, dataocm } = dadosOcorrencia;

        if (!titulo || !id_inspecao || !id_tipo_patologia) {
            throw new Error('Campos obrigatórios: titulo, id_inspecao, id_tipo_patologia.');
        }

        if (titulo.trim().length === 0) {
            throw new Error('Tí<PERSON><PERSON> da ocorrência não pode estar vazio.');
        }

        const inspecao = await InspecaoModel.findById(id_inspecao);
        if (!inspecao) {
            throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
        }

        if (id_ambiente) {
            const ambiente = await AmbienteModel.findById(id_ambiente);
            if (!ambiente) {
                throw new Error(`Ambiente com ID ${id_ambiente} não encontrado.`);
            }
            
            if (ambiente.id_inspecao !== parseInt(id_inspecao)) {
                throw new Error(`O ambiente ${id_ambiente} não pertence à inspeção ${id_inspecao}.`);
            }
        }

        const tipoPatologia = await TipoPatologiaModel.findById(id_tipo_patologia);
        if (!tipoPatologia) {
            throw new Error('Tipo de patologia não encontrado.');
        }

        if (id_tipo_sistema) {
            const tipoSistema = await TipoSistemaModel.findById(id_tipo_sistema);
            if (!tipoSistema) {
                throw new Error('Tipo de sistema não encontrado.');
            }
        }

        if (dataocm) {
            const data = new Date(dataocm);
            if (isNaN(data.getTime())) {
                throw new Error('Data da ocorrência inválida.');
            }

            if (data > new Date()) {
                throw new Error('Data da ocorrência não pode ser futura.');
            }
        }

        return {
            titulo: titulo.trim(),
            id_inspecao: parseInt(id_inspecao),
            id_ambiente: id_ambiente ? parseInt(id_ambiente) : null,
            id_tipo_sistema: id_tipo_sistema || null,
            id_tipo_patologia: id_tipo_patologia,
            descricao: descricao?.trim() || null,
            concluido_em: dataocm || null
        };
    }

    async criarOcorrenciaNoAmbiente(id_ambiente, dadosOcorrencia) {
        try {
            
            const dadosValidados = await this.validarDadosCriacao({
                ...dadosOcorrencia,
                id_ambiente
            });

            const novaOcorrencia = await OcorrenciaModel.create(dadosValidados);
            return novaOcorrencia;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] criarOcorrenciaNoAmbiente:', error);
            throw error;
        }
    }

    async listarOcorrenciasPorInspecao(id_inspecao) {
        try {
            
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
            }

            const ocorrencias = await OcorrenciaModel.findAllByInspecaoId(id_inspecao);
            return ocorrencias;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] listarOcorrenciasPorInspecao:', error);
            throw error;
        }
    }

    async listarOcorrenciasPorAmbiente(id_ambiente) {
        try {
            
            const ambiente = await AmbienteModel.findById(id_ambiente);
            if (!ambiente) {
                throw new Error(`Ambiente com ID ${id_ambiente} não encontrado.`);
            }

            const ocorrencias = await OcorrenciaModel.findAllByAmbienteId(id_ambiente);
            return ocorrencias;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] listarOcorrenciasPorAmbiente:', error);
            throw error;
        }
    }

    async obterOcorrenciaPorId(id_ocorrencia) {
        try {
            const ocorrencia = await OcorrenciaModel.findById(id_ocorrencia);
            if (!ocorrencia) {
                throw new Error('Ocorrência não encontrada.');
            }
            return ocorrencia;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] obterOcorrenciaPorId:', error);
            throw error;
        }
    }

    async validarDadosAtualizacao(dadosOcorrencia) {
        const { titulo, id_tipo_sistema, id_tipo_patologia, descricao, dataocm, grau_urgencia } = dadosOcorrencia;

        const dadosValidados = {};

        if (titulo !== undefined) {
            if (!titulo || titulo.trim().length === 0) {
                throw new Error('Título da ocorrência não pode estar vazio.');
            }
            dadosValidados.titulo = titulo.trim();
        }

        if (id_tipo_sistema !== undefined) {
            if (id_tipo_sistema) {
                const tipoSistema = await TipoSistemaModel.findById(id_tipo_sistema);
                if (!tipoSistema) {
                    throw new Error('Tipo de sistema não encontrado.');
                }
                dadosValidados.id_tipo_sistema = parseInt(id_tipo_sistema);
            } else {
                dadosValidados.id_tipo_sistema = null;
            }
        }

        if (id_tipo_patologia !== undefined) {
            const tipoPatologia = await TipoPatologiaModel.findById(id_tipo_patologia);
            if (!tipoPatologia) {
                throw new Error('Tipo de patologia não encontrado.');
            }
            dadosValidados.id_tipo_patologia = parseInt(id_tipo_patologia);
        }

        if (descricao !== undefined) {
            dadosValidados.descricao = descricao?.trim() || null;
        }

        if (dataocm !== undefined) {
            if (dataocm) {
                const data = new Date(dataocm);
                if (isNaN(data.getTime())) {
                    throw new Error('Data da ocorrência inválida.');
                }

                if (data > new Date()) {
                    throw new Error('Data da ocorrência não pode ser futura.');
                }
                dadosValidados.dataocm = dataocm;
            } else {
                dadosValidados.dataocm = null;
            }
        }

        if (grau_urgencia !== undefined) {
            const grausValidos = ['baixo', 'medio', 'alto', 'urgente'];
            if (grau_urgencia && !grausValidos.includes(grau_urgencia.toLowerCase())) {
                throw new Error('Grau de urgência deve ser: baixo, medio, alto ou urgente.');
            }
            dadosValidados.grau_urgencia = grau_urgencia?.toLowerCase() || null;
        }

        return dadosValidados;
    }

    async atualizarOcorrencia(id_ocorrencia, dadosOcorrencia) {
        try {
            
            await this.obterOcorrenciaPorId(id_ocorrencia);

            const dadosValidados = await this.validarDadosAtualizacao(dadosOcorrencia);

            const ocorrenciaAtualizada = await OcorrenciaModel.update(id_ocorrencia, dadosValidados);
            if (!ocorrenciaAtualizada) {
                throw new Error('Ocorrência não encontrada para atualização.');
            }

            return ocorrenciaAtualizada;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] atualizarOcorrencia:', error);
            throw error;
        }
    }

    async verificarPodeDeletar(id_ocorrencia) {
        try {

            return true;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] verificarPodeDeletar:', error);
            throw error;
        }
    }

    async deletarOcorrencia(id_ocorrencia) {
        try {
            
            await this.obterOcorrenciaPorId(id_ocorrencia);

            await this.verificarPodeDeletar(id_ocorrencia);

            const deletada = await OcorrenciaModel.remove(id_ocorrencia);
            if (!deletada) {
                throw new Error('Ocorrência não encontrada para deleção.');
            }

            return true;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] deletarOcorrencia:', error);
            throw error;
        }
    }

    async obterEstatisticasPorInspecao(id_inspecao) {
        try {
            const ocorrencias = await this.listarOcorrenciasPorInspecao(id_inspecao);
            
            const estatisticas = {
                total: ocorrencias.length,
                porUrgencia: {
                    baixo: 0,
                    medio: 0,
                    alto: 0,
                    urgente: 0
                },
                porTipoPatologia: {},
                porTipoSistema: {}
            };

            ocorrencias.forEach(ocorrencia => {
                
                const urgencia = ocorrencia.grau_urgencia?.toLowerCase() || 'baixo';
                if (estatisticas.porUrgencia[urgencia] !== undefined) {
                    estatisticas.porUrgencia[urgencia]++;
                }

                const tipoPatologia = ocorrencia.tipo_patologia || 'Não especificado';
                estatisticas.porTipoPatologia[tipoPatologia] = (estatisticas.porTipoPatologia[tipoPatologia] || 0) + 1;

                const tipoSistema = ocorrencia.tipo_sistema || 'Não especificado';
                estatisticas.porTipoSistema[tipoSistema] = (estatisticas.porTipoSistema[tipoSistema] || 0) + 1;
            });

            return estatisticas;
        } catch (error) {
            console.error('[OCORRENCIAS_SERVICE_ERROR] obterEstatisticasPorInspecao:', error);
            throw error;
        }
    }
}

module.exports = new OcorrenciasService();