const express = require('express');
const router = express.Router();
const projetosController = require('../controllers/projetosController');

// Rotas para projetos
router.get('/', projetosController.listarProjetos);
router.post('/', projetosController.criarProjeto);
router.get('/estatisticas', projetosController.obterEstatisticasProjetos);
router.get('/codigo/:codigo_projeto', projetosController.obterProjetoPorCodigo);
router.get('/:id', projetosController.obterProjetoPorId);
router.put('/:id', projetosController.atualizarProjeto);
router.delete('/:id', projetosController.deletarProjeto);

module.exports = router;
