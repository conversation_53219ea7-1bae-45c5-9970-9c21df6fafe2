const axios = require('axios');

class GeocodingService {
    
    /**
     * Converte CEP em coordenadas usando ViaCEP + Nominatim
     */
    async getCepCoordinates(cep) {
        try {
            const cepLimpo = cep.replace(/\D/g, '');
            if (cepLimpo.length !== 8) {
                throw new Error('CEP deve ter 8 dígitos');
            }

            // Buscar endereço pelo CEP
            const viaCepResponse = await axios.get(`https://viacep.com.br/ws/${cepLimpo}/json/`, {
                timeout: 10000, // 10 segundos
                headers: {
                    'User-Agent': 'InsPecT-App/1.0'
                }
            });
            
            if (viaCepResponse.data.erro) {
                throw new Error('CEP não encontrado');
            }

            const endereco = viaCepResponse.data;
            
            // Montar query para geocoding
            const query = `${endereco.logradouro}, ${endereco.bairro}, ${endereco.localidade}, ${endereco.uf}, Brasil`;
            
            // Usar Nominatim para obter coordenadas
            const nominatimResponse = await axios.get('https://nominatim.openstreetmap.org/search', {
                params: {
                    q: query,
                    format: 'json',
                    limit: 1,
                    countrycodes: 'br'
                },
                headers: {
                    'User-Agent': 'InsPecT-App/1.0'
                },
                timeout: 10000 // 10 segundos de timeout
            });

            if (nominatimResponse.data.length === 0) {
                // Tentar busca mais genérica apenas com cidade e estado
                const genericQuery = `${endereco.localidade}, ${endereco.uf}, Brasil`;
                const fallbackResponse = await axios.get('https://nominatim.openstreetmap.org/search', {
                    params: {
                        q: genericQuery,
                        format: 'json',
                        limit: 1,
                        countrycodes: 'br'
                    },
                    headers: {
                        'User-Agent': 'InsPecT-App/1.0'
                    },
                    timeout: 10000
                });

                if (fallbackResponse.data.length === 0) {
                    throw new Error('Coordenadas não encontradas para este endereço');
                }

                const fallbackLocation = fallbackResponse.data[0];
                return {
                    latitude: parseFloat(fallbackLocation.lat),
                    longitude: parseFloat(fallbackLocation.lon),
                    endereco: {
                        logradouro: endereco.logradouro,
                        bairro: endereco.bairro,
                        localidade: endereco.localidade,
                        uf: endereco.uf,
                        cep: cepLimpo
                    },
                    precisao: 'cidade' // Indicar que é uma localização aproximada
                };
            }

            const location = nominatimResponse.data[0];

            return {
                latitude: parseFloat(location.lat),
                longitude: parseFloat(location.lon),
                endereco: {
                    logradouro: endereco.logradouro,
                    bairro: endereco.bairro,
                    cidade: endereco.localidade,
                    uf: endereco.uf,
                    cep: endereco.cep
                },
                precisao: 'endereco' // Localização precisa
            };
        } catch (error) {
            console.error('Erro ao obter coordenadas do CEP:', error);
            throw error;
        }
    }

    /**
     * Converte endereço completo em coordenadas
     */
    async getAddressCoordinates(endereco, numero = '') {
        try {
            const query = `${endereco}${numero ? ', ' + numero : ''}, Brasil`;
            
            const nominatimResponse = await axios.get('https://nominatim.openstreetmap.org/search', {
                params: {
                    q: query,
                    format: 'json',
                    limit: 1,
                    countrycodes: 'br'
                },
                headers: {
                    'User-Agent': 'InsPecT-App/1.0'
                }
            });

            if (nominatimResponse.data.length === 0) {
                throw new Error('Coordenadas não encontradas para este endereço');
            }

            const location = nominatimResponse.data[0];
            
            return {
                latitude: parseFloat(location.lat),
                longitude: parseFloat(location.lon),
                endereco: location.display_name
            };
        } catch (error) {
            console.error('Erro ao obter coordenadas do endereço:', error);
            throw error;
        }
    }

    /**
     * Processa lista de inspeções e adiciona coordenadas (otimizado para velocidade)
     */
    async processInspectionsWithCoordinates(inspections) {
        const results = [];

        // Limitar a 50 inspeções para evitar timeout
        const limitedInspections = inspections.slice(0, 50);

        // Processar em paralelo com limite de concorrência
        const batchSize = 5;
        for (let i = 0; i < limitedInspections.length; i += batchSize) {
            const batch = limitedInspections.slice(i, i + batchSize);

            const batchPromises = batch.map(async (inspection) => {
                try {
                    if (inspection.cep) {
                        // Usar versão mais rápida sem retry para primeira carga
                        const coordinates = await this.getCepCoordinates(inspection.cep);
                            latitude: coordinates.latitude,
                            longitude: coordinates.longitude,
                            endereco_completo: `${coordinates.endereco.logradouro}, ${inspection.numero || 'S/N'}, ${coordinates.endereco.bairro}, ${coordinates.endereco.cidade} - ${coordinates.endereco.uf}`
                        };
                    } else {
                        // Inspeção sem CEP - adicionar sem coordenadas
                        return {
                            ...inspection,
                            latitude: null,
                            longitude: null,
                            endereco_completo: 'Endereço não disponível'
                        };
                    }
                } catch (error) {
                    console.warn(`Erro ao processar coordenadas da inspeção ${inspection.id}:`, error.message);
                    return {
                        ...inspection,
                        latitude: null,
                        longitude: null,
                        endereco_completo: 'Erro ao obter localização'
                    };
                }
            });

            // Aguardar o lote atual
            const batchResults = await Promise.allSettled(batchPromises);

            // Adicionar resultados válidos
            batchResults.forEach(result => {
                if (result.status === 'fulfilled' && result.value) {
                    results.push(result.value);
                }
            });

            // Delay entre lotes para evitar rate limiting
            if (i + batchSize < limitedInspections.length) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        return results;
    }

    /**
     * Versão com retry do getCepCoordinates para lidar com erros de conexão
     */
    async getCepCoordinatesWithRetry(cep, maxRetries = 3) {
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 1) {
                    // Aguardar mais tempo entre tentativas (backoff exponencial)
                    const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
                    await new Promise(resolve => setTimeout(resolve, delay));
                }

                return await this.getCepCoordinates(cep);

            } catch (error) {
                lastError = error;

                // Log apenas na última tentativa para evitar spam
                if (attempt === maxRetries) {
                    console.error(`Todas as tentativas falharam para CEP ${cep}:`, error.message);
                }

                // Se for erro de rede, tentar novamente
                if (error.code === 'ECONNRESET' ||
                    error.code === 'ETIMEDOUT' ||
                    error.code === 'ENOTFOUND' ||
                    error.code === 'ECONNREFUSED' ||
                    error.message.includes('timeout') ||
                    error.message.includes('network')) {

                    if (attempt < maxRetries) {
                        console.warn(`Tentativa ${attempt}/${maxRetries} falhou para CEP ${cep}, tentando novamente...`);
                        continue;
                    }
                }

                // Para outros erros (CEP inválido, etc), não tentar novamente
                break;
            }
        }

        throw lastError;
    }
}

module.exports = new GeocodingService();
