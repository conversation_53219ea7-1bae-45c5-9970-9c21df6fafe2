const axios = require('axios');

class GeocodingService {
    
    /**
     * Converte CEP em coordenadas usando ViaCEP + Nominatim
     */
    async getCepCoordinates(cep) {
        try {
            const cepLimpo = cep.replace(/\D/g, '');
            if (cepLimpo.length !== 8) {
                throw new Error('CEP deve ter 8 dígitos');
            }

            // Buscar endereço pelo CEP
            const viaCepResponse = await axios.get(`https://viacep.com.br/ws/${cepLimpo}/json/`);
            
            if (viaCepResponse.data.erro) {
                throw new Error('CEP não encontrado');
            }

            const endereco = viaCepResponse.data;
            
            // Montar query para geocoding
            const query = `${endereco.logradouro}, ${endereco.bairro}, ${endereco.localidade}, ${endereco.uf}, Brasil`;
            
            // Usar Nominatim para obter coordenadas
            const nominatimResponse = await axios.get('https://nominatim.openstreetmap.org/search', {
                params: {
                    q: query,
                    format: 'json',
                    limit: 1,
                    countrycodes: 'br'
                },
                headers: {
                    'User-Agent': 'InsPecT-App/1.0'
                },
                timeout: 5000 // 5 segundos de timeout
            });

            if (nominatimResponse.data.length === 0) {
                // Tentar busca mais genérica apenas com cidade e estado
                const genericQuery = `${endereco.localidade}, ${endereco.uf}, Brasil`;
                const fallbackResponse = await axios.get('https://nominatim.openstreetmap.org/search', {
                    params: {
                        q: genericQuery,
                        format: 'json',
                        limit: 1,
                        countrycodes: 'br'
                    },
                    headers: {
                        'User-Agent': 'InsPecT-App/1.0'
                    },
                    timeout: 5000
                });

                if (fallbackResponse.data.length === 0) {
                    throw new Error('Coordenadas não encontradas para este endereço');
                }

                const fallbackLocation = fallbackResponse.data[0];
                return {
                    latitude: parseFloat(fallbackLocation.lat),
                    longitude: parseFloat(fallbackLocation.lon),
                    endereco: {
                        logradouro: endereco.logradouro,
                        bairro: endereco.bairro,
                        localidade: endereco.localidade,
                        uf: endereco.uf,
                        cep: cepLimpo
                    },
                    precisao: 'cidade' // Indicar que é uma localização aproximada
                };
            }

            const location = nominatimResponse.data[0];

            return {
                latitude: parseFloat(location.lat),
                longitude: parseFloat(location.lon),
                endereco: {
                    logradouro: endereco.logradouro,
                    bairro: endereco.bairro,
                    cidade: endereco.localidade,
                    uf: endereco.uf,
                    cep: endereco.cep
                },
                precisao: 'endereco' // Localização precisa
            };
        } catch (error) {
            console.error('Erro ao obter coordenadas do CEP:', error);
            throw error;
        }
    }

    /**
     * Converte endereço completo em coordenadas
     */
    async getAddressCoordinates(endereco, numero = '') {
        try {
            const query = `${endereco}${numero ? ', ' + numero : ''}, Brasil`;
            
            const nominatimResponse = await axios.get('https://nominatim.openstreetmap.org/search', {
                params: {
                    q: query,
                    format: 'json',
                    limit: 1,
                    countrycodes: 'br'
                },
                headers: {
                    'User-Agent': 'InsPecT-App/1.0'
                }
            });

            if (nominatimResponse.data.length === 0) {
                throw new Error('Coordenadas não encontradas para este endereço');
            }

            const location = nominatimResponse.data[0];
            
            return {
                latitude: parseFloat(location.lat),
                longitude: parseFloat(location.lon),
                endereco: location.display_name
            };
        } catch (error) {
            console.error('Erro ao obter coordenadas do endereço:', error);
            throw error;
        }
    }

    /**
     * Processa lista de inspeções e adiciona coordenadas
     */
    async processInspectionsWithCoordinates(inspections) {
        const results = [];
        
        for (const inspection of inspections) {
            try {
                if (inspection.cep) {
                    const coordinates = await this.getCepCoordinates(inspection.cep);
                    results.push({
                        ...inspection,
                        latitude: coordinates.latitude,
                        longitude: coordinates.longitude,
                        endereco_completo: `${coordinates.endereco.logradouro}, ${inspection.numero || 'S/N'}, ${coordinates.endereco.bairro}, ${coordinates.endereco.cidade} - ${coordinates.endereco.uf}`
                    });
                } else {
                    // Inspeção sem CEP - adicionar sem coordenadas
                    results.push({
                        ...inspection,
                        latitude: null,
                        longitude: null,
                        endereco_completo: 'Endereço não disponível'
                    });
                }
                
                // Delay para evitar rate limiting
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.warn(`Erro ao processar coordenadas da inspeção ${inspection.id}:`, error.message);
                results.push({
                    ...inspection,
                    latitude: null,
                    longitude: null,
                    endereco_completo: 'Erro ao obter localização'
                });
            }
        }
        
        return results;
    }
}

module.exports = new GeocodingService();
