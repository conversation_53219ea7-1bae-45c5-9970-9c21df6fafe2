.ambient-details-page {
    background-color: var(--background-color);
    color: var(--foreground-color);
    transition: all 0.3s ease;
}

.image-banner {
    width: 100%;
    height: auto;
    max-height: 600px;
    background-color: #f0f2f5;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-banner img {
    width: 100%;
    height: 100%;
    max-height: 600px;
    margin: auto;
    object-fit: contain;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    max-height: 600px;
    background: linear-gradient(110deg, #e1e1e1 8%, #f0f0f0 18%, #e1e1e1 33%);
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
}

.image-placeholder-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
    color: var(--muted-foreground-color);
    text-align: center;
    transition: color 0.3s ease;
}

.image-placeholder-icon svg {
    width: 80px;
    height: 80px;
    stroke: var(--muted-foreground-color);
    fill: none;
    opacity: 0.6;
    transition: stroke 0.3s ease, opacity 0.3s ease;
}

.image-placeholder-icon p {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.8;
    color: var(--muted-foreground-color);
    transition: color 0.3s ease;
}

/* Modo escuro - placeholder de imagem */
[data-theme="dark"] .image-placeholder-icon {
    color: #e5e7eb;
}

[data-theme="dark"] .image-placeholder-icon svg {
    stroke: #e5e7eb;
    opacity: 0.8;
}

[data-theme="dark"] .image-placeholder-icon p {
    color: #e5e7eb;
}

@keyframes shine {
    to {
        background-position-x: -200%;
    }
}

.info-container {
    max-width: 1200px;
    margin: -1rem auto 0 auto;
    padding: 0 1.5rem 3rem 1.5rem;
    position: relative;
    z-index: 10;
    background-color: var(--background-color);
    color: var(--foreground-color);
    transition: all 0.3s ease;
}

a.btn-primary {
    text-decoration: none;
}

.info-header {
    background-color: var(--background-color);
    padding: 1.5rem 2rem;
    border-radius: 12px;
    border: 1px solid var(--muted-color);
    box-shadow: 0 8px 30px rgba(0, 0, 0, .08);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

/* Info header no modo escuro */
[data-theme="dark"] .info-header {
    background-color: #1f2937;
    border-color: #4b5563;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.info-section {
    margin-top: 2rem;
}

.info-section label {
    display: block;
    margin-bottom: .75rem;
    color: var(--muted-foreground-color);
    font-weight: 600;
    transition: color 0.3s ease;
}

.info-section p {
    margin: 0;
    padding: 1.25rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid var(--muted-color);
    color: var(--muted-foreground-color);
    line-height: 1.6;
    transition: all 0.3s ease;
}

/* Labels e parágrafos no modo escuro */
[data-theme="dark"] .info-section label {
    color: #d1d5db;
}

[data-theme="dark"] .info-section p {
    background-color: #374151;
    border-color: #6b7280;
    color: #e5e7eb;
}

.section-divider {
    border: none;
    border-top: 1px solid var(--muted-color);
    margin: 2.5rem 0;
}

.content-section .h5 {
    margin-bottom: 0.5rem;
}

.horizontal-scroll-wrapper {
    display: flex;
    overflow-x: auto;
    gap: 1rem;
    padding: 2rem;
    padding-bottom: 1.5rem;
    padding-top: 0.5rem;
    margin: 0 -2rem;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.horizontal-scroll-wrapper::-webkit-scrollbar {
    display: none;
}

.ambient-card-h {
    flex: 0 0 240px;
    text-decoration: none;
    color: inherit;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--muted-color);
    background-color: var(--background-color);
    transition: transform .2s ease, box-shadow .2s ease;
}

.ambient-card-h:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, .08);
}

.ambient-card-h .card-image {
    width: 100%;
    aspect-ratio: 4/3;
    object-fit: cover;
    background-color: #f0f2f5;
}

.card-image-placeholder {
    width: 100%;
    aspect-ratio: 16/9;
    background: linear-gradient(135deg, var(--muted-color) 0%, var(--background-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--muted-foreground-color);
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
}

.card-image-placeholder svg {
    width: 40px;
    height: 40px;
    stroke: var(--muted-foreground-color);
    opacity: 0.6;
    transition: stroke 0.3s ease, opacity 0.3s ease;
}

/* Modo escuro - placeholder dos cards */
[data-theme="dark"] .card-image-placeholder {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    color: #e5e7eb;
}

[data-theme="dark"] .card-image-placeholder svg {
    stroke: #e5e7eb;
    opacity: 0.8;
}

.ambient-card-h .card-title {
    font-size: .9rem;
    font-weight: 600;
    padding: 1rem;
    margin: 0;
}

.occurrences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 1.5rem;
}

.ocorrencia-card-v {
    display: block;
    text-decoration: none;
    color: inherit;
    background-color: var(--background-color);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--muted-color);
    transition: all 0.3s ease;
}

/* Cards de ocorrência no modo escuro */
[data-theme="dark"] .ocorrencia-card-v {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #ffffff;
}

[data-theme="dark"] .ocorrencia-card-v:hover {
    background-color: #374151;
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
}

.ocorrencia-card-v:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, .1);
}

.ocorrencia-card-v .card-image {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: cover;
    background-color: #f0f2f5;
    transition: background-color 0.3s ease;
}

/* Imagens dos cards no modo escuro */
[data-theme="dark"] .ocorrencia-card-v .card-image {
    background-color: #374151;
}

.ocorrencia-card-v .card-content {
    padding: 1rem;
    transition: all 0.3s ease;
}

/* Conteúdo dos cards no modo escuro */
[data-theme="dark"] .ocorrencia-card-v .card-content {
    background-color: #1f2937;
}

[data-theme="dark"] .ocorrencia-card-v .card-content h3 {
    color: #ffffff;
}

[data-theme="dark"] .ocorrencia-card-v .card-content p {
    color: #d1d5db;
}

.ocorrencia-card-v .title-date {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
}

.ocorrencia-card-v .title-date h4 {
    margin: 0;
    font-size: 1.1rem;
}

.ocorrencia-card-v .title-date .date {
    font-size: .8rem;
    color: var(--muted-foreground-color);
}

.empty-text {
    color: var(--muted-foreground-color);
    padding: 1.5rem;
    background-color: #fdfdfd;
    border: 1px dashed var(--muted-color);
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

/* Estado vazio no modo escuro */
[data-theme="dark"] .empty-text {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #d1d5db;
}

.hidden {
    display: none !important;
}

/* ===== CHECKLIST DE AFAZERES ===== */
.afazeres-checklist {
    background-color: #f8f9fa;
    border: 1px solid var(--muted-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

[data-theme="dark"] .afazeres-checklist {
    background-color: #1f2937;
    border-color: #4b5563;
}

.loading-placeholder {
    text-align: center;
    color: var(--muted-foreground-color);
    padding: 2rem;
    font-style: italic;
}

.afazer-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.afazer-item:last-child {
    border-bottom: none;
}

[data-theme="dark"] .afazer-item {
    border-bottom-color: #374151;
}

.afazer-item:hover {
    background-color: rgba(59, 130, 246, 0.05);
    border-radius: 6px;
    margin: 0 -0.5rem;
    padding: 0.75rem 0.5rem;
}

[data-theme="dark"] .afazer-item:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.afazer-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--muted-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    flex-shrink: 0;
}

.afazer-checkbox:hover {
    border-color: var(--primary-color);
}

.afazer-checkbox.checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.afazer-checkbox.checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.afazer-label {
    flex: 1;
    color: var(--foreground-color);
    font-size: 0.95rem;
    line-height: 1.4;
    cursor: pointer;
    transition: all 0.2s ease;
}

.afazer-item.completed .afazer-label {
    color: var(--muted-foreground-color);
    text-decoration: line-through;
}

.afazeres-empty {
    text-align: center;
    color: var(--muted-foreground-color);
    padding: 2rem;
    font-style: italic;
    background-color: #fdfdfd;
    border: 1px dashed var(--muted-color);
    border-radius: 8px;
}

[data-theme="dark"] .afazeres-empty {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #d1d5db;
}

.afazeres-progress {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    font-size: 0.9rem;
    color: #1e40af;
}

[data-theme="dark"] .afazeres-progress {
    background-color: #1e3a8a;
    border-color: #3b82f6;
    color: #dbeafe;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

[data-theme="dark"] .progress-bar {
    background-color: #374151;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.error-message {
    color: var(--destructive-color);
    background-color: #fff5f5;
    border: 1px solid var(--destructive-color);
    border-radius: 8px;
    padding: 1rem;
    margin: 1.5rem;
    text-align: center;
}