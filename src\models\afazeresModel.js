const db = require('../config/db');

const Afazer = {
  async findAll() {
    const queryText = 'SELECT * FROM afazeres ORDER BY titulo;';
    try {
      const { rows } = await db.query(queryText);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar todos os afazeres:', error);
      throw error;
    }
  },

  async create({ titulo }) {
    const queryText = `
        INSERT INTO afazeres (titulo)
        VALUES ($1)
        RETURNING *;
    `;
    try {
        const { rows } = await db.query(queryText, [titulo]);
        return rows[0];
    } catch (error) {
        console.error('Erro ao criar afazer:', error);
        throw error;
    }
  },

  // Buscar apenas afazeres que foram selecionados para um ambiente específico
  async findByAmbienteId(id_ambiente) {
    const queryText = `
        SELECT
            a.id,
            a.titulo,
            aa.valor as concluido
        FROM afazeres a
        INNER JOIN ambientes_afazeres aa ON a.id = aa.id_afazer
        WHERE aa.id_ambiente = $1
        ORDER BY a.titulo;
    `;
    try {
        const { rows } = await db.query(queryText, [id_ambiente]);
        return rows;
    } catch (error) {
        console.error('Erro ao buscar afazeres do ambiente:', error);
        throw error;
    }
  },

  // Atualizar status de um afazer para um ambiente
  async updateStatusAmbiente(id_afazer, id_ambiente, valor) {
    const queryText = `
        INSERT INTO ambientes_afazeres (id_afazer, id_ambiente, valor)
        VALUES ($1, $2, $3)
        ON CONFLICT (id_afazer, id_ambiente)
        DO UPDATE SET valor = $3
        RETURNING *;
    `;
    try {
        const { rows } = await db.query(queryText, [id_afazer, id_ambiente, valor]);
        return rows[0];
    } catch (error) {
        console.error('Erro ao atualizar status do afazer:', error);
        throw error;
    }
  },

  // Buscar apenas afazeres concluídos de um ambiente
  async findConcluidos(id_ambiente) {
    const queryText = `
        SELECT a.id, a.titulo
        FROM afazeres a
        INNER JOIN ambientes_afazeres aa ON a.id = aa.id_afazer
        WHERE aa.id_ambiente = $1 AND aa.valor = true
        ORDER BY a.titulo;
    `;
    try {
        const { rows } = await db.query(queryText, [id_ambiente]);
        return rows;
    } catch (error) {
        console.error('Erro ao buscar afazeres concluídos:', error);
        throw error;
    }
  }
};

module.exports = Afazer;