const db = require('../config/db');

const Afazer = {
  async findAll() {
    const queryText = 'SELECT * FROM afazeres ORDER BY titulo;';
    try {
      const { rows } = await db.query(queryText);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar todos os afazeres:', error);
      throw error;
    }
  },

  async create({ titulo }) {
    const queryText = `
        INSERT INTO afazeres (titulo) 
        VALUES ($1) 
        RETURNING *;
    `;
    try {
        const { rows } = await db.query(queryText, [titulo]);
        return rows[0];
    } catch (error) {
        console.error('Erro ao criar afazer:', error);
        throw error;
    }
  }
};

module.exports = Afazer;