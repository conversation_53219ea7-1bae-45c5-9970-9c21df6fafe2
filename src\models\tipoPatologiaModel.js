const db = require('../config/db');

const TipoPatologia = {
  async findAll() {
    const queryText = 'SELECT nome as id, nome, id_tipo_sistema FROM tipos_patologia ORDER BY nome;';
    try {
      const { rows } = await db.query(queryText);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar tipos de patologia:', error);
      throw error;
    }
  },

  async findByNome(nome) {
    const queryText = 'SELECT nome, id_tipo_sistema FROM tipos_patologia WHERE nome = $1;';
    try {
      const { rows } = await db.query(queryText, [nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar tipo de patologia por nome:', error);
      throw error;
    }
  },

  async create(dados) {
    const queryText = 'INSERT INTO tipos_patologia (nome) VALUES ($1) RETURNING nome as id, nome;';
    try {
      const { rows } = await db.query(queryText, [dados.nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao criar tipo de patologia:', error);
      throw error;
    }
  },

  async findById(nome) {
    const queryText = 'SELECT * FROM tipos_patologia WHERE nome = $1;';
    try {
      const { rows } = await db.query(queryText, [nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar tipo de patologia por nome:', error);
      throw error;
    }
  }
};
module.exports = TipoPatologia;