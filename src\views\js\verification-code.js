document.addEventListener('DOMContentLoaded', () => {
  const form = document.getElementById('verificationForm');
  const realInput = document.getElementById('codeInput');
  const visualBoxes = Array.from(document.querySelectorAll('.code-box'));

  const updateVisuals = () => {
    const value = realInput.value;

    visualBoxes.forEach((box, index) => {
      box.textContent = value[index] || '';
      if (index < value.length) {
        box.classList.add('filled');
      } else {
        box.classList.remove('filled');
      }

      box.classList.remove('focused');
    });

    const nextIndex = value.length;
    if (nextIndex < visualBoxes.length) {
      visualBoxes[nextIndex].classList.add('focused');
    } else {
      if (visualBoxes.length > 0) {
        visualBoxes[visualBoxes.length - 1].classList.add('focused');
      }
    }
  };

  realInput.addEventListener('focus', () => {
    document.querySelector('.code-input-visuals').classList.add('active');
    updateVisuals();
  });

  realInput.addEventListener('blur', () => {
    visualBoxes.forEach(box => box.classList.remove('focused'));
  });

  realInput.addEventListener('input', updateVisuals);

  form.addEventListener('submit', (e) => {
    e.preventDefault();
    const code = realInput.value;
    if (code.length === 6) {
      console.log('Código enviado:', code);
      alert(`Código a ser verificado: ${code}`);
    } else {
      alert('Por favor, insira o código de 6 dígitos.');
    }
  });

  realInput.focus();
});