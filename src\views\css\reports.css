
.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 20px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--background-color);
  color: var(--foreground-color);
  transition: all 0.3s ease;
}

/* Responsividade do container */
@media (max-width: 767px) {
  .container {
    padding: 16px 12px;
    gap: 16px;
  }
}

@media (min-width: 768px) {
  .container {
    padding: 32px 24px;
  }
}

@media (min-width: 1200px) {
  .container {
    padding: 40px 32px;
  }
}

h2 {
  margin-bottom: 1rem;
  font-size: 1.8rem;
  color: var(--foreground-color);
  transition: color 0.3s ease;
}

/* Melhorar contraste do título no modo escuro */
[data-theme="dark"] h2 {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.search-filter {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 1.5rem;
}

/* Responsividade da busca */
@media (max-width: 767px) {
  .search-filter {
    gap: 8px;
    margin-bottom: 1rem;
  }

  .input-wrapper input {
    font-size: 16px; /* Evita zoom no iOS */
  }
}

.input-wrapper {
  position: relative;
  flex: 1;
}

.input-wrapper input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid var(--muted-color);
  border-radius: 8px;
  font-size: 1rem;
  background-color: var(--background-color);
  color: var(--foreground-color);
  transition: all 0.3s ease;
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Melhorar contraste dos inputs no modo escuro */
[data-theme="dark"] .input-wrapper input {
  background-color: #374151;
  border-color: #6b7280;
  color: #ffffff;
}

[data-theme="dark"] .input-wrapper input::placeholder {
  color: #9ca3af;
}

[data-theme="dark"] .input-wrapper input:focus {
  background-color: #4b5563;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.input-wrapper img {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  pointer-events: none;
  transition: filter 0.3s ease;
}

/* Melhorar visibilidade dos ícones no modo escuro */
[data-theme="dark"] .input-wrapper img {
  filter: brightness(1.5) contrast(1.2);
}

.filter-btn {
  background: var(--background-color);
  border: 1px solid var(--muted-color);
  padding: 0.75rem;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.filter-btn:hover {
  background-color: var(--muted-color);
  border-color: var(--primary-color);
}

/* Modo escuro para o botão de filtro */
[data-theme="dark"] .filter-btn {
  background: #374151;
  border-color: #6b7280;
}

[data-theme="dark"] .filter-btn:hover {
  background-color: #4b5563;
  border-color: var(--primary-color);
}

[data-theme="dark"] .filter-btn img {
  filter: brightness(1.5) contrast(1.2);
}

.report-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 1rem;
}

/* Responsividade para desktop */
@media (min-width: 1200px) {
  .report-list {
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .report-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
}

/* Mobile */
@media (max-width: 767px) {
  .report-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.report-card {
  background: linear-gradient(135deg, var(--background-color) 0%, rgba(248, 250, 252, 0.5) 100%);
  border: 1px solid var(--muted-color);
  border-radius: 16px;
  padding: 1.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  color: var(--foreground-color);
}

/* Melhorar contraste dos títulos dos cards */
.report-card .subtitle1 {
  color: #1e293b;
  font-weight: 700;
  font-size: 1.25rem;
  line-height: 1.3;
  margin-bottom: 0.5rem;
}

/* Melhorar contraste dos textos secundários */
.report-card .text-muted-foreground {
  color: #475569 !important;
  font-weight: 600;
}

.report-card .body2 {
  color: #374151;
  font-weight: 500;
  line-height: 1.4;
}

.report-card .body3 {
  color: #4b5563;
  font-weight: 500;
}

/* Cards no modo escuro */
[data-theme="dark"] .report-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-color: #6b7280;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  color: #ffffff;
}

/* Melhorar contraste dos textos nos cards no modo escuro */
[data-theme="dark"] .report-card .subtitle1 {
  color: #ffffff;
  font-weight: 800;
  font-size: 1.3rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .report-card .text-muted-foreground {
  color: #e5e7eb !important;
  font-weight: 600;
}

[data-theme="dark"] .report-card .body2,
[data-theme="dark"] .report-card .body3 {
  color: #f3f4f6;
  font-weight: 500;
}

[data-theme="dark"] .report-card .loading-address {
  color: #d1d5db;
  font-weight: 500;
}

/* Melhorar seções dos cards no modo escuro */
[data-theme="dark"] .report-card .top {
  border-bottom-color: #4b5563;
}

[data-theme="dark"] .report-card .top .subtitle1 {
  color: #ffffff;
  font-weight: 800;
}

[data-theme="dark"] .report-card .top .status {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-color: #6b7280;
  color: #ffffff;
}

[data-theme="dark"] .report-card .middle {
  background: rgba(31, 41, 55, 0.8);
  border-color: #4b5563;
}

[data-theme="dark"] .report-card .middle .body2 {
  color: #ffffff;
  font-weight: 600;
}

[data-theme="dark"] .report-card .bottom {
  border-top-color: #4b5563;
}

[data-theme="dark"] .report-card .bottom .body2 {
  color: #e5e7eb;
  font-weight: 600;
}

[data-theme="dark"] .report-card .top .status p {
  color: #ffffff;
  font-weight: 700;
}

[data-theme="dark"] .report-card .date p {
  color: #d1d5db;
  font-weight: 600;
}

/* Status badges com melhor contraste */
.status-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid;
}

.status-badge.aberta {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  border-color: #9ca3af;
}

.status-badge.em-andamento {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border-color: #f59e0b;
}

.status-badge.concluida {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border-color: #10b981;
}

/* Status badges no modo escuro */
[data-theme="dark"] .status-badge.aberta {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
  color: #ffffff;
  border-color: #9ca3af;
}

[data-theme="dark"] .status-badge.em-andamento {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #000000;
  border-color: #f59e0b;
}

[data-theme="dark"] .status-badge.concluida {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #000000;
  border-color: #10b981;
}

/* Melhorar contraste das seções dos cards no modo claro */
.report-card .top {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.report-card .top .subtitle1 {
  color: #0f172a;
  font-weight: 800;
  font-size: 1.3rem;
}

.report-card .top .status {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-weight: 600;
}

.report-card .middle {
  background: rgba(248, 250, 252, 0.8);
  padding: 1rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin: 0.5rem 0;
}

.report-card .middle .body2 {
  color: #1e293b;
  font-weight: 600;
  font-size: 0.95rem;
}

.report-card .bottom {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
  margin-top: 1rem;
}

.report-card .bottom .body2 {
  color: #374151;
  font-weight: 600;
}

/* Melhorar contraste do loading address */
.report-card .loading-address {
  color: #6b7280;
  font-weight: 500;
  font-style: italic;
}

/* Melhorar contraste dos códigos de projeto - MODO CLARO */
.report-card .top .status p {
  color: #ffffff !important;
  font-weight: 700;
  font-size: 0.85rem;
}

/* Garantir números brancos em todos os contextos no modo claro */
.report-card .top .status p,
.report-card .status p,
.project-number,
.codigo-projeto {
  color: #ffffff !important;
  font-weight: 700 !important;
}

/* Melhorar contraste das datas */
.report-card .date p {
  color: #4b5563;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Códigos de projeto com destaque */
.project-code {
  background: linear-gradient(135deg, #00558C 0%, #004070 100%);
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-weight: 700;
  font-size: 0.8rem;
  border: 1px solid #00558C;
  display: inline-block;
}

[data-theme="dark"] .project-code {
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
  color: #ffffff;
  border-color: #3b82f6;
}

/* Melhorar contraste dos nomes de inspeção */
.inspection-name {
  color: #0f172a;
  font-weight: 700;
  font-size: 1.1rem;
  line-height: 1.3;
  margin-bottom: 0.5rem;
}

[data-theme="dark"] .inspection-name {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Melhorar contraste das descrições */
.inspection-description {
  color: #475569;
  font-weight: 500;
  line-height: 1.4;
  font-size: 0.9rem;
}

[data-theme="dark"] .inspection-description {
  color: #d1d5db;
  font-weight: 500;
}

/* Melhorar contraste dos endereços */
.address-info {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  margin: 0.5rem 0;
}

.address-info p {
  color: #374151;
  font-weight: 600;
  font-size: 0.85rem;
  margin: 0;
}

[data-theme="dark"] .address-info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .address-info p {
  color: #e5e7eb;
  font-weight: 600;
}

/* Melhorar contraste das informações de data */
.date-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.85rem;
  font-weight: 600;
}

[data-theme="dark"] .date-info {
  color: #d1d5db;
}

/* Ícones com melhor visibilidade */
.info-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.report-card:hover .info-icon {
  opacity: 1;
}

[data-theme="dark"] .info-icon {
  filter: brightness(1.3) contrast(1.1);
}

.report-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #00558C 0%, #0066A3 50%, #007BC7 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

/* Animação shimmer nos cards do modo claro */
.report-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 85, 140, 0.08), transparent);
  animation: cardShimmer 5s infinite;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.report-card:hover::after {
  opacity: 1;
}

@keyframes cardShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.report-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: var(--primary-color);
}

/* Hover no modo escuro */
[data-theme="dark"] .report-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  border-color: var(--primary-color);
}

.report-card:hover::before {
  transform: scaleX(1);
}

/* Botões de ação do card */
.card-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.btn-card {
  flex: 1;
  padding: 0.875rem 1.25rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.btn-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-card:hover::before {
  left: 100%;
}

.btn-preview {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #00558C;
  border: 2px solid #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-preview:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #00558C;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-download {
  background: linear-gradient(135deg, #00558C 0%, #004070 100%);
  color: white;
  border: 2px solid #00558C;
  box-shadow: 0 4px 8px rgba(0, 85, 140, 0.3);
}

.btn-download:hover {
  background: linear-gradient(135deg, #004070 0%, #003050 100%);
  border-color: #003050;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 85, 140, 0.4);
  animation: pulse 2s infinite;
}

/* Animação de pulsação para botões */
@keyframes pulse {
  0% {
    box-shadow: 0 6px 12px rgba(0, 85, 140, 0.4);
  }
  50% {
    box-shadow: 0 6px 12px rgba(0, 85, 140, 0.6), 0 0 20px rgba(0, 85, 140, 0.3);
  }
  100% {
    box-shadow: 0 6px 12px rgba(0, 85, 140, 0.4);
  }
}

/* Botões no modo escuro */
[data-theme="dark"] .btn-preview {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
  color: #ffffff;
  border-color: #9ca3af;
  font-weight: 600;
}

[data-theme="dark"] .btn-preview:hover {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
  border-color: var(--primary-color);
  color: #ffffff;
}

[data-theme="dark"] .btn-download {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
  border-color: var(--primary-color);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  color: #ffffff;
  font-weight: 600;
}

[data-theme="dark"] .btn-download:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  border-color: #1e40af;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.5);
  color: #ffffff;
}

/* Responsividade dos botões */
@media (max-width: 767px) {
  .btn-preview {
    display: none; /* Esconde o botão preview no mobile */
  }

  .btn-download {
    flex: 1;
    width: 100%;
  }
}

@media (min-width: 768px) {
  .card-actions {
    flex-direction: row;
  }
}

.report-card .top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.report-card .top .subtitle1 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  flex-grow: 1;
  line-height: 1.4;
  letter-spacing: -0.025em;
}

.report-card .top .status {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.report-card .top .status p.text-muted-foreground {
    color: #ffffff !important;
}

/* Garantir que todos os números de projeto sejam brancos no modo claro */
.report-card .top .status p,
.report-card .status p,
.status p {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* Regra específica para números de projeto com máxima prioridade */
.report-card .top .status p,
.report-card .top .status p.text-muted-foreground,
.report-card .top .status p.body2,
.report-card .top .status p.body3,
.report-card .status p,
.project-code,
.codigo-projeto {
    color: #ffffff !important;
    font-weight: 700 !important;
    text-shadow: none !important;
}
/* Indicador de relatório novo */
.report-card.new::after {
  content: 'NOVO';
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  z-index: 10;
}

/* Status badges melhorados */
.report-card .top .status.finalizado {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.report-card .top .status.em_andamento {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.report-card .top .status.nao_iniciado {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}


.report-card .middle {
  margin: 0.75rem 0;
  padding: 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border-left: 4px solid #00558C;
}

.report-card .middle .body2 {
  font-size: 0.95rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
}

.report-card .middle::before {
  content: '📍';
  margin-right: 0.5rem;
  font-size: 1rem;
}

.report-card .bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.report-card .bottom .date {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.report-card .bottom .date::before {
  content: '📅';
  font-size: 1rem;
}

.report-card .bottom .date p.text-muted-foreground {
    color: #4b5563;
    margin: 0;
}

.list-separator {
  height: 1px;
  background-color: #eee;
  margin: 1rem 0;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.pagination button {
  border: none;
  background: transparent;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  color: #00528C;
  transition: all 0.2s ease-in-out;
}

.pagination button:hover:not(.active):not([disabled]) {
  background-color: #e6f2f9;
}

.pagination button.active {
  background-color: #00528C;
  color: white;
}

.pagination button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsividade da paginação */
@media (max-width: 767px) {
  .pagination {
    margin-top: 1.5rem;
    gap: 4px;
  }

  .pagination button {
    padding: 0.5rem;
    font-size: 0.9rem;
    min-width: 40px;
  }
}

/* Animação de loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Estados do botão de download */
.btn-download:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-download:disabled:hover {
  background: #00558C;
  transform: none;
}

/* Efeitos visuais adicionais */
.report-card .top .subtitle1:hover {
  color: #00558C;
  transition: color 0.2s ease;
}

.report-card .middle {
  position: relative;
}

.report-card .middle::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #00558C, #007BC7);
  transition: width 0.3s ease;
}

.report-card:hover .middle::after {
  width: 100%;
}

/* Animação de loading para botões */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.btn-card:disabled {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Card destacado/importante */
.report-card.featured {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  box-shadow: 0 8px 16px rgba(245, 158, 11, 0.2);
}

.report-card.featured::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
  height: 6px;
  transform: scaleX(1);
}

.report-card.featured .top .subtitle1 {
  color: #92400e;
}

/* Efeito de brilho sutil */
.report-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: rotate(45deg);
  transition: all 0.6s;
  opacity: 0;
  pointer-events: none;
}

.report-card:hover::after {
  opacity: 1;
  transform: rotate(45deg) translate(50%, 50%);
}

/* Indicador de carregamento de endereço */
.loading-address {
  color: #64748b;
  font-style: italic;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.report-card .middle p {
  animation: fadeIn 0.3s ease-out;
}

/* Estado de nenhum resultado encontrado */
.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  border: 2px dashed #cbd5e1;
}

.no-results p {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

.no-results::before {
  content: '🔍';
  display: block;
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .report-list {
    grid-template-columns: 1fr;
  }

  .report-card {
    padding: 1.25rem;
    gap: 1rem;
  }

  .report-card .top .subtitle1 {
    font-size: 1.125rem;
  }

  .report-card .middle {
    padding: 0.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .btn-card {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .search-filter {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-btn {
    width: 100%;
    height: auto;
    padding: 0.75rem;
  }
}
