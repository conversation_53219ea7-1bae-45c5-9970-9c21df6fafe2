* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

body {
    background: var(--background-color);
    color: var(--foreground-color);
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Body no modo escuro */
[data-theme="dark"] body {
    background: var(--background-color);
}

.topbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, #004070 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Topbar no modo escuro */
[data-theme="dark"] .topbar {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid #374151;
}

.menu-btn,
.search-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.menu-btn:hover,
.search-btn:hover {
    transform: scale(1.1);
}

.logo {
    font-size: 22px;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.container {
    padding: 24px 16px;
    max-width: 1200px;
    margin: 0 auto;
    background-color: var(--background-color);
    color: var(--foreground-color);
    transition: all 0.3s ease;
    width: 100%;
    min-height: calc(100vh - 64px);
}

/* Garantir que não haja barras laterais */
html, body {
    width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

h2 {
    font-size: 2rem;
    margin-bottom: 32px;
    font-weight: 700;
    color: var(--foreground-color);
    text-align: center;
    position: relative;
    transition: color 0.3s ease;
}

/* Melhorar contraste do título no modo escuro */
[data-theme="dark"] h2 {
    color: #ffffff;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #00558C, #007BC7);
    border-radius: 2px;
}

/* Layout em grid para cards */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    margin-top: 32px;
}

.team-card {
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(248, 250, 252, 0.5) 100%);
    border-radius: 20px;
    padding: 28px 24px;
    margin-bottom: 0;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--muted-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    color: var(--foreground-color);
}

/* Cards no modo escuro */
[data-theme="dark"] .team-card {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #6b7280;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    color: #ffffff;
}

/* Melhorar contraste dos textos nos cards de equipe */
[data-theme="dark"] .team-name {
    color: #ffffff;
    font-weight: 700;
}

[data-theme="dark"] .team-id {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: #ffffff;
    font-weight: 700;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #00558C 0%, #0066A3 50%, #007BC7 100%);
}

/* Animação shimmer nos cards de equipe do modo claro */
.team-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 85, 140, 0.06), transparent);
    animation: teamShimmer 6s infinite;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-card:hover::after {
    opacity: 1;
}

@keyframes teamShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.team-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

/* Hover no modo escuro */
[data-theme="dark"] .team-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

.team-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--muted-color);
    transition: border-color 0.3s ease;
}

.team-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--foreground-color);
    margin: 0;
    transition: color 0.3s ease;
}

/* Header no modo escuro */
[data-theme="dark"] .team-header {
    border-bottom-color: #4b5563;
}

.team-id {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.avatars {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 20px;
    margin: 24px 0;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 16px;
    border: 1px solid var(--muted-color);
    transition: all 0.3s ease;
}

/* Avatares no modo escuro */
[data-theme="dark"] .avatars {
    background: rgba(31, 41, 55, 0.8);
    border-color: #6b7280;
}

/* Melhorar contraste dos nomes dos avatares */
[data-theme="dark"] .avatar-name {
    color: #ffffff;
    font-weight: 600;
}

/* Melhorar contraste dos avatares no hover */
[data-theme="dark"] .avatar:hover {
    background: rgba(59, 130, 246, 0.2);
}

[data-theme="dark"] .avatar img {
    border-color: #6b7280;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.avatar:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.avatar img {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    margin-bottom: 8px;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    padding: 8px;
    transition: all 0.3s ease;
}

.avatar:hover img {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3);
    animation: float 3s ease-in-out infinite;
}

/* Animação de flutuação para avatares */
@keyframes float {
    0%, 100% {
        transform: scale(1.1) translateY(0px);
    }
    50% {
        transform: scale(1.1) translateY(-5px);
    }
}

.avatar-name {
    font-size: 0.75rem;
    color: var(--foreground-color);
    font-weight: 600;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
    max-width: 80px;
    transition: color 0.3s ease;
}

.toggle {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #004070 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 85, 140, 0.3);
    margin-top: 16px;
}

/* Toggle no modo escuro */
[data-theme="dark"] .toggle {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.toggle:hover {
    background: linear-gradient(135deg, #004070 0%, #003050 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 85, 140, 0.4);
    color: white;
    text-decoration: none;
    animation: glow 2s ease-in-out infinite alternate;
}

/* Animação de brilho para botão toggle */
@keyframes glow {
    from {
        box-shadow: 0 6px 12px rgba(0, 85, 140, 0.4);
    }
    to {
        box-shadow: 0 6px 12px rgba(0, 85, 140, 0.6), 0 0 25px rgba(0, 85, 140, 0.3);
    }
}

/* Toggle hover no modo escuro */
[data-theme="dark"] .toggle:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
}

.inspections {
    list-style: none;
    margin: 20px 0 0 0;
    padding: 0;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--muted-color);
    transition: all 0.3s ease;
}

/* Inspeções no modo escuro */
[data-theme="dark"] .inspections {
    background: rgba(31, 41, 55, 0.9);
    border-color: #6b7280;
}

.inspections li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    font-size: 0.875rem;
    border-bottom: 1px solid var(--muted-color);
    transition: all 0.2s ease;
    color: var(--foreground-color);
}

.inspections li:hover {
    background-color: rgba(59, 130, 246, 0.05);
    animation: ripple 0.6s ease-out;
}

/* Animação de ondulação para itens de inspeção */
@keyframes ripple {
    0% {
        background-color: rgba(59, 130, 246, 0.1);
        transform: scale(1);
    }
    50% {
        background-color: rgba(59, 130, 246, 0.08);
        transform: scale(1.02);
    }
    100% {
        background-color: rgba(59, 130, 246, 0.05);
        transform: scale(1);
    }
}

.inspections li:last-child {
    border-bottom: none;
}

.inspections li > div {
    flex: 1;
    font-weight: 500;
    color: var(--foreground-color);
    transition: color 0.3s ease;
}

/* Itens de inspeção no modo escuro */
[data-theme="dark"] .inspections li {
    border-bottom-color: #6b7280;
    color: #ffffff;
}

[data-theme="dark"] .inspections li:hover {
    background-color: rgba(59, 130, 246, 0.2);
}

[data-theme="dark"] .inspections li > div {
    color: #ffffff;
    font-weight: 600;
}

/* Melhorar contraste dos status no modo escuro */
[data-theme="dark"] .status {
    font-weight: 700;
}

[data-theme="dark"] .status.aberta {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    color: #ffffff;
}

[data-theme="dark"] .status.em-andamento {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: #000000;
    font-weight: 700;
}

[data-theme="dark"] .status.concluida {
    background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
    color: #000000;
    font-weight: 700;
}

.status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status.aberta {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.status.em-andamento {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.status.concluida {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.status .dot {
    width: 6px;
    height: 6px;
    background: currentColor;
    border-radius: 50%;
    display: inline-block;
}

/* Estado vazio */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(248, 250, 252, 0.5) 100%);
    border-radius: 20px;
    border: 2px dashed var(--muted-color);
    margin-top: 32px;
    transition: all 0.3s ease;
}

.empty-state::before {
    content: '👥';
    display: block;
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.125rem;
    color: var(--muted-foreground-color);
    margin: 0;
    font-weight: 500;
    transition: color 0.3s ease;
}

/* Estado vazio no modo escuro */
[data-theme="dark"] .empty-state {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #6b7280;
}

[data-theme="dark"] .empty-state p {
    color: #d1d5db;
    font-weight: 600;
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.team-card {
    animation: fadeInUp 0.6s ease-out;
}

.team-card:nth-child(2) { animation-delay: 0.1s; }
.team-card:nth-child(3) { animation-delay: 0.2s; }
.team-card:nth-child(4) { animation-delay: 0.3s; }

/* Responsividade */
@media (max-width: 768px) {
    .teams-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .container {
        padding: 20px 12px;
    }

    h2 {
        font-size: 1.75rem;
        margin-bottom: 24px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 16px 8px;
    }

    .team-card {
        padding: 20px 16px;
        border-radius: 16px;
    }

    .avatars {
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 12px;
        padding: 16px;
    }

    .avatar img {
        width: 48px;
        height: 48px;
    }

    .avatar-name {
        font-size: 0.7rem;
    }

    .team-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .team-name {
        font-size: 1.125rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .toggle {
        padding: 10px 16px;
        font-size: 0.8rem;
    }

    .inspections li {
        padding: 12px 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}