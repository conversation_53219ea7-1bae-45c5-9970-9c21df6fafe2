<link rel="stylesheet" href="/css/auth.css">
<link rel="stylesheet" href="/css/toast.css">

<div id="toast-container" class="toast-container"></div>

<div class="login-layout">
    <!-- <PERSON><PERSON> esquerdo - Informações do IPT -->
    <div class="login-info-side">
        <div class="info-content">
            <div class="logo-section">
                <img src="/assets/logo-extended.png" alt="IPT Logo" class="main-logo">
                <h2>Instituto de Pesquisas Tecnológicas</h2>
                <p class="tagline">Excelência em Inspeção e Tecnologia</p>
            </div>

            <div class="features-section">
                <h3>Sistema InsPecT</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"/>
                                <path d="M9 11V9a3 3 0 0 1 6 0v2"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <h4>Inspeções Seguras</h4>
                            <p>Controle total com segurança</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <h4>Relatórios Profissionais</h4>
                            <p>Geração automática e detalhada</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <h4>Colaboração</h4>
                            <p>Trabalhe em equipe eficientemente</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lado direito - Formulário de login -->
    <div class="login-form-side">
        <!-- Toggle de tema -->
        <div class="theme-toggle-container">
            <button type="button" class="theme-toggle-btn" onclick="toggleGlobalTheme()" title="Alternar tema">
                <span class="theme-icon-global">🌙</span>
            </button>
        </div>

        <!-- Card do formulário -->
        <div class="form-card">
            <div class="form-container">
                <div class="form-header">
                    <h1>Entrar</h1>
                    <p>Acesse sua conta para continuar</p>
                </div>

                <form id="loginForm">
                    <div class="input-group">
                        <img src="/assets/icons/mail-muted.svg" alt="Email Icon">
                        <input type="email" id="email" placeholder="Email" required>
                        <div class="error-message" id="emailError"></div>
                    </div>

                    <div class="input-group">
                        <img src="/assets/icons/lock-muted.svg" alt="Senha Icon">
                        <input type="password" id="senha" placeholder="Senha" required>
                        <img src="/assets/icons/eye-slash-muted.svg" alt="Mostrar senha" title="Mostrar senha" class="password-toggle" id="passwordToggle" onclick="togglePasswordVisibility()">
                        <div class="error-message" id="senhaError"></div>
                    </div>

                    <div class="options">
                        <label><input type="checkbox" id="lembrar"> Lembrar-me</label>
                        <a href="#">Esqueceu sua senha?</a>
                    </div>

                    <button type="submit" class="btn-entrar">Entrar</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="/js/toast.js"></script>
<script src="/js/login.js"></script>
<script>
// ===== SISTEMA DE TEMA GLOBAL =====

// Inicializar tema ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    initializeGlobalTheme();
});

// Inicializar tema global
function initializeGlobalTheme() {
    const savedTheme = localStorage.getItem('global-theme') || 'light';
    applyGlobalTheme(savedTheme);
}

// Alternar tema global
function toggleGlobalTheme() {
    const currentTheme = document.body.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    applyGlobalTheme(newTheme);
    localStorage.setItem('global-theme', newTheme);
}

// Aplicar tema global
function applyGlobalTheme(theme) {
    const body = document.body;
    const themeIcon = document.querySelector('.theme-icon-global');

    if (theme === 'dark') {
        body.setAttribute('data-theme', 'dark');
        if (themeIcon) themeIcon.textContent = '☀️';
    } else {
        body.removeAttribute('data-theme');
        if (themeIcon) themeIcon.textContent = '🌙';
    }
}

// ===== FUNCIONALIDADE DE MOSTRAR/OCULTAR SENHA =====

function togglePasswordVisibility() {
    const passwordInput = document.getElementById('senha');
    const toggleIcon = document.getElementById('passwordToggle');

    if (passwordInput.type === 'password') {
        // Mostrar senha
        passwordInput.type = 'text';
        toggleIcon.src = '/assets/icons/eye-muted.svg';
        toggleIcon.alt = 'Ocultar senha';
        toggleIcon.title = 'Ocultar senha';
    } else {
        // Ocultar senha
        passwordInput.type = 'password';
        toggleIcon.src = '/assets/icons/eye-slash-muted.svg';
        toggleIcon.alt = 'Mostrar senha';
        toggleIcon.title = 'Mostrar senha';
    }

    // Feedback visual
    toggleIcon.style.transform = 'scale(0.9)';
    setTimeout(() => {
        toggleIcon.style.transform = 'scale(1)';
    }, 150);
}
</script>