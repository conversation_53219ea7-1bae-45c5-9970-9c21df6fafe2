<link rel="stylesheet" href="/css/auth.css">
<link rel="stylesheet" href="/css/toast.css">

<div id="toast-container" class="toast-container"></div>

<div class="login-layout">
    <!-- <PERSON><PERSON> esquerdo - Informações do IPT -->
    <div class="login-info-side">
        <div class="info-content">
            <div class="logo-section">
                <img src="/assets/logo-extended.png" alt="IPT Logo" class="main-logo">
                <h2>Instituto de Pesquisas Tecnológicas</h2>
                <p class="tagline">Excelência em Inspeção e Tecnologia</p>
            </div>

            <div class="features-section">
                <h3>Sistema InsPecT</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"/>
                                <path d="M9 11V9a3 3 0 0 1 6 0v2"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <h4>Inspeções Seguras</h4>
                            <p>Controle total com segurança</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <h4>Relatórios Profissionais</h4>
                            <p>Geração automática e detalhada</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <div class="feature-text">
                            <h4>Colaboração</h4>
                            <p>Trabalhe em equipe eficientemente</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lado direito - Formulário de login -->
    <div class="login-form-side">
        <div class="form-container">
            <!-- Toggle de tema -->
            <div class="theme-toggle-container">
                <button type="button" class="theme-toggle-btn" onclick="toggleTheme()">
                    <svg class="theme-icon sun-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5"/>
                        <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                    </svg>
                    <svg class="theme-icon moon-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
            </div>

            <div class="form-header">
                <h1>Entrar</h1>
                <p>Acesse sua conta para continuar</p>
            </div>

            <form id="loginForm">
                <div class="input-group">
                    <img src="/assets/icons/mail-muted.svg" alt="Email Icon">
                    <input type="email" id="email" placeholder="Email" required>
                    <div class="error-message" id="emailError"></div>
                </div>

                <div class="input-group">
                    <img src="/assets/icons/lock-muted.svg" alt="Senha Icon">
                    <input type="password" id="senha" placeholder="Senha" required>
                    <img src="/assets/icons/eye-slash-muted.svg" alt="Senha Icon">
                    <div class="error-message" id="senhaError"></div>
                </div>

                <div class="options">
                    <label><input type="checkbox" id="lembrar"> Lembrar-me</label>
                    <a href="#">Esqueceu sua senha?</a>
                </div>

                <button type="submit" class="btn-entrar">Entrar</button>
            </form>
        </div>
    </div>
</div>

<script src="/js/toast.js"></script>
<script src="/js/login.js"></script>
<script>
// Função para alternar tema
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
}

// Carregar tema salvo
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
});
</script>