header,
.sidebar {
    display: none !important
}

body,
main {
    background-color: var(--background-color);
    color: var(--foreground-color);
    transition: all 0.3s ease;
}

/* Body e main no modo escuro */
[data-theme="dark"] body,
[data-theme="dark"] main {
    background-color: var(--background-color);
}

.page-container {
    background-color: var(--background-color, #FEFEFE);
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 420px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, .1);
    color: var(--foreground-color);
    transition: all 0.3s ease;
}

/* Page container no modo escuro */
[data-theme="dark"] .page-container {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, #004070 100%);
    color: var(--background-color);
    padding: 0 16px;
    height: 64px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

/* Page header no modo escuro */
[data-theme="dark"] .page-header {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: #ffffff;
    border-bottom: 1px solid #374151;
}

.page-header .page-title {
    color: var(--background-color);
    font-weight: 600;
    transition: color 0.3s ease;
}

/* Page title no modo escuro */
[data-theme="dark"] .page-header .page-title {
    color: #ffffff;
}

.header-btn {
    background: 0 0;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: var(--background-color);
    transition: all 0.3s ease;
    border-radius: 8px;
}

.header-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

/* Header buttons no modo escuro */
[data-theme="dark"] .header-btn {
    color: #ffffff;
}

[data-theme="dark"] .header-btn:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Ícones dos botões no modo escuro */
[data-theme="dark"] .header-btn img {
    filter: brightness(1.2) contrast(1.1);
}

.form-scroll-container {
    flex-grow: 1;
    overflow-y: auto;
    padding-bottom: 96px
}

.form-body {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.form-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--foreground-color)
}

.upload-area {
    border: 2px dashed var(--muted-color);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    background-color: #f9fafb;
    min-height: 210px;
    transition: all 0.3s ease;
    color: var(--foreground-color);
}

.upload-area:hover {
    background-color: #f3f4f6
}

/* Upload area no modo escuro */
[data-theme="dark"] .upload-area {
    background-color: #374151;
    border-color: #6b7280;
    color: #d1d5db;
}

[data-theme="dark"] .upload-area:hover {
    background-color: #4b5563;
}

.upload-icons {
    display: flex;
    gap: 32px;
    margin-bottom: 12px
}

.icon-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--muted-foreground-color);
    font-size: .8rem
}

.icon-wrapper svg {
    stroke: var(--muted-foreground-color)
}

input[type=file] {
    display: none
}

.upload-text {
    font-size: .8rem;
    color: #9ca3af
}

.image-preview {
    max-height: 120px;
    width: auto;
    max-width: 100%;
    border-radius: 8px;
    margin-bottom: 12px;
    object-fit: cover
}

.preview-info {
    font-size: .9rem;
    font-weight: 500;
    color: var(--foreground-color);
    word-break: break-all
}

.remove-selection-btn {
    background: 0 0;
    border: none;
    color: var(--destructive-color, #E53935);
    text-decoration: underline;
    cursor: pointer;
    font-size: .8rem;
    margin-top: 8px;
    padding: 4px
}

input[type=text],
textarea,
select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--muted-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    background-color: var(--background-color);
    color: var(--foreground-color);
    transition: all 0.3s ease;
}

/* Inputs no modo escuro */
[data-theme="dark"] input[type=text],
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background-color: #374151;
    border-color: #6b7280;
    color: #ffffff;
}

[data-theme="dark"] input[type=text]:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

textarea {
    min-height: 80px;
    resize: vertical
}

select {
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right .7rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem
}

select:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
    color: #9ca3af
}

/* Select disabled no modo escuro */
[data-theme="dark"] select:disabled {
    background-color: #4b5563;
    color: #9ca3af;
}

.selection-prompt {
    font-size: .8rem;
    color: var(--muted-foreground-color);
    margin-top: 4px;
    margin-bottom: 8px
}

.checklist-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    background-color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--muted-color);
    transition: all 0.3s ease;
}

/* Checklist container no modo escuro */
[data-theme="dark"] .checklist-container {
    background-color: #374151;
    border-color: #6b7280;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: .9rem;
    color: var(--foreground-color);
    transition: color 0.3s ease;
}

/* Checklist items no modo escuro */
[data-theme="dark"] .checklist-item {
    color: #d1d5db;
}

.checklist-item input[type=checkbox] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color)
}

.btn-add-item {
    border: 1px dashed var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    padding: 10px;
    border-radius: 8px;
    font-size: .9rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

/* Botão adicionar item no modo escuro */
[data-theme="dark"] .btn-add-item {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

[data-theme="dark"] .btn-add-item:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

/* ===== MODAL STYLES ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

/* Modal no modo escuro */
[data-theme="dark"] .modal-content {
    background: #1f2937;
    color: #f9fafb;
}

.modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

[data-theme="dark"] .modal-header {
    border-bottom-color: #374151;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

[data-theme="dark"] .modal-close {
    color: #9ca3af;
}

[data-theme="dark"] .modal-close:hover {
    background-color: #374151;
    color: #f3f4f6;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

[data-theme="dark"] .modal-footer {
    border-top-color: #374151;
}

.btn-secondary {
    background: #f9fafb;
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

[data-theme="dark"] .btn-secondary {
    background: #374151;
    color: #d1d5db;
    border-color: #4b5563;
}

[data-theme="dark"] .btn-secondary:hover {
    background: #4b5563;
    border-color: #6b7280;
}

.form-footer {
    position: sticky;
    bottom: 0;
    padding: 16px 24px;
    background-color: var(--background-color);
    border-top: 1px solid var(--muted-color);
    box-shadow: 0 -4px 12px rgba(0, 0, 0, .05);
    transition: all 0.3s ease;
}

/* Form footer no modo escuro */
[data-theme="dark"] .form-footer {
    background-color: var(--background-color);
    border-top-color: #4b5563;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.3);
}

@media (min-width:768px) {
    .page-container {
        max-width: none;
        height: auto;
        min-height: auto;
        margin: 0;
        box-shadow: none
    }

    .page-header {
        position: static
    }

    .form-scroll-container {
        overflow-y: visible;
        padding-bottom: 24px
    }

    .form-body {
        max-width: 900px;
        margin: 0 auto;
        padding: 48px
    }

    .form-footer {
        position: static;
        box-shadow: none;
        border-top: none;
        padding: 0 24px 24px;
        background-color: transparent;
        display: flex;
        justify-content: flex-end;
        max-width: 900px;
        margin: 0 auto
    }

    .form-footer .btn-primary {
        width: auto;
        min-width: 250px
    }
}