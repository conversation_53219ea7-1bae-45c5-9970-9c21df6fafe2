const HistoricoModel = require('../models/historicoModel');
const InspecaoModel = require('../models/inspecoesModel');

class HistoricoService {
    
    async listarHistoricoPorInspecao(id_inspecao) {
        try {
            
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
            }

            const historico = await HistoricoModel.findByInspecaoId(id_inspecao);
            if (!historico || historico.length === 0) {
                throw new Error('Histórico não encontrado para esta inspeção.');
            }

            return historico;
        } catch (error) {
            console.error('[HISTORICO_SERVICE_ERROR] listarHistoricoPorInspecao:', error);
            throw error;
        }
    }

    async criarHistorico(dadosHistorico) {
        try {
            const { id_inspecao, status_id } = dadosHistorico;

            if (!id_inspecao || !status_id) {
                throw new Error('ID da inspeção e status são obrigatórios.');
            }

            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
            }

            const novoHistorico = await HistoricoModel.create({
                id_inspecao: parseInt(id_inspecao),
                status_id
            });

            return novoHistorico;
        } catch (error) {
            console.error('[HISTORICO_SERVICE_ERROR] criarHistorico:', error);
            throw error;
        }
    }

    async obterStatusAtual(id_inspecao) {
        try {
            const historico = await HistoricoModel.findByInspecaoId(id_inspecao);
            
            if (historico && historico.length > 0) {
                
                return historico[0].status_id;
            }
            
            return 'Aberta';
        } catch (error) {
            console.error('[HISTORICO_SERVICE_ERROR] obterStatusAtual:', error);
            return 'Aberta';
        }
    }

    async deletarHistoricoPorInspecao(id_inspecao) {
        try {
            
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
            }

            const deletado = await HistoricoModel.deleteByInspecaoId(id_inspecao);
            return deletado;
        } catch (error) {
            console.error('[HISTORICO_SERVICE_ERROR] deletarHistoricoPorInspecao:', error);
            throw error;
        }
    }
}

module.exports = new HistoricoService();