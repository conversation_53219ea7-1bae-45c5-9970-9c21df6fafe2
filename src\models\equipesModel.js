const db = require('../config/db');

const Equipe = {
  async create({ nome }) {
    const query = `
      INSERT INTO equipes (nome)
      VALUES ($1)
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao criar equipe:', error);
      throw error;
    }
  },

  async findAll() {
    const query = 'SELECT * FROM equipes ORDER BY nome;';
    try {
      const { rows } = await db.query(query);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar equipes:', error);
      throw error;
    }
  },

  async findById(id) {

    const equipeQuery = 'SELECT * FROM equipes WHERE id = $1;';
    const usuariosQuery = `
      SELECT u.id AS id_usuario, u.nome AS nome_usuario, uce.id_cargo AS cargo_nome
      FROM usuarios u
      JOIN usuarios_cargos_equipes uce ON u.id = uce.id_usuario
      WHERE uce.id_equipe = $1;
    `;
    try {
      const equipeResult = await db.query(equipeQuery, [id]);
      if (equipeResult.rows.length === 0) {
        return null;
      }
      const equipe = equipeResult.rows[0];

      const usuariosResult = await db.query(usuariosQuery, [id]);
      equipe.usuarios = usuariosResult.rows; 

      return equipe;
    } catch (error) {
      console.error('Erro ao buscar equipe por ID com usuários:', error);
      throw error;
    }
  },

  async update(id, { nome }) {
    const query = `
      UPDATE equipes
      SET nome = $1
      WHERE id = $2
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [nome, id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao atualizar equipe:', error);
      throw error;
    }
  },

  async remove(id) {
    
    const deleteAssociationsQuery = 'DELETE FROM usuarios_cargos_equipes WHERE id_equipe = $1;';
    const deleteEquipeQuery = 'DELETE FROM equipes WHERE id = $1 RETURNING id;';
    try {
      await db.query(deleteAssociationsQuery, [id]); 
      const { rows } = await db.query(deleteEquipeQuery, [id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao remover equipe:', error);
      throw error;
    }
  },

  async addUserToEquipe(id_equipe, id_usuario, id_cargo) {
    const query = `
      INSERT INTO usuarios_cargos_equipes (id_equipe, id_usuario, id_cargo)
      VALUES ($1, $2, $3)
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [id_equipe, id_usuario, id_cargo]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao adicionar usuário à equipe:', error);
      throw error;
    }
  },

  async removeUserFromEquipe(id_equipe, id_usuario) {
    const query = `
      DELETE FROM usuarios_cargos_equipes
      WHERE id_equipe = $1 AND id_usuario = $2
      RETURNING id;
    `;
    try {
      const { rows } = await db.query(query, [id_equipe, id_usuario]);
      return rows.length > 0 ? { id_equipe, id_usuario, removido: true } : null;
    } catch (error) {
      console.error('Erro ao remover usuário da equipe:', error);
      throw error;
    }
  },

   async findUsersByEquipeId(id_equipe) {
     const query = `
       SELECT u.id AS id_usuario, u.nome AS nome_usuario, uce.id_cargo AS cargo_nome
       FROM usuarios u
       JOIN usuarios_cargos_equipes uce ON u.id = uce.id_usuario
       WHERE uce.id_equipe = $1;
     `;
     try {
       const { rows } = await db.query(query, [id_equipe]);
       return rows;
     } catch (error) {
       console.error('Erro ao buscar usuários da equipe:', error);
       throw error;
     }
   }
};

module.exports = Equipe;