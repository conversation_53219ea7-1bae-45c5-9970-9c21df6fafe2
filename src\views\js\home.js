let allInspections = [];
let filteredInspections = [];
let currentFilter = 'todos';
let currentSearch = '';
let currentView = 'grid';

function navigateTo(url) {
  window.location.href = url;
}

// Inicializar AOS (Animate On Scroll)
document.addEventListener('DOMContentLoaded', function() {
  AOS.init({
    duration: 800,
    easing: 'ease-out-cubic',
    once: true,
    offset: 100
  });

  // Inicializar funcionalidades
  initializeEventListeners();
  loadInspections();
  animateStats();
});

// Event Listeners
function initializeEventListeners() {
  // Toggle de visualização
  document.querySelectorAll('.toggle-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      document.querySelectorAll('.toggle-btn').forEach(b => b.classList.remove('active'));
      this.classList.add('active');
      currentView = this.dataset.view;
      updateViewMode();
    });
  });

  // Filtros
  document.querySelectorAll('.filter-tag').forEach(tag => {
    tag.addEventListener('click', function() {
      document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
      this.classList.add('active');
      currentFilter = this.dataset.filter;
      filterInspections();
    });
  });

  // Busca
  const searchInput = document.getElementById('search-input');
  const searchClear = document.getElementById('search-clear');

  searchInput.addEventListener('input', function() {
    currentSearch = this.value;
    searchClear.style.display = this.value ? 'block' : 'none';
    filterInspections();
  });

  searchClear.addEventListener('click', function() {
    searchInput.value = '';
    currentSearch = '';
    this.style.display = 'none';
    filterInspections();
  });
}

// Animar estatísticas
function animateStats() {
  const animateNumber = (element, target, duration = 2000) => {
    const start = 0;
    const increment = target / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      element.textContent = Math.floor(current);
    }, 16);
  };

  // Animar quando as estatísticas estiverem visíveis
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const totalEl = document.getElementById('total-inspections');
        const activeEl = document.getElementById('active-inspections');
        const completedEl = document.getElementById('completed-inspections');

        setTimeout(() => animateNumber(totalEl, allInspections.length), 200);
        setTimeout(() => animateNumber(activeEl, allInspections.filter(i => i.status === 'Em Andamento').length), 400);
        setTimeout(() => animateNumber(completedEl, allInspections.filter(i => i.status === 'Concluída').length), 600);

        observer.disconnect();
      }
    });
  });

  const statsSection = document.querySelector('.hero-stats');
  if (statsSection) {
    observer.observe(statsSection);
  }
}

// Atualizar modo de visualização
function updateViewMode() {
  const inspectionList = document.getElementById('inspection-list');
  inspectionList.className = `inspection-list ${currentView}-view`;
  renderInspections();
}

async function getAddressFromCep(idEndereco) {
  try {
    const token = localStorage.getItem('authToken');
    const response0 = await fetch(`/api/enderecos/${idEndereco}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response0.status === 401) {
      window.location.href = '/';
      return 'Endereço não encontrado';
    }
    if (!response0.ok) throw new Error('Failed to fetch address');
    const endereco = await response0.json();
    const response = await fetch(`https://viacep.com.br/ws/${endereco.cep}/json/`);
    if (!response.ok) throw new Error('Não foi possível conectar ao serviço de CEP.');
    const data = await response.json();
    if (data.erro) throw new Error('CEP não encontrado.');
    return `${data.logradouro}, ${data.bairro}, ${data.localidade} - ${data.uf}`;
  } catch (error) {
    console.error('Error fetching address:', error);
    return 'Endereço não encontrado';
  }
}

function normalizeStatus(status) {
  if (!status) return 'Não Iniciada';
  const normalized = status.toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/ã/g, 'a')
    .replace(/í/g, 'i')
    .replace(/ú/g, 'u');

  return normalized;
}

function getStatusDisplay(status) {
  if (!status) return 'NÃO INICIADA';
  const statusMap = {
    'Em Andamento': 'EM ANDAMENTO',
    'Não Iniciada': 'NÃO INICIADA',
    'Concluída': 'CONCLUÍDO',
    'Cancelada': 'CANCELADA',
    'Aberta': 'ABERTA',
  };
  return statusMap[status] || status.toUpperCase();
}

function getStatusColor(status) {
  if (!status) return '#3B82F6';
  const statusMap = {
    'Em Andamento': '#F59E0B',
    'Não Iniciada': '#3B82F6',
    'Concluída': '#10B981',
    'Cancelada': '#EF4444',
    'Aberta': '#4C718B',
  };
  return statusMap[status] || '#E5E7EB';
}

// Carregar inspeções
async function loadInspections() {
  try {
    const token = localStorage.getItem('authToken');
    if (!token) {
      window.location.href = '/';
      return;
    }

    const response = await fetch('/api/inspections', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 401) {
      window.location.href = '/';
      return;
    }

    if (!response.ok) {
      throw new Error('Erro ao carregar inspeções');
    }

    const data = await response.json();
    if (data.success) {
      allInspections = data.data;

      // Carregar endereços para cada inspeção
      for (let inspection of allInspections) {
        if (inspection.id_endereco) {
          inspection.address = await getAddressFromCep(inspection.id_endereco);
        }
      }

      filteredInspections = [...allInspections];
      renderInspections();
      animateStats(); // Reanimar estatísticas com dados reais
    }
  } catch (error) {
    console.error('Erro ao carregar inspeções:', error);
    showError('Erro ao carregar inspeções');
  }
}

// Filtrar inspeções
function filterInspections() {
  filteredInspections = allInspections.filter(inspection => {
    const matchesFilter = currentFilter === 'todos' ||
      normalizeStatus(inspection.status) === currentFilter;

    const matchesSearch = currentSearch === '' ||
      (inspection.nome && inspection.nome.toLowerCase().includes(currentSearch.toLowerCase())) ||
      (inspection.address && inspection.address.toLowerCase().includes(currentSearch.toLowerCase()));

    return matchesFilter && matchesSearch;
  });

  renderInspections();
}

// Renderizar inspeções com novo design
function renderInspections() {
  const inspectionList = document.getElementById('inspection-list');

  if (filteredInspections.length === 0) {
    inspectionList.innerHTML = `
      <div class="no-results">
        <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
        <h3>Nenhuma inspeção encontrada</h3>
        <p>Tente ajustar os filtros ou criar uma nova inspeção</p>
      </div>`;
    return;
  }

  // Renderizar com animação escalonada
  const renderWithDelay = async () => {
    const cards = await Promise.all(filteredInspections.map(async (inspection, index) => {
      const statusNormalized = normalizeStatus(inspection.status);
      const statusDisplay = getStatusDisplay(inspection.status);
      const date = inspection.criado_em ?
        new Date(inspection.criado_em).toLocaleDateString('pt-BR') :
        new Date().toLocaleDateString('pt-BR');

      const address = inspection.address || 'Endereço não disponível';

      return `
        <div class="inspection-card"
             data-inspection-id="${inspection.id || index}"
             onclick="navigateTo('/inspection-details/${inspection.id}')"
             style="animation-delay: ${index * 100}ms"
             tabindex="0"
             role="button"
             aria-label="Abrir detalhes da inspeção ${inspection.nome}">
          <div class="top">
            <h3>${inspection.nome || 'Inspeção sem nome'}</h3>
            <div class="status" data-status="${statusDisplay}">
              ${statusDisplay}
            </div>
          </div>
          <div class="middle">
            <p>${address}</p>
          </div>
          <div class="bottom">
            <div class="date">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 16px; height: 16px;">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                <line x1="16" y1="2" x2="16" y2="6"/>
                <line x1="8" y1="2" x2="8" y2="6"/>
                <line x1="3" y1="10" x2="21" y2="10"/>
              </svg>
              <span>${date}</span>
            </div>
            <div class="icons">
              <img src="/assets/icons/user-placeholder.png" alt="Usuário" onerror="this.style.display='none'">
            </div>
          </div>
        </div>`;
    }));

    inspectionList.innerHTML = cards.join('');

    // Adicionar event listeners para acessibilidade
    document.querySelectorAll('.inspection-card').forEach(card => {
      card.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.click();
        }
      });
    });
  };

  renderWithDelay().catch(error => {
    console.error('Erro ao renderizar inspeções:', error);
    inspectionList.innerHTML = `
      <div class="no-results">
        <div style="font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
        <h3>Erro ao carregar inspeções</h3>
        <p>Tente recarregar a página</p>
      </div>`;
  });
}

// Mostrar erro
function showError(message) {
  const inspectionList = document.getElementById('inspection-list');
  inspectionList.innerHTML = `
    <div class="no-results">
      <div style="font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
      <h3>Erro</h3>
      <p>${message}</p>
    </div>`;
}

// Função para adicionar efeitos de hover suaves
function addHoverEffects() {
  document.querySelectorAll('.action-card, .stat-card, .inspection-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-8px) scale(1.02)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0) scale(1)';
    });
  });
}

// Função para scroll suave
function smoothScrollTo(element) {
  element.scrollIntoView({
    behavior: 'smooth',
    block: 'start'
  });
}

// Função para atualizar estatísticas em tempo real
function updateStats() {
  const totalEl = document.getElementById('total-inspections');
  const activeEl = document.getElementById('active-inspections');
  const completedEl = document.getElementById('completed-inspections');

  if (totalEl) totalEl.textContent = allInspections.length;
  if (activeEl) activeEl.textContent = allInspections.filter(i => i.status === 'Em Andamento').length;
  if (completedEl) completedEl.textContent = allInspections.filter(i => i.status === 'Concluída').length;
}

// Função para detectar tema escuro
function detectDarkMode() {
  const isDark = document.documentElement.getAttribute('data-theme') === 'dark' ||
                 window.matchMedia('(prefers-color-scheme: dark)').matches;

  if (isDark) {
    document.body.classList.add('dark-mode');
  }
}

// Inicializar quando a página carregar
window.addEventListener('load', function() {
  detectDarkMode();
  addHoverEffects();

  // Adicionar efeito de parallax sutil às formas flutuantes
  window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const shapes = document.querySelectorAll('.shape');

    shapes.forEach((shape, index) => {
      const speed = 0.5 + (index * 0.1);
      shape.style.transform = `translateY(${scrolled * speed}px)`;
    });
  });
});