let allInspections = [];
let filteredInspections = [];
let currentFilter = 'todos';
let currentSearch = '';

function navigateTo(url) {
  window.location.href = url;
}

async function getAddressFromCep(idEndereco) {
  try {
    const token = localStorage.getItem('authToken');
    const response0 = await fetch(`/api/enderecos/${idEndereco}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response0.status === 401) {
      window.location.href = '/';
      return 'Endereço não encontrado';
    }
    if (!response0.ok) throw new Error('Failed to fetch address');
    const endereco = await response0.json();
    const response = await fetch(`https://viacep.com.br/ws/${endereco.cep}/json/`);
    if (!response.ok) throw new Error('Não foi possível conectar ao serviço de CEP.');
    const data = await response.json();
    if (data.erro) throw new Error('CEP não encontrado.');
    return `${data.logradouro}, ${data.bairro}, ${data.localidade} - ${data.uf}`;
  } catch (error) {
    console.error('Error fetching address:', error);
    return 'Endereço não encontrado';
  }
}

function normalizeStatus(status) {
  if (!status) return 'Não Iniciada';
  const normalized = status.toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/ã/g, 'a')
    .replace(/í/g, 'i')
    .replace(/ú/g, 'u');

  return normalized;
}

function getStatusDisplay(status) {
  if (!status) return 'NÃO INICIADA';
  const statusMap = {
    'Em Andamento': 'EM ANDAMENTO',
    'Não Iniciada': 'NÃO INICIADA',
    'Concluída': 'CONCLUÍDO',
    'Cancelada': 'CANCELADA',
    'Aberta': 'ABERTA',
  };
  return statusMap[status] || status.toUpperCase();
}

function getStatusColor(status) {
  if (!status) return '#3B82F6';
  const statusMap = {
    'Em Andamento': '#F59E0B',
    'Não Iniciada': '#3B82F6',
    'Concluída': '#10B981',
    'Cancelada': '#EF4444',
    'Aberta': '#4C718B',
  };
  return statusMap[status] || '#E5E7EB';
}

function filterInspections() {
  filteredInspections = allInspections.filter(inspection => {
    const matchesFilter = currentFilter === 'todos' ||
      normalizeStatus(inspection.status) === currentFilter;

    const matchesSearch = currentSearch === '' ||
      (inspection.nome && inspection.nome.toLowerCase().includes(currentSearch.toLowerCase())) ||
      (inspection.address && inspection.address.toLowerCase().includes(currentSearch.toLowerCase()));

    return matchesFilter && matchesSearch;
  });

  renderInspections();
}

function renderInspections() {
  const inspectionList = document.getElementById('inspection-list');
  if (filteredInspections.length === 0) {
    inspectionList.innerHTML = '<div class="no-results">Nenhuma inspeção encontrada</div>';
    return;
  }

  Promise.all(filteredInspections.map(async (inspection, index) => {
    const statusNormalized = normalizeStatus(inspection.status);
    const statusDisplay = getStatusDisplay(inspection.status);
    const date = inspection.criado_em ?
      new Date(inspection.criado_em).toLocaleDateString('pt-BR') : '07/11';

    const address = inspection.id_endereco ?
      await getAddressFromCep(inspection.id_endereco) :
      (inspection.address || 'Endereço não disponível');

    return `
      <div class="inspection-card" data-inspection-id="${inspection.id || index}" onclick="navigateTo('/inspection-details/${inspection.id}')">
        <div class="top">
        <h3 class="subtitle1">${inspection.nome || 'Quadra Butantã'}</h3>
        </div>
        <div class="middle">
        <p class="text-muted-foreground body2">${address}</p>
        </div>
        <div class="bottom">
        <span class="status" data-status="${statusNormalized}">
          <span style="background-color: ${getStatusColor(inspection.status)};"></span>
          <p class="body3">${statusDisplay}</p>
        </span>
        <span class="date">
          <p class="body2 text-muted-foreground">${date} 📅</p>
        </span>
        </div>
      </div>
      ${index < filteredInspections.length - 1 ? '<div class="list-separator"></div>' : ''}`
  })).then(elements => {
    inspectionList.innerHTML = elements.join('');
  }).catch(error => {
    console.error('Error rendering inspections:', error);
    inspectionList.innerHTML = '<div class="error">Erro ao carregar os endereços</div>';
  });
}

document.querySelectorAll('.tag').forEach(tag => {
  tag.addEventListener('click', () => {
    document.querySelectorAll('.tag').forEach(t => t.classList.remove('active'));
    tag.classList.add('active');

    currentFilter = tag.dataset.filter;
    filterInspections();
  });
});

document.getElementById('search-input').addEventListener('input', (e) => {
  currentSearch = e.target.value.trim();
  filterInspections();
});

async function fetchInspections() {
  try {
    const token = localStorage.getItem('authToken');
    if (!token) {
      window.location.href = '/';
      return;
    }

    const response = await fetch('/api/inspecoes', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 401) {
      window.location.href = '/';
      return;
    }

    if (!response.ok) {
      throw new Error('Failed to fetch inspections');
    }

    allInspections = await response.json();

    if (!Array.isArray(allInspections)) {
      throw new Error('Invalid data format');
    }

    filteredInspections = [...allInspections];
    renderInspections();
  } catch (error) {
    console.error('Error fetching inspections:', error);

    filteredInspections = [];
    renderInspections();
  }
}

fetchInspections();