<link rel="stylesheet" href="/css/add-ambient.css" />
<link rel="stylesheet" href="/css/toast.css">

<div class="page-container">

    <div class="page-header">
        <button class="header-btn" onclick="window.history.back()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M19 12H5"></path>
                <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
        </button>
        <h1 class="h5 page-title">Adicionar ambiente</h1>
        <a href="/home" class="header-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
        </a>
    </div>

    <div class="form-scroll-container">
        <form id="addAmbientForm" class="form-body">

            <div class="form-group">
                <label for="inspecaoSelect" class="form-label">Inspeção</label>
                <select id="inspecaoSelect" name="inspecao" required>
                    <option value="" disabled selected>Carregando inspeções...</option>
                </select>
            </div>

            <div class="form-group" id="ambientePaiGroup" style="display: none;">
                <label for="ambientePaiSelect" class="form-label">Ambiente Pai (Opcional)</label>
                <select id="ambientePaiSelect" name="ambientePai" disabled>
                    <option value="">Nenhum (ambiente principal)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="imageUpload" class="upload-area">
                    <div class="upload-icons">
                        <div class="icon-wrapper">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"
                                fill="none" stroke="#9ca3af" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path
                                    d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z">
                                </path>
                                <circle cx="12" cy="13" r="4"></circle>
                            </svg>
                            <span>Abrir câmera</span>
                        </div>
                        <div class="icon-wrapper">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"
                                fill="none" stroke="#9ca3af" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                <polyline points="21 15 16 10 5 21"></polyline>
                            </svg>
                            <span>Adicionar imagem</span>
                        </div>
                    </div>
                    <span class="upload-text">Escolha ou arraste uma imagem aqui</span>
                    <input type="file" id="imageUpload" name="fotos" accept="image/*" multiple>
                </label>
            </div>

            <div class="form-group">
                <label for="title" class="form-label">Título do Ambiente</label>
                <input type="text" id="title" name="title" placeholder="Ex: Cozinha, Fachada Principal" required>
            </div>

            <div class="form-group">
                <label for="observation" class="form-label">Observação</label>
                <textarea id="observation" name="observation" placeholder="Descreva aqui (opcional)"></textarea>
            </div>

            <div class="form-group">
                <label class="form-label">Lista de afazeres</label>
                <p class="selection-prompt">
                    Um ambiente só será marcado como concluído quando todos os itens forem marcados.
                </p>
                <div class="checklist-container" id="afazeres-list">
                </div>
                <button type="button" class="btn-add-item" onclick="openAfazerModal()">+ Adicionar item</button>
            </div>

        </form>
    </div>

    <div class="form-footer">
        <button type="submit" form="addAmbientForm" class="btn-primary">Adicionar</button>
    </div>
</div>

<!-- Modal para adicionar novo afazer -->
<div id="afazerModal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Adicionar Novo Afazer</h3>
            <button type="button" class="modal-close" onclick="closeAfazerModal()">&times;</button>
        </div>
        <form id="afazerForm" onsubmit="event.preventDefault(); saveAfazer();">
            <div class="modal-body">
                <div class="form-group">
                    <label for="afazerTitulo" class="form-label">Título do Afazer</label>
                    <input type="text" id="afazerTitulo" name="titulo" placeholder="Ex: Verificar instalação elétrica" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeAfazerModal()">Cancelar</button>
                <button type="submit" class="btn-primary">Salvar</button>
            </div>
        </form>
    </div>
</div>

<script src="/js/toast.js"></script>
<script src="/js/add-ambient.js"></script>