const exibirErro = (mensagem) => {
    const errorContainer = document.getElementById('error-container');
    const mainContent = document.getElementById('main-content');
    errorContainer.textContent = `❌ Erro: ${mensagem}`;
    errorContainer.classList.remove('hidden');
    mainContent.classList.add('hidden');
};

const renderizarAmbientesFilhos = (container, ambientes, fotosDosFilhos, urlsAssinadasPorId) => {
    container.innerHTML = '';
    if (!ambientes || ambientes.length === 0) {
        container.innerHTML = '<p class="empty-text">Nenhum sub-ambiente encontrado.</p>';
        container.classList.add('hidden');
        document.getElementById('child-ambients-title').classList.add('hidden');
        document.querySelector('.section-divider').classList.add('hidden');
        return;
    }
    ambientes.forEach((ambiente, index) => {
        const fotosDoAmbienteFilho = fotosDosFilhos[index] || [];
        const fotoBanner = fotosDoAmbienteFilho.find(f => !f.id_ocorrencia);
        const imageUrl = fotoBanner ? (urlsAssinadasPorId[fotoBanner.id] || '/assets/templates/ambient-banner.png') : '/assets/templates/ambient-banner.png';
        const cardLink = document.createElement('a');
        cardLink.href = `/inspection-details/${ambiente.id_inspecao}/ambient-details/${ambiente.id}`;
        cardLink.className = 'ambient-card-h';
        cardLink.innerHTML = `
            <img src="${imageUrl}" alt="${ambiente.titulo}" class="card-image">
            <h4 class="card-title">${ambiente.titulo}</h4>`;
        container.appendChild(cardLink);
    });
};

const renderizarOcorrencias = (container, ocorrencias, fotos, urlsAssinadasPorId) => {
    container.innerHTML = '';
    if (!ocorrencias || ocorrencias.length === 0) {
        container.innerHTML = '<p class="empty-text">Nenhuma ocorrência encontrada.</p>';
        return;
    }
    const fotosPorOcorrencia = fotos.reduce((acc, foto) => {
        if (foto.id_ocorrencia) {
            if (!acc[foto.id_ocorrencia]) acc[foto.id_ocorrencia] = [];
            acc[foto.id_ocorrencia].push(foto);
        }
        return acc;
    }, {});
    ocorrencias.forEach(ocorrencia => {
        const fotosDaOcorrencia = fotosPorOcorrencia[ocorrencia.id] || [];
        const imageUrl = fotosDaOcorrencia.length > 0 ? (urlsAssinadasPorId[fotosDaOcorrencia[0].id] || '/assets/templates/occurrence-banner.png') : '/assets/templates/occurrence-banner.png';
        const cardLink = document.createElement('a');
        cardLink.href = `/inspection-details/${ocorrencia.id_inspecao}/ambient-details/${ocorrencia.id_ambiente}/occurrence-details/${ocorrencia.id}`;
        cardLink.className = 'ocorrencia-card-v';
        cardLink.innerHTML = `
            <img src="${imageUrl}" alt="${ocorrencia.titulo}" class="card-image">
            <div class="card-content">
                <div class="title-date">
                    <h4 class="subtitle1">${ocorrencia.titulo}</h4>
                    <span class="date body2">${new Date(ocorrencia.criado_em).toLocaleDateString('pt-BR')}</span>
                </div>
            </div>`;
        container.appendChild(cardLink);
    });
};

const carregarDadosDaPagina = async () => {
    try {
        const token = localStorage.getItem('authToken');
        if (!token) return exibirErro('Autenticação necessária.');

        const pathParts = window.location.pathname.split('/');
        const idAmbiente = parseInt(pathParts[pathParts.length - 1], 10);
        if (isNaN(idAmbiente)) throw new Error('ID do ambiente inválido.');

        const headers = { 'Authorization': `Bearer ${token}` };
        const fetchJson = async (url) => {
            const response = await fetch(url, { headers });
            if (response.status === 401) {
                window.location.href = '/';
                return [];
            }
            if (!response.ok) return [];
            return response.json();
        };

        const dadosAmbiente = await fetchJson(`/api/ambientes/${idAmbiente}`);
        if (!dadosAmbiente || !dadosAmbiente.id_inspecao) throw new Error('Falha ao buscar dados do ambiente.');

        

        const [dadosOcorrencias, dadosFotosDoAmbienteAtual, todosAmbientesDaInspecao] = await Promise.all([
            fetchJson(`/api/ambientes/${idAmbiente}/ocorrencias`),
            fetchJson(`/api/fotos/ambiente/${idAmbiente}`),
            fetchJson(`/api/inspecoes/${dadosAmbiente.id_inspecao}/ambientes`)
        ]);

        const dadosAmbientesFilhos = todosAmbientesDaInspecao.filter(a => a.id_ambiente_pai === idAmbiente);

        const fotosDosFilhosPromises = dadosAmbientesFilhos.map(filho => fetchJson(`/api/fotos/ambiente/${filho.id}`));
        const fotosDosFilhos = await Promise.all(fotosDosFilhosPromises);

        document.getElementById('ambient-title').textContent = dadosAmbiente.titulo;
        document.getElementById('ambient-observations').textContent = dadosAmbiente.observacoes || "Nenhuma observação.";

        const idsDeFotosParaAssinar = new Set();
        dadosFotosDoAmbienteAtual.forEach(foto => idsDeFotosParaAssinar.add(foto.id));
        fotosDosFilhos.flat().forEach(foto => idsDeFotosParaAssinar.add(foto.id));

        let urlsAssinadasPorId = {};
        if (idsDeFotosParaAssinar.size > 0) {
            const urlPromises = Array.from(idsDeFotosParaAssinar).map(id => fetchJson(`/api/fotos/${id}/signed-url`));
            const signedUrlResponses = await Promise.all(urlPromises);

            signedUrlResponses.forEach((data, index) => {
                if (data && data.signedUrl) {
                    const fotoId = Array.from(idsDeFotosParaAssinar)[index];
                    urlsAssinadasPorId[fotoId] = data.signedUrl;
                }
            });
        }

        const fotoBannerAmbiente = dadosFotosDoAmbienteAtual.find(f => !f.id_ocorrencia);
        if (fotoBannerAmbiente && urlsAssinadasPorId[fotoBannerAmbiente.id]) {
            document.getElementById('ambient-image').src = urlsAssinadasPorId[fotoBannerAmbiente.id];
            document.getElementById('ambient-image').classList.remove('hidden');
            document.querySelector('.image-placeholder').classList.add('hidden');
        } else {
            document.getElementById('ambient-image').src = '/assets/templates/ambient-banner.png';
            document.getElementById('ambient-image').style.aspectRatio = '16 / 9';
            document.getElementById('ambient-image').classList.add('hidden');
            document.querySelector('.image-placeholder').style.aspectRatio = '16 / 9';
            document.querySelector('.image-placeholder').classList.remove('hidden');
        }

        renderizarAmbientesFilhos(document.getElementById('child-ambients-container'), dadosAmbientesFilhos, fotosDosFilhos, urlsAssinadasPorId);
        renderizarOcorrencias(document.getElementById('occurrences-container'), dadosOcorrencias, dadosFotosDoAmbienteAtual, urlsAssinadasPorId);

        document.getElementById('main-content').classList.remove('hidden');
    } catch (erro) {
        exibirErro(erro.message);
    }
};

document.addEventListener('DOMContentLoaded', carregarDadosDaPagina);