// Estado do dashboard
const dashboardState = {
    stats: {
        users: 0,
        inspections: 0,
        environments: 0,
        occurrences: 0
    },
    recentActivity: [],
    chartData: {
        inspections: [],
        users: [],
        environments: [],
        occurrences: []
    },
    chart: null,
    currentTheme: 'light'
};

// Inicialização do dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    initializeChart();
});

// ===== INTEGRAÇÃO COM TEMA GLOBAL =====

// Obter tema atual do sistema global
function getCurrentTheme() {
    return document.body.getAttribute('data-theme') || 'light';
}

// Atualizar tema do gráfico
function updateChartTheme(theme) {
    const chart = dashboardState.chart;
    if (!chart) return;

    const isDark = theme === 'dark';
    const textColor = isDark ? '#ffffff' : '#374151';
    const gridColor = isDark ? '#6b7280' : '#e5e7eb';
    
    // Atualizar cores do gráfico
    chart.options.plugins.legend.labels.color = textColor;
    chart.options.plugins.title.color = textColor;
    
    if (chart.options.scales) {
        if (chart.options.scales.x) {
            chart.options.scales.x.ticks.color = textColor;
            chart.options.scales.x.grid.color = gridColor;
        }
        if (chart.options.scales.y) {
            chart.options.scales.y.ticks.color = textColor;
            chart.options.scales.y.grid.color = gridColor;
        }
    }
    
    chart.update();
}

// Funções de navegação
function navigateToCreateUser() {
    window.location.href = '/adm/add-user';
}

function navigateToCreateInspection() {
    window.location.href = '/adm/add-inspection';
}

// Carregamento de dados do dashboard
async function loadDashboardData() {
    try {
        // Carregar estatísticas
        await loadStatistics();
        
        // Carregar atividade recente
        await loadRecentActivity();
        
    } catch (error) {
        console.error('Erro ao carregar dados do dashboard:', error);
        showErrorMessage();
    }
}

// Carregar estatísticas
async function loadStatistics() {
    try {
        // Carregar usuários
        const users = await apiRequest('/api/usuarios');
        updateStatCard('total-users', users.length);
        
        // Carregar inspeções
        const inspections = await apiRequest('/api/inspecoes');
        updateStatCard('total-inspections', inspections.length);
        
        // Carregar ambientes (através de todas as inspeções)
        let totalEnvironments = 0;
        for (const inspection of inspections) {
            try {
                const environments = await apiRequest(`/api/inspecoes/${inspection.id}/ambientes`);
                totalEnvironments += environments.length;
            } catch (error) {
                console.warn(`Erro ao carregar ambientes da inspeção ${inspection.id}:`, error);
            }
        }
        updateStatCard('total-environments', totalEnvironments);
        
        // Carregar ocorrências (estimativa baseada em ambientes)
        let totalOccurrences = 0;
        for (const inspection of inspections) {
            try {
                const environments = await apiRequest(`/api/inspecoes/${inspection.id}/ambientes`);
                for (const environment of environments) {
                    try {
                        const occurrences = await apiRequest(`/api/ocorrencias/ambiente/${environment.id}`);
                        totalOccurrences += occurrences.length;
                    } catch (error) {
                        // Ignorar erros de ocorrências individuais
                    }
                }
            } catch (error) {
                console.warn(`Erro ao carregar ocorrências da inspeção ${inspection.id}:`, error);
            }
        }
        updateStatCard('total-occurrences', totalOccurrences);
        
    } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
        // Mostrar valores padrão em caso de erro
        updateStatCard('total-users', '-');
        updateStatCard('total-inspections', '-');
        updateStatCard('total-environments', '-');
        updateStatCard('total-occurrences', '-');
    }
}

// Carregar atividade recente
async function loadRecentActivity() {
    try {
        const activities = [];
        
        // Buscar inspeções recentes
        const inspections = await apiRequest('/api/inspecoes');
        const recentInspections = inspections
            .sort((a, b) => new Date(b.criado_em) - new Date(a.criado_em))
            .slice(0, 3);
        
        recentInspections.forEach(inspection => {
            activities.push({
                icon: '📋',
                text: `Nova inspeção criada: ${inspection.nome}`,
                time: formatTimeAgo(inspection.criado_em)
            });
        });
        
        // Buscar usuários recentes
        const users = await apiRequest('/api/usuarios');
        const recentUsers = users
            .sort((a, b) => new Date(b.criado_em) - new Date(a.criado_em))
            .slice(0, 2);
        
        recentUsers.forEach(user => {
            activities.push({
                icon: '👤',
                text: `Novo usuário cadastrado: ${user.nome}`,
                time: formatTimeAgo(user.criado_em)
            });
        });
        
        // Ordenar por data e pegar os 5 mais recentes
        activities.sort((a, b) => new Date(b.time) - new Date(a.time));
        const recentActivities = activities.slice(0, 5);
        
        renderRecentActivity(recentActivities);
        
    } catch (error) {
        console.error('Erro ao carregar atividade recente:', error);
        renderRecentActivity([]);
    }
}

// Atualizar card de estatística
function updateStatCard(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        // Animação de contagem
        animateNumber(element, value);
    }
}

// Animação de contagem de números
function animateNumber(element, targetValue) {
    if (typeof targetValue !== 'number') {
        element.textContent = targetValue;
        return;
    }
    
    const startValue = 0;
    const duration = 1000;
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
        element.textContent = currentValue.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// Renderizar atividade recente
function renderRecentActivity(activities) {
    const container = document.getElementById('recent-activity-list');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon">📊</div>
                <div class="activity-content">
                    <p class="activity-text">Nenhuma atividade recente encontrada</p>
                    <span class="activity-time">-</span>
                </div>
            </div>
        `;
        return;
    }
    
    const activitiesHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">${activity.icon}</div>
            <div class="activity-content">
                <p class="activity-text">${activity.text}</p>
                <span class="activity-time">${activity.time}</span>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = activitiesHTML;
}

// Formatar tempo relativo
function formatTimeAgo(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'Agora mesmo';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minuto${minutes > 1 ? 's' : ''} atrás`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hora${hours > 1 ? 's' : ''} atrás`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} dia${days > 1 ? 's' : ''} atrás`;
    }
}

// Função de requisição API
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Erro na requisição API:', error);
        throw error;
    }
}

// Mostrar mensagem de erro
function showErrorMessage() {
    const container = document.querySelector('.dashboard-container');
    const errorMessage = document.createElement('div');
    errorMessage.className = 'error-message';
    errorMessage.innerHTML = `
        <div style="background: #fee2e2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin: 16px 0; color: #dc2626;">
            <strong>Erro ao carregar dados:</strong> Alguns dados podem não estar atualizados.
        </div>
    `;
    container.insertBefore(errorMessage, container.firstChild);
}

// ===== FUNCIONALIDADES DOS GRÁFICOS INTERATIVOS =====

// Inicializar gráfico
function initializeChart() {
    const ctx = document.getElementById('interactive-chart').getContext('2d');

    const isDark = getCurrentTheme() === 'dark';
    const textColor = isDark ? '#ffffff' : '#374151';
    const gridColor = isDark ? '#6b7280' : '#e5e7eb';

    dashboardState.chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Carregando...'],
            datasets: [{
                label: 'Dados',
                data: [0],
                backgroundColor: [
                    'rgba(0, 85, 140, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgba(0, 85, 140, 1)',
                    'rgba(59, 130, 246, 1)',
                    'rgba(16, 185, 129, 1)',
                    'rgba(245, 158, 11, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        color: textColor
                    }
                },
                title: {
                    display: true,
                    text: 'Status das Inspeções',
                    color: textColor
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: textColor
                    },
                    grid: {
                        color: gridColor
                    }
                },
                x: {
                    ticks: {
                        color: textColor
                    },
                    grid: {
                        color: gridColor
                    }
                }
            }
        }
    });

    // Carregar dados iniciais do gráfico
    setTimeout(() => {
        updateChartData();
    }, 1000);
}

// Atualizar tipo do gráfico
function updateChartType() {
    const chartType = document.getElementById('chart-type').value;

    if (dashboardState.chart) {
        dashboardState.chart.destroy();
    }

    const ctx = document.getElementById('interactive-chart').getContext('2d');
    const isDark = getCurrentTheme() === 'dark';

    dashboardState.chart = new Chart(ctx, {
        type: chartType,
        data: dashboardState.chart?.data || {
            labels: ['Carregando...'],
            datasets: [{
                label: 'Dados',
                data: [0],
                backgroundColor: [
                    'rgba(0, 85, 140, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgba(0, 85, 140, 1)',
                    'rgba(59, 130, 246, 1)',
                    'rgba(16, 185, 129, 1)',
                    'rgba(245, 158, 11, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: getChartOptions(chartType, isDark)
    });

    updateChartData();
}

// Atualizar dados do gráfico
async function updateChartData() {
    const dataType = document.getElementById('chart-data').value;
    const period = document.getElementById('chart-period').value;

    try {
        let chartData;
        let title;

        switch (dataType) {
            case 'status':
                chartData = await getInspectionStatusData(period);
                title = 'Status das Inspeções';
                break;
            case 'monthly':
                chartData = await getMonthlyInspectionsData(period);
                title = 'Inspeções por Mês';
                break;
            case 'users':
                chartData = await getUsersData(period);
                title = 'Usuários por Tipo';
                break;
            case 'environments':
                chartData = await getEnvironmentsData(period);
                title = 'Ambientes por Inspeção';
                break;
            default:
                chartData = { labels: ['Sem dados'], data: [0] };
                title = 'Dados não disponíveis';
        }

        if (dashboardState.chart) {
            dashboardState.chart.data.labels = chartData.labels;
            dashboardState.chart.data.datasets[0].data = chartData.data;
            dashboardState.chart.data.datasets[0].label = title;
            dashboardState.chart.options.plugins.title.text = title;
            dashboardState.chart.update();
        }

    } catch (error) {
        console.error('Erro ao atualizar dados do gráfico:', error);
    }
}

// Atualizar período do gráfico
function updateChartPeriod() {
    updateChartData();
}

// Obter opções do gráfico baseado no tipo
function getChartOptions(chartType, isDark = false) {
    const textColor = isDark ? '#ffffff' : '#374151';
    const gridColor = isDark ? '#6b7280' : '#e5e7eb';

    const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    color: textColor
                }
            },
            title: {
                display: true,
                text: 'Dados do Sistema',
                color: textColor
            }
        }
    };

    if (chartType === 'pie' || chartType === 'doughnut') {
        return baseOptions;
    }

    return {
        ...baseOptions,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    color: textColor
                },
                grid: {
                    color: gridColor
                }
            },
            x: {
                ticks: {
                    color: textColor
                },
                grid: {
                    color: gridColor
                }
            }
        }
    };
}

// Obter dados de status das inspeções
async function getInspectionStatusData(period) {
    try {
        const inspections = await apiRequest('/api/inspecoes');
        const filteredInspections = filterByPeriod(inspections, period);

        const statusCount = {};
        filteredInspections.forEach(inspection => {
            const status = inspection.status || 'Sem status';
            statusCount[status] = (statusCount[status] || 0) + 1;
        });

        return {
            labels: Object.keys(statusCount),
            data: Object.values(statusCount)
        };
    } catch (error) {
        console.error('Erro ao obter dados de status:', error);
        return { labels: ['Erro'], data: [0] };
    }
}

// Obter dados mensais de inspeções
async function getMonthlyInspectionsData(period) {
    try {
        const inspections = await apiRequest('/api/inspecoes');
        const filteredInspections = filterByPeriod(inspections, period);

        const monthlyCount = {};
        filteredInspections.forEach(inspection => {
            const date = new Date(inspection.criado_em);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            monthlyCount[monthKey] = (monthlyCount[monthKey] || 0) + 1;
        });

        const sortedMonths = Object.keys(monthlyCount).sort();

        return {
            labels: sortedMonths.map(month => {
                const [year, monthNum] = month.split('-');
                const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
                return `${monthNames[parseInt(monthNum) - 1]} ${year}`;
            }),
            data: sortedMonths.map(month => monthlyCount[month])
        };
    } catch (error) {
        console.error('Erro ao obter dados mensais:', error);
        return { labels: ['Erro'], data: [0] };
    }
}

// Obter dados de usuários
async function getUsersData(period) {
    try {
        const users = await apiRequest('/api/usuarios');
        const filteredUsers = filterByPeriod(users, period);

        const userTypeCount = {};
        filteredUsers.forEach(user => {
            const type = user.permissao || 'Sem permissão';
            userTypeCount[type] = (userTypeCount[type] || 0) + 1;
        });

        return {
            labels: Object.keys(userTypeCount),
            data: Object.values(userTypeCount)
        };
    } catch (error) {
        console.error('Erro ao obter dados de usuários:', error);
        return { labels: ['Erro'], data: [0] };
    }
}

// Obter dados de ambientes
async function getEnvironmentsData(period) {
    try {
        const inspections = await apiRequest('/api/inspecoes');
        const filteredInspections = filterByPeriod(inspections, period);

        const environmentCount = {};

        for (const inspection of filteredInspections.slice(0, 10)) { // Limitar a 10 inspeções para performance
            try {
                const environments = await apiRequest(`/api/inspecoes/${inspection.id}/ambientes`);
                const inspectionName = inspection.nome || `Inspeção ${inspection.id}`;
                environmentCount[inspectionName] = environments.length;
            } catch (error) {
                console.warn(`Erro ao carregar ambientes da inspeção ${inspection.id}:`, error);
            }
        }

        return {
            labels: Object.keys(environmentCount),
            data: Object.values(environmentCount)
        };
    } catch (error) {
        console.error('Erro ao obter dados de ambientes:', error);
        return { labels: ['Erro'], data: [0] };
    }
}

// Filtrar dados por período
function filterByPeriod(data, period) {
    if (period === 'all') {
        return data;
    }

    const now = new Date();
    let startDate;

    switch (period) {
        case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
        case 'last7':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case 'last30':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
        default:
            return data;
    }

    return data.filter(item => {
        const itemDate = new Date(item.criado_em);
        return itemDate >= startDate;
    });
}
