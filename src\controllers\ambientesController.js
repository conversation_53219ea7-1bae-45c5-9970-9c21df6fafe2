const ambientesService = require('../services/ambientesService');

exports.listarAmbientesPorInspecao = async (req, res, next) => {
    try {
        const { id_inspecao } = req.params;
        const ambientes = await ambientesService.listarAmbientesPorInspecao(id_inspecao);
        res.status(200).json(ambientes);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarAmbientesPorInspecao:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.criarAmbienteNaInspecao = async (req, res, next) => {
    try {
        const { id_inspecao } = req.params;
        const novoAmbiente = await ambientesService.criarAmbienteNaInspecao(id_inspecao, req.body);
        res.status(201).json(novoAmbiente);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarAmbienteNaInspecao:', error);
        const status = error.message.includes('não encontrada') || error.message.includes('não encontrado') ? 404 :
                      error.message.includes('obrigatório') || error.message.includes('inválido') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterAmbientePorId = async (req, res, next) => {
    try {
        const { id_ambiente } = req.params;
        const ambiente = await ambientesService.obterAmbientePorId(id_ambiente);
        res.status(200).json(ambiente);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterAmbientePorId:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.atualizarAmbiente = async (req, res, next) => {
    try {
        const { id_ambiente } = req.params;
        const ambienteAtualizado = await ambientesService.atualizarAmbiente(id_ambiente, req.body);
        res.status(200).json(ambienteAtualizado);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] atualizarAmbiente:', error);
        const status = error.message.includes('não encontrado') ? 404 :
                      error.message.includes('obrigatório') || error.message.includes('inválido') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.deletarAmbiente = async (req, res, next) => {
    try {
        const { id_ambiente } = req.params;
        await ambientesService.deletarAmbiente(id_ambiente);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarAmbiente:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};