const db = require('../config/db');
const bcrypt = require('bcryptjs');
jest.mock('../config/db'); 
jest.mock('bcryptjs');

const Usuario = require('../models/usuariosModel');

describe('Model: Usuario', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar um usuário com sucesso', async () => {
    bcrypt.genSalt.mockResolvedValueOnce('salt');
    bcrypt.hash.mockResolvedValueOnce('hashed');
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Teste', email: '<EMAIL>' }] });
    const result = await Usuario.create({ nome: 'Teste', email: '<EMAIL>', senha: '123456' });
    expect(result).toHaveProperty('id');
    expect(result.nome).toBe('Teste');
  });

  it('deve retornar erro ao criar usuário', async () => {
    bcrypt.genSalt.mockResolvedValueOnce('salt');
    bcrypt.hash.mockResolvedValueOnce('hashed');
    db.query.mockRejectedValueOnce(new Error('DB error'));
    await expect(Usuario.create({ nome: 'Teste', email: '<EMAIL>', senha: '123456' }))
      .rejects.toThrow('DB error');
  });

  it('deve buscar usuário por email', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Teste', email: '<EMAIL>', senha: 'hash' }] });
    const result = await Usuario.findByEmail('<EMAIL>');
    expect(result.email).toBe('<EMAIL>');
  });

  it('deve retornar undefined se usuário não encontrado por email', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Usuario.findByEmail('<EMAIL>');
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao buscar usuário por email', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar usuário'));
    await expect(Usuario.findByEmail('<EMAIL>')).rejects.toThrow('Erro ao buscar usuário');
  });

  it('deve buscar usuário por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Teste', email: '<EMAIL>' }] });
    const result = await Usuario.findById(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar undefined se usuário por id não encontrado', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Usuario.findById(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao buscar usuário por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar usuário por ID no modelo:'));
    await expect(Usuario.findById(1)).rejects.toThrow('Erro ao buscar usuário por ID no modelo:');
  });

  it('deve buscar todos os usuários', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Teste' }] });
    const result = await Usuario.findAll();
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].nome).toBe('Teste');
  });

  it('deve tratar erro ao buscar todos os usuários', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar todos os usuários no modelo:'));
    await expect(Usuario.findAll()).rejects.toThrow('Erro ao buscar todos os usuários no modelo:');
  });

  it('deve atualizar um usuário', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Novo Nome', email: '<EMAIL>' }] });
    const result = await Usuario.update(1, { nome: 'Novo Nome', email: '<EMAIL>' });
    expect(result.nome).toBe('Novo Nome');
  });

  it('deve lançar erro se tentar atualizar sem dados válidos', async () => {
    await expect(Usuario.update(1, {})).rejects.toThrow('Nenhum dado válido para atualizar.');
  });

  it('deve tratar erro ao atualizar usuário', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar usuário no modelo:'));
    await expect(Usuario.update(1, { nome: 'Novo Nome' })).rejects.toThrow('Erro ao atualizar usuário no modelo:');
  });

  it('deve remover um usuário', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Usuario.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar undefined ao remover usuário inexistente', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Usuario.remove(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao remover usuário', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao deletar usuário no modelo:'));
    await expect(Usuario.remove(1)).rejects.toThrow('Erro ao deletar usuário no modelo:');
  });
});