const db = require('../config/db');

const TipoEdificacao = {
  async findAll() {
    const queryText = 'SELECT nome FROM tipo_edificacao ORDER BY nome;';
    try {
      const { rows } = await db.query(queryText);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar tipos de edificação', error);
      throw error;
    }
  },

  async findById(nome) {
    const queryText = 'SELECT * FROM tipo_edificacao WHERE nome = $1;';
    try {
      const { rows } = await db.query(queryText, [nome]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao buscar tipo de edificação por nome:', error);
      throw error;
    }
  }
};
module.exports = TipoEdificacao;