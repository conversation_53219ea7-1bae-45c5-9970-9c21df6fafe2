async function processPhotosUrls(fotos) {
    if (!fotos || !Array.isArray(fotos)) {
        return fotos || [];
    }

    try {
        
        const processedPhotos = fotos.map(foto => {
            if (!foto || !foto.url) {
                return foto;
            }

            if (foto.url.includes('/object/public/')) {
                console.log('📸 URL pública detectada, usando como está');
                return {
                    ...foto,
                    url: foto.url,
                    originalUrl: foto.url
                };
            }

            return {
                ...foto,
                url: foto.url,
                originalUrl: foto.url
            };
        });
        
        return processedPhotos;
    } catch (error) {
        console.error('Erro ao processar URLs das fotos:', error);
        return fotos; 
    }
}

module.exports = {
    processPhotosUrls
};