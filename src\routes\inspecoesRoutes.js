const express = require("express");
const router = express.Router();
const inspecaoController = require("../controllers/inspecoesController");
const ambienteController = require("../controllers/ambientesController");
const relatorioController = require("../controllers/relatoriosController");
const historicoController = require("../controllers/historicoController");

router.get("/", inspecaoController.listarInspecoes);
router.get(
  "/por-equipe/:id_equipe",
  inspecaoController.listarInspecoesPorEquipe
);
router.post("/", inspecaoController.criarInspecao);
router.get("/:id_inspecao", inspecaoController.obterInspecaoPorId);
router.put("/:id_inspecao", inspecaoController.atualizarInspecao);
router.delete("/:id_inspecao", inspecaoController.deletarInspecao);
router.patch(
  "/:id_inspecao/status",
  inspecaoController.atualizarInspecaoStatus
);

router.get(
  "/:id_inspecao/historico",
  historicoController.listarHistoricoPorInspecao
);

router.get(
  "/:id_inspecao/ambientes",
  ambienteController.listarAmbientesPorInspecao
);
router.post(
  "/:id_inspecao/ambientes",
  ambienteController.criarAmbienteNaInspecao
);
router.get(
  "/:id_inspecao/relatorios",
  relatorioController.listarRelatoriosPorInspecao
);
router.post(
  "/:id_inspecao/relatorios",
  relatorioController.adicionarRelatorioNaInspecao
);

module.exports = router;