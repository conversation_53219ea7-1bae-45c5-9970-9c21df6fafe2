const db = require('../config/db');
jest.mock('../config/db'); 

const TipoPatologia = require('../models/tipoPatologiaModel');

describe('Model: TipoPatologia', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve buscar todos os tipos de patologia', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ nome: 'Fissura', id_tipo_sistema: 1 }] });
    const result = await TipoPatologia.findAll();
    expect(result[0].nome).toBe('Fissura');
  });

  it('deve tratar erro ao buscar tipos de patologia', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro DB'));
    await expect(TipoPatologia.findAll()).rejects.toThrow('Erro DB');
  });
});