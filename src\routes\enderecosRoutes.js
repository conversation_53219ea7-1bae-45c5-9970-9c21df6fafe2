const express = require("express");
const router = express.Router();
const enderecoController = require("../controllers/enderecosController");

router.get("/", enderecoController.listarEnderecos);
router.post("/", enderecoController.criarEndereco);
router.get("/:id_endereco", enderecoController.obterEnderecoPorId);
router.put("/:id_endereco", enderecoController.atualizarEndereco);
router.delete("/:id_endereco", enderecoController.deletarEndereco);

module.exports = router;