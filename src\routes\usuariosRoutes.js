const express = require('express');
const router = express.Router();
const usuarioController = require('../controllers/usuariosController');
const auth = require("../middlewares/authMiddleware");

router.post('/', usuarioController.criarUsuario);

router.get("/", usuarioController.listarUsuarios);
router.get('/:id_usuario', usuarioController.obterUsuarioPorId);
router.put('/:id_usuario', usuarioController.atualizarUsuario);
router.delete('/:id_usuario', usuarioController.deletarUsuario);
router.patch("/:id_usuario", usuarioController.atualizarUsuario);

module.exports = router;