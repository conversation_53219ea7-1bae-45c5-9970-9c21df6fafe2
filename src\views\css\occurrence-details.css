.occurrence-details-page {
    background-color: #f4f7f9;
    min-height: 100vh;
}

.image-banner {
    width: 100%;
    height: auto;
    max-height: 600px;
    background-color: #f0f2f5;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.photo-gallery {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-gallery img {
    width: 100%;
    height: 100%;
    max-height: 600px;
    margin: auto;
    object-fit: contain;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    max-height: 600px;
    background: linear-gradient(110deg, #e1e1e1 8%, #f0f0f0 18%, #e1e1e1 33%);
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite;
}

@keyframes shine {
    to {
        background-position-x: -200%;
    }
}

.info-container {
    max-width: 900px;
    margin: -1rem auto 0 auto;
    padding: 0 1.5rem 3rem 1.5rem;
    position: relative;
    z-index: 10;
}

.content-card {
    background-color: var(--background-color);
    padding: clamp(1.5rem, 4vw, 2.5rem);
    border-radius: 16px;
    border: 1px solid var(--muted-color);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.08);
}

.title-date-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: baseline;
    gap: 0.5rem 1rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--muted-color);
}

.card-title {
    margin: 0;
}

.card-date {
    white-space: nowrap;
    flex-shrink: 0;
}

.tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
    padding: 1.5rem 0;
}

.info-section {
    margin-top: 1.5rem;
}

.info-section:first-of-type {
    margin-top: 0;
}

.info-section label {
    display: block;
    margin-bottom: .75rem;
}

.info-section p {
    margin: 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid var(--muted-color);
    line-height: 1.6;
    min-height: 48px;
}

.hidden {
    display: none !important;
}

.error-message {
    color: var(--destructive-color);
    background-color: #fff5f5;
    border: 1px solid var(--destructive-color);
    border-radius: 8px;
    padding: 1rem;
    margin: 1.5rem;
    text-align: center;
}