.occurrence-details-page {
    background-color: #f4f7f9;
    min-height: 100vh;
    transition: background-color 0.3s ease;
}

/* Modo escuro - página principal */
[data-theme="dark"] .occurrence-details-page {
    background: linear-gradient(135deg,
        var(--background-color) 0%,
        rgba(59, 130, 246, 0.02) 50%,
        var(--background-color) 100%);
}

.image-banner {
    width: 100%;
    height: auto;
    max-height: 600px;
    background-color: #f0f2f5;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s ease;
}

/* Modo escuro - banner de imagem */
[data-theme="dark"] .image-banner {
    background-color: #1f2937;
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.photo-gallery {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-gallery img {
    width: 100%;
    height: 100%;
    max-height: 600px;
    margin: auto;
    object-fit: contain;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    max-height: 600px;
    background: linear-gradient(110deg, #e1e1e1 8%, #f0f0f0 18%, #e1e1e1 33%);
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
}

.image-placeholder-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
    color: var(--muted-foreground-color);
    text-align: center;
    transition: color 0.3s ease;
}

.image-placeholder-icon svg {
    width: 80px;
    height: 80px;
    stroke: var(--muted-foreground-color);
    fill: none;
    opacity: 0.6;
    transition: stroke 0.3s ease, opacity 0.3s ease;
}

.image-placeholder-icon p {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.8;
    color: var(--muted-foreground-color);
    transition: color 0.3s ease;
}

/* Modo escuro - placeholder de imagem */
[data-theme="dark"] .image-placeholder {
    background: linear-gradient(110deg, #374151 8%, #4b5563 18%, #374151 33%);
    background-size: 200% 100%;
    border: 1px solid #6b7280;
}

[data-theme="dark"] .image-placeholder-icon {
    color: #f3f4f6 !important;
}

[data-theme="dark"] .image-placeholder-icon svg {
    stroke: #f3f4f6 !important;
    opacity: 0.9 !important;
}

[data-theme="dark"] .image-placeholder-icon p {
    color: #f3f4f6 !important;
    opacity: 0.9 !important;
}

@keyframes shine {
    to {
        background-position-x: -200%;
    }
}

.info-container {
    max-width: 900px;
    margin: -1rem auto 0 auto;
    padding: 0 1.5rem 3rem 1.5rem;
    position: relative;
    z-index: 10;
}

.content-card {
    background-color: var(--background-color);
    padding: clamp(1.5rem, 4vw, 2.5rem);
    border-radius: 16px;
    border: 1px solid var(--muted-color);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

/* Modo escuro - card de conteúdo */
[data-theme="dark"] .content-card {
    background: rgba(31, 41, 55, 0.95);
    backdrop-filter: blur(15px);
    border-color: rgba(75, 85, 99, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.title-date-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: baseline;
    gap: 0.5rem 1rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--muted-color);
    transition: border-color 0.3s ease;
}

/* Modo escuro - seção título e data */
[data-theme="dark"] .title-date-section {
    border-bottom-color: rgba(75, 85, 99, 0.4);
}

.card-title {
    margin: 0;
    color: var(--foreground-color);
    transition: color 0.3s ease;
}

.card-date {
    white-space: nowrap;
    flex-shrink: 0;
    color: var(--muted-foreground-color);
    transition: color 0.3s ease;
}

/* Modo escuro - título e data */
[data-theme="dark"] .card-title {
    color: #ffffff;
    font-weight: 600;
}

[data-theme="dark"] .card-date {
    color: #d1d5db;
}

.tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
    padding: 1.5rem 0;
}

/* Modo escuro - seção de tags */
[data-theme="dark"] .tags-section .tag {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .tags-section .tag:hover {
    background: rgba(59, 130, 246, 0.3);
    color: #ffffff;
}

.info-section {
    margin-top: 1.5rem;
}

.info-section:first-of-type {
    margin-top: 0;
}

.info-section label {
    display: block;
    margin-bottom: .75rem;
    color: var(--foreground-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

.info-section p {
    margin: 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid var(--muted-color);
    line-height: 1.6;
    min-height: 48px;
    color: var(--foreground-color);
    transition: all 0.3s ease;
}

/* Modo escuro - seções de informação */
[data-theme="dark"] .info-section label {
    color: #f3f4f6;
    font-weight: 600;
}

[data-theme="dark"] .info-section p {
    background-color: rgba(55, 65, 81, 0.6);
    border-color: rgba(75, 85, 99, 0.4);
    color: #e5e7eb;
    backdrop-filter: blur(10px);
}

[data-theme="dark"] .info-section p:hover {
    background-color: rgba(55, 65, 81, 0.8);
    border-color: rgba(75, 85, 99, 0.6);
}

.hidden {
    display: none !important;
}

.error-message {
    color: var(--destructive-color);
    background-color: #fff5f5;
    border: 1px solid var(--destructive-color);
    border-radius: 8px;
    padding: 1rem;
    margin: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

/* Modo escuro - mensagem de erro */
[data-theme="dark"] .error-message {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #fca5a5;
    backdrop-filter: blur(10px);
}

/* Melhorias adicionais para modo escuro */
[data-theme="dark"] .photo-gallery img {
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .photo-gallery {
    background-color: #1f2937;
    color: #f3f4f6;
}

[data-theme="dark"] .photo-gallery .image-placeholder-icon {
    color: #f3f4f6 !important;
}

[data-theme="dark"] .photo-gallery .image-placeholder-icon svg {
    stroke: #f3f4f6 !important;
    opacity: 0.9 !important;
}

[data-theme="dark"] .photo-gallery .image-placeholder-icon p {
    color: #f3f4f6 !important;
    opacity: 0.9 !important;
}

/* Regra mais específica para garantir visibilidade no modo escuro */
[data-theme="dark"] .image-banner .photo-gallery .image-placeholder-icon,
[data-theme="dark"] .image-banner .photo-gallery .image-placeholder-icon * {
    color: #f9fafb !important;
}

[data-theme="dark"] .image-banner .photo-gallery .image-placeholder-icon svg {
    stroke: #f9fafb !important;
    opacity: 1 !important;
}

/* Scrollbar personalizada para modo escuro */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.8);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 0.9);
}

/* Animações suaves para transições */
[data-theme="dark"] * {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Melhor contraste para texto em modo escuro */
[data-theme="dark"] .occurrence-details-page {
    color: #f9fafb;
}

[data-theme="dark"] .occurrence-details-page h1,
[data-theme="dark"] .occurrence-details-page h2,
[data-theme="dark"] .occurrence-details-page h3,
[data-theme="dark"] .occurrence-details-page h4,
[data-theme="dark"] .occurrence-details-page h5,
[data-theme="dark"] .occurrence-details-page h6 {
    color: #ffffff;
    font-weight: 600;
}

/* Responsividade melhorada para modo escuro */
@media (max-width: 768px) {
    [data-theme="dark"] .content-card {
        background: rgba(31, 41, 55, 0.98);
        margin: 0 0.5rem;
    }

    [data-theme="dark"] .info-container {
        padding: 0 1rem 2rem 1rem;
    }
}