const axios = require('axios');

class IAService {
    constructor() {
        
        this.providers = {
            openai: {
                url: 'https://api.openai.com/v1/chat/completions',
                headers: {
                    'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            },
            anthropic: {
                url: 'https://api.anthropic.com/v1/messages',
                headers: {
                    'x-api-key': process.env.ANTHROPIC_API_KEY,
                    'Content-Type': 'application/json',
                    'anthropic-version': '2023-06-01'
                }
            },
            gemini: {
                url: `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${process.env.GEMINI_API_KEY}`,
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        };

        this.currentProvider = process.env.IA_PROVIDER || 'openai';
    }

    async gerarConteudoRelatorio(dadosInspecao) {
        try {
            const prompt = this.construirPromptRelatorio(dadosInspecao);
            const resposta = await this.chamarIA(prompt);
            return this.processarRespostaIA(resposta);
        } catch (error) {
            console.error('[IA_SERVICE_ERROR] gerarConteudoRelatorio:', error);
            throw new Error('Erro ao gerar conteúdo do relatório com IA.');
        }
    }

    construirPromptRelatorio(dadosInspecao) {
        const { inspecao, ambientes, ocorrencias, equipe, endereco } = dadosInspecao;

        return `
Você é um especialista em inspeções prediais e deve gerar um relatório técnico profissional baseado nos dados fornecidos.

DADOS DA INSPEÇÃO:
- Nome: ${inspecao.nome}
- Tipo de Edificação: ${inspecao.tipo_edificacao || 'Não especificado'}
- Data de Criação: ${new Date(inspecao.criado_em).toLocaleDateString('pt-BR')}
- Status: ${inspecao.status || 'Em andamento'}

ENDEREÇO:
- CEP: ${endereco?.cep || 'Não informado'}
- Número: ${endereco?.numero || 'Não informado'}
- Complemento: ${endereco?.complemento || 'Não informado'}

EQUIPE RESPONSÁVEL:
- Nome da Equipe: ${equipe?.nome || 'Não informado'}
- Membros: ${equipe?.membros?.length || 0} pessoas

AMBIENTES INSPECIONADOS:
${ambientes?.map(amb => `- ${amb.titulo}`).join('\n') || 'Nenhum ambiente cadastrado'}

OCORRÊNCIAS ENCONTRADAS:
${ocorrencias?.map(ocr => `- ${ocr.titulo}: ${ocr.descricao || 'Sem descrição'}`).join('\n') || 'Nenhuma ocorrência registrada'}

INSTRUÇÕES:
Gere um relatório técnico de inspeção predial em formato JSON com a seguinte estrutura:

{
  "titulo": "Título do relatório",
  "resumoExecutivo": "Resumo executivo de 2-3 parágrafos",
  "introducao": "Introdução explicando o objetivo da inspeção",
  "metodologia": "Metodologia utilizada na inspeção",
  "resultados": {
    "ambientesInspecionados": "Descrição dos ambientes",
    "ocorrenciasEncontradas": "Análise das ocorrências",
    "classificacaoRisco": "Classificação do nível de risco"
  },
  "recomendacoes": [
    "Lista de recomendações específicas"
  ],
  "conclusao": "Conclusão técnica",
  "observacoes": "Observações adicionais se necessário"
}

O relatório deve ser técnico, profissional e seguir as normas brasileiras de inspeção predial (NBR 16747).
`;
    }

    async chamarIA(prompt) {
        const provider = this.providers[this.currentProvider];
        
        if (!provider) {
            throw new Error(`Provedor de IA '${this.currentProvider}' não configurado.`);
        }

        let requestBody;

        switch (this.currentProvider) {
            case 'openai':
                requestBody = {
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: 'system',
                            content: 'Você é um especialista em inspeções prediais e relatórios técnicos.'
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: 2000,
                    temperature: 0.7
                };
                break;

            case 'anthropic':
                requestBody = {
                    model: 'claude-3-sonnet-20240229',
                    max_tokens: 2000,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ]
                };
                break;

            case 'gemini':
                requestBody = {
                    contents: [
                        {
                            parts: [
                                {
                                    text: prompt
                                }
                            ]
                        }
                    ]
                };
                break;

            default:
                throw new Error(`Provedor '${this.currentProvider}' não suportado.`);
        }

        const response = await axios.post(provider.url, requestBody, {
            headers: provider.headers,
            timeout: 30000 
        });

        return this.extrairRespostaDaIA(response.data);
    }

    extrairRespostaDaIA(data) {
        switch (this.currentProvider) {
            case 'openai':
                return data.choices[0].message.content;
            
            case 'anthropic':
                return data.content[0].text;
            
            case 'gemini':
                return data.candidates[0].content.parts[0].text;
            
            default:
                throw new Error(`Extração não implementada para '${this.currentProvider}'.`);
        }
    }

    processarRespostaIA(resposta) {
        try {
            
            const jsonMatch = resposta.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }

            return this.criarRelatorioFallback(resposta);
        } catch (error) {
            console.error('[IA_SERVICE_ERROR] processarRespostaIA:', error);
            return this.criarRelatorioFallback(resposta);
        }
    }

    criarRelatorioFallback(textoIA) {
        return {
            titulo: 'Relatório de Inspeção Predial',
            resumoExecutivo: textoIA.substring(0, 500) + '...',
            introducao: 'Relatório gerado automaticamente com base nos dados da inspeção.',
            metodologia: 'Inspeção visual e técnica conforme NBR 16747.',
            resultados: {
                ambientesInspecionados: 'Conforme dados coletados durante a inspeção.',
                ocorrenciasEncontradas: 'Detalhadas nos registros da inspeção.',
                classificacaoRisco: 'A ser determinada com base nas ocorrências.'
            },
            recomendacoes: [
                'Acompanhar as ocorrências identificadas',
                'Realizar manutenções preventivas',
                'Monitorar condições estruturais'
            ],
            conclusao: 'Inspeção realizada conforme metodologia técnica.',
            observacoes: 'Relatório gerado automaticamente.'
        };
    }

    async gerarResumoOcorrencias(ocorrencias) {
        if (!ocorrencias || ocorrencias.length === 0) {
            return 'Nenhuma ocorrência registrada durante a inspeção.';
        }

        try {
            const prompt = `
Analise as seguintes ocorrências de uma inspeção predial e gere um resumo técnico:

${ocorrencias.map(ocr => `- ${ocr.titulo}: ${ocr.descricao || 'Sem descrição'}`).join('\n')}

Gere um resumo profissional de 2-3 parágrafos destacando:
1. Principais problemas encontrados
2. Nível de criticidade geral
3. Impacto na segurança e funcionalidade do edifício
`;

            return await this.chamarIA(prompt);
        } catch (error) {
            console.error('[IA_SERVICE_ERROR] gerarResumoOcorrencias:', error);
            return 'Resumo das ocorrências não disponível no momento.';
        }
    }

    isConfigurado() {
        const provider = this.providers[this.currentProvider];
        if (!provider) return false;

        switch (this.currentProvider) {
            case 'openai':
                return !!process.env.OPENAI_API_KEY;
            case 'anthropic':
                return !!process.env.ANTHROPIC_API_KEY;
            case 'gemini':
                return !!process.env.GEMINI_API_KEY;
            default:
                return false;
        }
    }
}

module.exports = new IAService();