const inspecoesService = require('../services/inspecoesService');

exports.listarInspecoes = async (req, res, next) => {
  try {
    const inspecoes = await inspecoesService.listarInspecoes();
    res.status(200).json(inspecoes);
  } catch (error) {
    console.error("[CONTROLLER_ERROR] listarInspecoes:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.listarInspecoesPorEquipe = async (req, res, next) => {
  try {
    const { id_equipe } = req.params;
    const inspecoesEquipe = await inspecoesService.listarInspecoesPorEquipe(id_equipe);
    res.status(200).json(inspecoesEquipe);
  } catch (error) {
    console.error("[CONTROLLER_ERROR] listarInspecoesPorEquipe:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.criarInspecao = async (req, res, next) => {
  try {
    const novaInspecao = await inspecoesService.criarInspecao(req.body);
    res.status(201).json(novaInspecao);
  } catch (error) {
    console.error("[CONTROLLER_ERROR] criarInspecao:", error);
    const status = error.message.includes('não encontrado') || error.message.includes('não encontrada') ? 404 :
                  error.message.includes('obrigatórios') || error.message.includes('inválido') ? 400 : 500;
    res.status(status).json({ message: error.message });
  }
};

exports.obterInspecaoPorId = async (req, res, next) => {
  try {
    const { id_inspecao } = req.params;
    const inspecao = await inspecoesService.obterInspecaoPorId(id_inspecao);
    res.status(200).json(inspecao);
  } catch (error) {
    console.error("[CONTROLLER_ERROR] obterInspecaoPorId:", error);
    const status = error.message.includes('não encontrada') ? 404 : 500;
    res.status(status).json({ message: error.message });
  }
};

exports.atualizarInspecao = async (req, res, next) => {
  try {
    const { id_inspecao } = req.params;
    const inspecaoAtualizada = await inspecoesService.atualizarInspecao(id_inspecao, req.body);
    res.status(200).json(inspecaoAtualizada);
  } catch (error) {
    console.error("[CONTROLLER_ERROR] atualizarInspecao:", error);
    const status = error.message.includes('não encontrada') || error.message.includes('não encontrado') ? 404 :
                  error.message.includes('obrigatório') || error.message.includes('inválido') ? 400 : 500;
    res.status(status).json({ message: error.message });
  }
};

exports.atualizarInspecaoStatus = async (req, res, next) => {
  try {
    const { id_inspecao } = req.params;
    const { status_nome } = req.body;
    const resultado = await inspecoesService.atualizarInspecaoStatus(id_inspecao, status_nome);
    res.status(201).json(resultado);
  } catch (error) {
    console.error("[CONTROLLER_ERROR] atualizarStatusInspecao:", error);
    const status = error.message.includes('não encontrada') || error.message.includes('não encontrado') ? 404 :
                  error.message.includes('obrigatório') ? 400 : 500;
    res.status(status).json({ message: error.message });
  }
};

exports.atualizarInspecaoStatus = async (req, res, next) => {
    try {
        const { id_inspecao } = req.params;
        const { status_nome } = req.body;

        if (!status_nome) {
            return res.status(400).json({ message: "O nome do status é obrigatório." });
        }

        const status = await StatusModel.findByName(status_nome);
        if (!status) {
            return res.status(404).json({ message: `Status '${status_nome}' não encontrado.` });
        }

        const inspecao = await InspecaoModel.findById(id_inspecao);
        if (!inspecao) {
            return res.status(404).json({ message: "Inspeção não encontrada." });
        }

        const novoHistorico = await HistoricoModel.create({
            id_inspecao,
            status_id: status.id
        });

        res.status(201).json({ message: "Status da inspeção atualizado com sucesso!", historico: novoHistorico });

    } catch (error) {
        console.error('[CONTROLLER_ERROR] atualizarStatusInspecao:', error);
        res.status(500).json({ message: "Erro ao atualizar o status da inspeção.", error: error.message });
    }
};

exports.deletarInspecao = async (req, res, next) => {
  try {
    const { id_inspecao } = req.params;
    await inspecoesService.deletarInspecao(id_inspecao);
    res.status(204).send();
  } catch (error) {
    console.error("[CONTROLLER_ERROR] deletarInspecao:", error);
    const status = error.message.includes('não encontrada') ? 404 : 500;
    res.status(status).json({ message: error.message });
  }
};