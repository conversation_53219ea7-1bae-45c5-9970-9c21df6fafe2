# Inteli - Instituto de Tecnologia e Liderança 

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="./assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# InsPecT 

## Nome do grupo: BugBusters

## Integrantes: 
- <a href="https://www.linkedin.com/in/ana-j%C3%BAlia-ribeiro/">Ana Júlia Rodrigues Ribeiro</a>
- <a href="https://www.linkedin.com/in/bernardolaurindo/"><PERSON></a>
- <a href="https://www.linkedin.com/in/clara-benito/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/jo%C3%A3ocardosodias/"><PERSON></a> 
- <a href="https://www.linkedin.com/in/marcus-valente/"><PERSON></a>
- <a href="https://www.linkedin.com/in/rebeca-namura-sbroglio-0a85ba358/">Rebeca Namura Sbroglio</a> 
- <a href="https://www.linkedin.com/in/vitor-ribeiro-2822a932a/">Vitor Ribeiro de Mattos Silva</a>

## :teacher: Professores:
### Orientador(a) 
- <a href="https://www.linkedin.com/in/profclaudioandre/">Cláudio Fernando André</a>
### Instrutores
- <a href="https://www.linkedin.com/in/anacristinadossantos/">Ana Cristina dos Santos</a>
- <a href="https://www.linkedin.com/in/bruna-mayer/">Bruna Mayer Costa</a>
- <a href="https://www.linkedin.com/in/diogo-martins-gon%C3%A7alves-de-morais-96404732/">Diogo Martins Gonçalves de Moraes</a> 
- <a href="https://www.linkedin.com/in/henrique-mohallem-paiva-6854b460/">Henrique Mohallem Paiva</a> 

## 📝 Descrição

A inspeção predial é um processo essencial para garantir a segurança, a funcionalidade e a conservação dos edifícios. No entanto, o setor ainda enfrenta desafios significativos, como a grande variedade de tipologias construtivas, a falta de padronização nos métodos de inspeção e a dificuldade em gerenciar os dados coletados durante as vistorias. Muitas das soluções existentes não conseguem atender de forma eficaz a essas necessidades, seja pela baixa flexibilidade, pela dificuldade de uso ou por falhas na organização e segurança das informações.

Pensando nisso, o InspPecT foi desenvolvido como uma solução inovadora e completa para transformar o modo como as inspeções prediais são realizadas. Trata-se de uma aplicação web intuitiva, segura e acessível em qualquer dispositivo, criada para facilitar a coleta, o registro e a gestão dos dados técnicos, fotos e documentos ao longo de todo o processo de inspeção.

## ✨ Funcionalidades Principais

* **Personalização Avançada:** Campos e formulários ajustáveis às características específicas de cada edifício.
* **Integração com Modelos Digitais:** Associação direta de informações coletadas a modelos digitais da edificação.
* **Geração Automática de Relatórios:** Criação de relatórios técnicos padronizados e exportáveis.
* **Segurança e Rastreabilidade:** Controle de acesso rigoroso e identificação dos responsáveis por cada entrada e edição de dados.
* **Acesso Multiplataforma:** Interface intuitiva e acessível em diversos dispositivos.

## 🎯 Valor Entregue

O InspPecT vai além da digitalização da inspeção predial: ele traz agilidade, organização e confiabilidade para um setor que ainda carece de ferramentas adequadas às suas demandas específicas. Ao integrar tecnologia de ponta com as boas práticas da engenharia diagnóstica, o InspPecT fortalece o trabalho dos profissionais e instituições como o IPT (Instituto de Pesquisas Tecnológicas), impulsionando a modernização do setor e a excelência técnica nas avaliações de edificações.

Em resumo, o InspPecT é a solução que conecta inovação e necessidade prática, transformando a inspeção predial em um processo mais eficiente, seguro e alinhado às exigências do mercado atual.

## 🔗 Link de Demonstração


* **Projeto Publicado:** [link do deploy do site](https://inspect-e0q2.onrender.com/)
* **Vídeo de Demonstração:** [link para o vídeo](https://drive.google.com/file/d/1dKHL4Ex8JftmOQRJsU5Y5fmTLen7nkKr/view?usp=sharing)


## 🛠️ Tecnologias Utilizadas

* **Backend:** Node.js, Express.js
* **Banco de Dados:** PostgreSQL
* **Frontend:** HTML, CSS, JavaScript (com EJS para renderização no lado do servidor, se aplicável)
* **Outras bibliotecas importantes:** `pg` (Node-Postgres), `bcryptjs` (para hashing de senhas), `dotenv` (para variáveis de ambiente).

## 📁 Estrutura de pastas

Dentre os arquivos e pastas presentes na raiz do projeto, definem-se:

- <b>assets</b>: aqui estão os arquivos relacionados a elementos não-estruturados deste repositório, como imagens.

- <b>document</b>: aqui estão todos os documentos do projeto, como o Web Application  Document (WAD) bem como documentos complementares, na pasta "other".

- <b>src</b>: Todo o código fonte criado para o desenvolvimento do projeto de aplicação web.

- <b>README.md</b>: arquivo que serve como guia introdutório e explicação geral sobre o projeto e a aplicação (o mesmo arquivo que você está lendo agora).

## 💻 Configuração para Desenvolvimento e Execução

Siga estas instruções para configurar e executar o projeto em seu ambiente de desenvolvimento local.

### Pré-requisitos
* **Node.js:** Versão 18.x LTS ou superior recomendada. Você pode baixar em [https://nodejs.org/](https://nodejs.org/).
* **npm** (geralmente vem com o Node.js) 
* **PostgreSQL:** Um servidor PostgreSQL instalado e em execução, com o utilitário de linha de comando `psql` acessível no seu PATH (geralmente instalado com o servidor).
* **Git:** Para clonar o repositório.
* **IDE de sua preferência:** (ex: VS Code).

### Passos para Instalação e Execução

1.  **Clonar o Repositório:**
    ```bash
    git clone https://github.com/Inteli-College/2025-1B-T16-IN02-G02
    cd 2025-1B-T16-IN02-G02
    ```

2.  **Configurar Variáveis de Ambiente:**
    * Navegue até a pasta raiz do backend (provavelmente `src/` ou a raiz do projeto se `app.js` estiver lá).
    * Crie um arquivo chamado `.env` nesta pasta.
    * Copie o conteúdo de um arquivo `.env.example` (se existir) para o seu `.env`, ou adicione as seguintes variáveis, ajustando os valores conforme sua configuração local:
        ```env
        DB_USER=seu_usuario 
        DB_HOST=seu_host
        DB_DATABASE=nome_do_seu_banco
        DB_PASSWORD=sua_senha
        DB_PORT=5432
        PORT=3000 
        ```

3.  **Instalar Dependências do Backend:**
    * Navegue até a pasta raiz do backend (onde está o `package.json` do backend).
    * Execute o comando:
        ```bash
        npm install
        ```
    Isso instalará todas as dependências listadas no `package.json`.

4.  **Configurar o Banco de Dados:**
    * **Inicializar o Esquema do Banco (Criar Tabelas):**
        O projeto já possui um script configurado no `package.json` para criar todas as tabelas necessárias. Com as dependências já instaladas e o arquivo `.env` configurado, navegue até a pasta que contém o arquivo `package.json` do backend no terminal e execute:
        ```bash
        npm run inib:db
        ```
        Este comando utilizará as configurações do seu `package.json` para executar o script SQL de criação do schema (ex: `document/database/schema.sql`). Certifique-se de que as credenciais e caminhos dentro do script `inib:db` no `package.json` estão corretos para o seu ambiente. 

5.  **Iniciar a Aplicação Backend:**
    * Ainda na pasta raiz do backend, execute:
        ```bash
        npm start
        ```

6.  **Acessar a Aplicação:**
    * O servidor backend estará online em `http://localhost:PORT` (substitua `PORT` pelo valor do seu `.env`, ex: `http://localhost:3000`).

## 📄 Documentação da API

A documentação detalhada dos endpoints da API, pode ser encontrada no arquivo `WAD.md` na pasta `documentos/` na seção 3.6.


## 🗃 Histórico de Lançamentos

   **0.0.1 - 30/04/2025**  

- Estudo de mercado (SWOT, Matriz de Risco e 5 Forças de Porter)  
- Idealização do projeto  
- Apresentação Sprint 1 para o IPT  

**0.0.2 - 16/05/2025**
  
- Estruturação da tabela de Banco de Dados
- Criação de Wireframes  
- Apresentação Sprint 2 para o IPT  

**0.0.3 - 30/05/2025**  

- Implementação do prótotipo de alta fidelidade
- Implementação dos dados de Guia de estilos  
- Relatórios dos Endpoints  
- Documentação da API 
- Apresentação Sprint 3 para o IPT

**0.0.4 - 13/06/2025**

- Implementação do Backend
- Implementação do Frontend
- Testes de Integração
- Apresentação Sprint 4 para o IPT

**0.0.5 - 26/06/2025**
- Revisão da documentação e do código
- Finalização do MVP
- Apresentação final



## 📋 Licença/License
<a href="https://github.com/Inteli-College/2025-1B-T16-IN02-G02">InPecT</a> © 2025 by <a href="https://github.com/Inteli-College/2025-1B-T16-IN02-G02">INTELI,[Ana Júlia Rodrigues Ribeiro](https://www.linkedin.com/in/ana-j%C3%BAlia-ribeiro/), [Bernardo Laurindo Gonzaga](https://www.linkedin.com/in/bernardolaurindo/), [Clara de Borba Gutierrez Benito](https://www.linkedin.com/in/clara-benito/), [João Cardoso Dias](https://www.linkedin.com/in/jo%C3%A3ocardosodias/), [Marcus Felipe dos Santos Valent](https://www.linkedin.com/in/marcus-valente/), [Rebeca Namura Sbroglio](https://www.linkedin.com/in/rebeca-namura-sbroglio-0a85ba358/), [Vitor Ribeiro de Mattos Silva](//www.linkedin.com/in/vitor-ribeiro-2822a932a/)</a> is licensed under <a href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution 4.0 International</a><img src="https://mirrors.creativecommons.org/presskit/icons/cc.svg" style="max-width: 1em;max-height:1em;margin-left: .2em;"><img src="https://mirrors.creativecommons.org/presskit/icons/by.svg" style="max-width: 1em;max-height:1em;margin-left: .2em;">


