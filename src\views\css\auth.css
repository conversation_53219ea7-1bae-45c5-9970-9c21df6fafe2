header {
    display: none;
}

.sidebar {
    display: none;
}

main {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
}

.error-message {
    color: #ff4444;
    font-size: 12px;
    margin-top: 4px;
    margin-bottom: 8px;
    display: none;
}

.login-container {
    color: var(--background-color);
    padding: 24px;
    border-radius: 12px;
    min-width: 300px;
    max-width: 400px;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.logoIPT {
    align-self: center;
}

.logoIPT img {
    height: 92px;
    align-self: center;
    margin: auto;
}

.Entrar {
    text-align: left;
    margin-top: 20px;
    margin-bottom: 20px;
}


.logo h2 {
    font-size: 12px;
    color: var(--muted-foreground-color);
    margin-top: 5px;
    line-height: 1.2;
}

h1 {
    margin-top: 20px;
    font-size: 24px;
    color: var(--foreground-color);
}

p {
    color: var(--muted-foreground-color);
}

.input-group {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
    padding: 10px;
    border: 1px solid var(--muted-color);
    border-radius: 8px;
}

.input-group input {
    flex: 1;
    outline: none;
    border: none;
    font-size: 14px;
}

.options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 13px;
    color: var(--muted-foreground-color);
}

.options a {
    color: var(--secondary-color);
    text-decoration: none;
}

.btn-entrar {
    background-color: var(--primary-color);
    color: var(--background-color);
    border: none;
    padding: 10px;
    width: 100%;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
}



.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    max-width: 400px;
    font-weight: 500;
    font-size: 14px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    background-color: #10b981;
    color: white;
    border-left: 4px solid #059669;
}

.toast.error {
    background-color: #ef4444;
    color: white;
    border-left: 4px solid #dc2626;
}

.toast-icon {
    margin-right: 12px;
    font-size: 18px;
    flex-shrink: 0;
}

.toast-content {
    flex: 1;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.toast-close:hover {
    opacity: 1;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.3);
    transition: width linear;
}

.toast.success .toast-progress {
    background-color: rgba(255, 255, 255, 0.4);
}

.toast.error .toast-progress {
    background-color: rgba(255, 255, 255, 0.4);
}

@media (max-width: 480px) {
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }

    .toast {
        min-width: auto;
        max-width: none;
    }
}
