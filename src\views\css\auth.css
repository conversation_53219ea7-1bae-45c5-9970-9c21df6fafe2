header {
    display: none;
}

.sidebar {
    display: none;
}

main {
    height: 100vh;
    overflow: hidden;
}

/* Layout principal */
.login-layout {
    display: flex;
    height: 100vh;
    min-height: 100vh;
    overflow: hidden;
}

/* Lado esquerdo - Informações do IPT */
.login-info-side {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color) 0%, #004070 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
}

.login-info-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.info-content {
    position: relative;
    z-index: 1;
    max-width: 480px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 100%;
    padding: 1rem 0;
}

/* Logo e cabeçalho */
.logo-section {
    text-align: center;
    margin-bottom: 2rem;
    flex-shrink: 0;
}

.main-logo {
    height: 100px;
    margin-bottom: 0.75rem;
    filter: brightness(0) invert(1);
}

.logo-section h2 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.4rem;
    line-height: 1.3;
}

.tagline {
    font-size: 0.95rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Seção de recursos */
.features-section {
    margin-bottom: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.features-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.2rem;
    text-align: center;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.feature-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.6rem;
    flex-shrink: 0;
}

.feature-icon svg {
    width: 20px;
    height: 20px;
    stroke: white;
}

.feature-text h4 {
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.15rem;
    line-height: 1.2;
}

.feature-text p {
    font-size: 0.8rem;
    opacity: 0.85;
    line-height: 1.3;
}

/* Lado direito - Formulário */
.login-form-side {
    flex: 0 0 420px;
    background: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

/* Toggle de tema */
.theme-toggle-container {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 10;
}

.theme-toggle-btn {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: scale(1.15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-icon-global {
    font-size: 1.3rem;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.theme-toggle-btn:hover .theme-icon-global {
    transform: rotate(20deg) scale(1.1);
}

/* Card do formulário */
.form-card {
    background: var(--background-color);
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--muted-color);
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 85, 140, 0.3), transparent);
    pointer-events: none;
}

.form-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
}



.form-container {
    width: 100%;
    max-width: 340px;
    position: relative;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h1 {
    font-size: 1.9rem;
    font-weight: 700;
    color: var(--foreground-color);
    margin-bottom: 0.5rem;
}

.form-header p {
    color: var(--muted-foreground-color);
    font-size: 0.95rem;
}

/* Estilos do formulário */
.input-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 14px 18px;
    border: 2px solid var(--muted-color);
    border-radius: 16px;
    background: var(--background-color);
    transition: all 0.3s ease;
    position: relative;
}

.input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(0, 85, 140, 0.1);
    transform: translateY(-1px);
}

.input-group img {
    width: 20px;
    height: 20px;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.input-group:focus-within img {
    opacity: 0.8;
}

.input-group input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
    color: var(--foreground-color);
}

.input-group input::placeholder {
    color: var(--muted-foreground-color);
}

.error-message {
    color: var(--destructive-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: none;
}

.options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.options label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--foreground-color);
    cursor: pointer;
}

.options input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.options a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.options a:hover {
    text-decoration: underline;
}

.btn-entrar {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 16px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-entrar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-entrar:hover::before {
    left: 100%;
}

.btn-entrar:hover {
    background: #004a7a;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 85, 140, 0.4);
}

/* Modo escuro */
body[data-theme="dark"] .login-info-side {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

body[data-theme="dark"] .login-form-side {
    background: var(--background-color);
}

body[data-theme="dark"] .input-group {
    background: #374151;
    border-color: #6b7280;
}

body[data-theme="dark"] .input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

body[data-theme="dark"] .input-group input {
    background: transparent;
    color: var(--foreground-color);
}

body[data-theme="dark"] .input-group img {
    filter: brightness(1.2);
}

body[data-theme="dark"] .btn-entrar {
    background: var(--primary-color);
}

body[data-theme="dark"] .btn-entrar:hover {
    background: #2563eb;
}

body[data-theme="dark"] .form-card {
    background: #1f2937;
    border-color: #4b5563;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

body[data-theme="dark"] .form-card::before {
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
}

body[data-theme="dark"] .form-card:hover {
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
}

body[data-theme="dark"] .theme-toggle-btn {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: rgba(59, 130, 246, 0.4) !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

body[data-theme="dark"] .theme-toggle-btn:hover {
    background: rgba(59, 130, 246, 0.3) !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Responsividade */
@media (max-width: 1200px) {
    .login-info-side {
        padding: 1.25rem;
    }

    .login-form-side {
        flex: 0 0 380px;
        padding: 1.5rem;
    }

    .info-content {
        max-width: 420px;
    }

    .main-logo {
        height: 90px;
    }

    .logo-section h2 {
        font-size: 1.2rem;
    }

    .features-section h3 {
        font-size: 1.1rem;
    }
}

@media (max-width: 1024px) {
    .login-info-side {
        flex: 0 0 45%;
        padding: 1rem;
    }

    .login-form-side {
        flex: 0 0 55%;
        padding: 1.25rem;
    }

    .info-content {
        max-width: 360px;
        padding: 0.5rem 0;
    }

    .logo-section {
        margin-bottom: 1.5rem;
    }

    .main-logo {
        height: 80px;
    }

    .features-section {
        margin-bottom: 1rem;
    }

    .feature-list {
        gap: 0.8rem;
    }

    .feature-item {
        padding: 0.4rem;
        gap: 0.6rem;
    }

    .feature-icon {
        padding: 0.5rem;
    }

    .feature-icon svg {
        width: 18px;
        height: 18px;
    }

    .feature-text h4 {
        font-size: 0.9rem;
    }

    .feature-text p {
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .login-layout {
        flex-direction: column;
        height: 100vh;
        overflow: hidden;
    }

    .login-info-side {
        flex: 0 0 45vh;
        padding: 1rem;
        overflow-y: auto;
    }

    .login-form-side {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
    }

    .form-card {
        padding: 1.5rem;
        border-radius: 20px;
        margin: 0;
    }

    .info-content {
        max-width: none;
        min-height: auto;
        padding: 0;
        justify-content: flex-start;
    }

    .logo-section {
        margin-bottom: 1rem;
    }

    .main-logo {
        height: 70px;
        margin-bottom: 0.5rem;
    }

    .logo-section h2 {
        font-size: 1.1rem;
        margin-bottom: 0.25rem;
    }

    .tagline {
        font-size: 0.85rem;
    }

    .features-section {
        margin-bottom: 0.5rem;
    }

    .features-section h3 {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }

    .feature-list {
        gap: 0.6rem;
    }

    .feature-item {
        padding: 0.35rem;
        gap: 0.5rem;
        border-radius: 12px;
    }

    .feature-icon {
        padding: 0.4rem;
        border-radius: 8px;
    }

    .feature-icon svg {
        width: 16px;
        height: 16px;
    }

    .feature-text h4 {
        font-size: 0.85rem;
        margin-bottom: 0.1rem;
    }

    .feature-text p {
        font-size: 0.7rem;
    }

    .theme-toggle-container {
        top: 1rem;
        right: 1rem;
    }

    .theme-toggle-btn {
        width: 40px;
        height: 40px;
    }

    .form-header h1 {
        font-size: 1.7rem;
    }

    .form-header p {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .login-info-side {
        flex: 0 0 40vh;
        padding: 0.75rem;
    }

    .login-form-side {
        padding: 0.75rem;
    }

    .form-card {
        padding: 1.25rem;
        border-radius: 18px;
    }

    .logo-section {
        margin-bottom: 0.75rem;
    }

    .main-logo {
        height: 60px;
    }

    .logo-section h2 {
        font-size: 1rem;
    }

    .tagline {
        font-size: 0.8rem;
    }

    .features-section h3 {
        font-size: 0.95rem;
        margin-bottom: 0.6rem;
    }

    .feature-list {
        gap: 0.5rem;
    }

    .feature-item {
        padding: 0.3rem;
        gap: 0.4rem;
    }

    .feature-icon {
        padding: 0.35rem;
    }

    .feature-icon svg {
        width: 14px;
        height: 14px;
    }

    .feature-text h4 {
        font-size: 0.8rem;
    }

    .feature-text p {
        font-size: 0.65rem;
    }

    .theme-toggle-container {
        top: 0.75rem;
        right: 0.75rem;
    }

    .theme-toggle-btn {
        width: 36px;
        height: 36px;
    }

    .theme-icon {
        width: 16px;
        height: 16px;
    }

    .form-container {
        max-width: none;
    }

    .form-header {
        margin-bottom: 1.5rem;
    }

    .form-header h1 {
        font-size: 1.5rem;
    }

    .form-header p {
        font-size: 0.85rem;
    }

    .input-group {
        padding: 12px 14px;
        border-radius: 14px;
    }

    .btn-entrar {
        padding: 14px 20px;
        border-radius: 14px;
    }
}

.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    max-width: 400px;
    font-weight: 500;
    font-size: 14px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    background-color: #10b981;
    color: white;
    border-left: 4px solid #059669;
}

.toast.error {
    background-color: #ef4444;
    color: white;
    border-left: 4px solid #dc2626;
}

.toast-icon {
    margin-right: 12px;
    font-size: 18px;
    flex-shrink: 0;
}

.toast-content {
    flex: 1;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.toast-close:hover {
    opacity: 1;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.3);
    transition: width linear;
}

.toast.success .toast-progress {
    background-color: rgba(255, 255, 255, 0.4);
}

.toast.error .toast-progress {
    background-color: rgba(255, 255, 255, 0.4);
}

@media (max-width: 480px) {
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }

    .toast {
        min-width: auto;
        max-width: none;
    }
}
