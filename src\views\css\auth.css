header {
    display: none;
}

.sidebar {
    display: none;
}

main {
    height: 100vh;
    overflow: hidden;
}

/* Layout principal */
.login-layout {
    display: flex;
    height: 100vh;
    min-height: 100vh;
}

/* Lado esquerdo - Informações do IPT */
.login-info-side {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.login-info-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.info-content {
    position: relative;
    z-index: 1;
    max-width: 500px;
    width: 100%;
}

/* Logo e cabeçalho */
.logo-section {
    text-align: center;
    margin-bottom: 3rem;
}

.main-logo {
    height: 120px;
    margin-bottom: 1rem;
    filter: brightness(0) invert(1);
}

.logo-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.tagline {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Seção de recursos */
.features-section {
    margin-bottom: 3rem;
}

.features-section h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.75rem;
    flex-shrink: 0;
}

.feature-icon svg {
    width: 24px;
    height: 24px;
    stroke: white;
}

.feature-text h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.feature-text p {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
}

/* Imagens de inspeção */
.inspection-images {
    text-align: center;
}

.image-grid {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.inspection-image {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.inspection-image svg {
    width: 48px;
    height: 48px;
    stroke: white;
    opacity: 0.9;
}

/* Lado direito - Formulário */
.login-form-side {
    flex: 0 0 450px;
    background: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.form-container {
    width: 100%;
    max-width: 350px;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--foreground-color);
    margin-bottom: 0.5rem;
}

.form-header p {
    color: var(--muted-foreground-color);
    font-size: 1rem;
}

/* Estilos do formulário */
.input-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border: 2px solid var(--muted-color);
    border-radius: 12px;
    background: var(--background-color);
    transition: all 0.3s ease;
}

.input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
}

.input-group img {
    width: 20px;
    height: 20px;
    opacity: 0.6;
}

.input-group input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
    color: var(--foreground-color);
}

.input-group input::placeholder {
    color: var(--muted-foreground-color);
}

.error-message {
    color: var(--destructive-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: none;
}

.options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.options label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--foreground-color);
    cursor: pointer;
}

.options input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.options a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.options a:hover {
    text-decoration: underline;
}

.btn-entrar {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-entrar:hover {
    background: #004a7a;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 85, 140, 0.3);
}

/* Modo escuro */
[data-theme="dark"] .login-form-side {
    background: var(--background-color);
}

[data-theme="dark"] .input-group {
    background: #374151;
    border-color: #6b7280;
}

[data-theme="dark"] .input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

[data-theme="dark"] .input-group input {
    background: transparent;
    color: var(--foreground-color);
}

[data-theme="dark"] .input-group img {
    filter: brightness(1.2);
}

[data-theme="dark"] .btn-entrar {
    background: var(--primary-color);
}

[data-theme="dark"] .btn-entrar:hover {
    background: #2563eb;
}

/* Responsividade */
@media (max-width: 1024px) {
    .login-info-side {
        flex: 0 0 40%;
    }

    .login-form-side {
        flex: 0 0 60%;
    }

    .features-section {
        margin-bottom: 2rem;
    }

    .inspection-images {
        display: none;
    }
}

@media (max-width: 768px) {
    .login-layout {
        flex-direction: column;
    }

    .login-info-side {
        flex: 0 0 40vh;
        padding: 1.5rem;
    }

    .login-form-side {
        flex: 1;
        padding: 1.5rem;
    }

    .info-content {
        max-width: none;
    }

    .logo-section {
        margin-bottom: 1.5rem;
    }

    .main-logo {
        height: 80px;
    }

    .logo-section h2 {
        font-size: 1.2rem;
    }

    .features-section {
        margin-bottom: 1rem;
    }

    .feature-list {
        gap: 1rem;
    }

    .feature-item {
        gap: 0.75rem;
    }

    .feature-icon {
        padding: 0.5rem;
    }

    .feature-text h4 {
        font-size: 0.9rem;
    }

    .feature-text p {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .login-info-side {
        padding: 1rem;
    }

    .login-form-side {
        padding: 1rem;
    }

    .form-container {
        max-width: none;
    }

    .form-header h1 {
        font-size: 1.75rem;
    }

    .input-group {
        padding: 10px 12px;
    }

    .btn-entrar {
        padding: 12px 20px;
    }
}

.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    max-width: 400px;
    font-weight: 500;
    font-size: 14px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    background-color: #10b981;
    color: white;
    border-left: 4px solid #059669;
}

.toast.error {
    background-color: #ef4444;
    color: white;
    border-left: 4px solid #dc2626;
}

.toast-icon {
    margin-right: 12px;
    font-size: 18px;
    flex-shrink: 0;
}

.toast-content {
    flex: 1;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.toast-close:hover {
    opacity: 1;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.3);
    transition: width linear;
}

.toast.success .toast-progress {
    background-color: rgba(255, 255, 255, 0.4);
}

.toast.error .toast-progress {
    background-color: rgba(255, 255, 255, 0.4);
}

@media (max-width: 480px) {
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }

    .toast {
        min-width: auto;
        max-width: none;
    }
}
