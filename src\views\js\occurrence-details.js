document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem('authToken');
    if (!token) {
        document.body.innerHTML = '<h1 class="error-message">❌ Acesso negado. Faça o login.</h1>';
        return;
    }

    const errorContainer = document.getElementById('error-container');
    const mainContent = document.getElementById('main-content');

    const pathParts = window.location.pathname.split('/');
    const occurrenceId = pathParts[pathParts.length - 1];

    if (!occurrenceId || !/^\d+$/.test(occurrenceId)) {
        errorContainer.textContent = '❌ Erro: ID da ocorrência inválido ou não encontrado na URL.';
        errorContainer.classList.remove('hidden');
        return;
    }

    try {
        const apiHeaders = { 'Authorization': `Bearer ${token}` };

        const [respostaOcorrencia, respostaFotosMeta] = await Promise.all([
            fetch(`/api/ocorrencias/${occurrenceId}`, { headers: apiHeaders }),
            fetch(`/api/fotos/ocorrencia/${occurrenceId}`, { headers: apiHeaders })
        ]);

        if (!respostaOcorrencia.ok) throw new Error('Falha ao buscar dados da ocorrência.');
        if (!respostaFotosMeta.ok) throw new Error('Falha ao buscar metadados das fotos.');

        const ocorrencia = await respostaOcorrencia.json();
        const fotosMetadata = await respostaFotosMeta.json();

        document.getElementById('occurrence-title').textContent = ocorrencia.titulo || 'Ocorrência sem Título';
        document.getElementById('occurrence-date').textContent = new Date(ocorrencia.criado_em).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
        document.getElementById('occurrence-description').textContent = ocorrencia.descricao || 'Nenhuma descrição fornecida.';
        document.getElementById('occurrence-creator').textContent = ocorrencia.criado_por || 'Desconhecido';

        const gallery = document.getElementById('photo-gallery');
        if (fotosMetadata && fotosMetadata.length > 0) {
            gallery.innerHTML = '<p class="placeholder-text body1 text-muted-foreground">Carregando imagens...</p>';
            const promisesDeUrlAssinada = fotosMetadata.map(foto =>
                fetch(`/api/fotos/${foto.id}/signed-url`, { headers: apiHeaders })
            );
            const respostasDeUrl = await Promise.all(promisesDeUrlAssinada);
            const dadosDasUrls = await Promise.all(
                respostasDeUrl.map(res => res.ok ? res.json() : null)
            );

            gallery.innerHTML = dadosDasUrls.map(data => {
                const altText = ocorrencia.titulo || 'Foto da ocorrência';
                if (data && data.signedUrl) {
                    return `<img src="${data.signedUrl}" alt="${altText}">`;
                }
                return '<p class="error-text">Erro ao carregar imagem</p>';
            }).join('');
            
            gallery.style.removeProperty('aspect-ratio');
        } else {
            gallery.innerHTML = '<p class="empty-text body1 text-muted-foreground">Nenhuma imagem disponível.</p>';
        }

        const tagsContainer = document.getElementById('occurrence-tags');
        tagsContainer.innerHTML = '';

        if (ocorrencia.id_tipo_sistema) {
            tagsContainer.innerHTML += `<span class="tag primary active">${ocorrencia.id_tipo_sistema}</span>`;
        }
        if (ocorrencia.id_tipo_patologia) {
            tagsContainer.innerHTML += `<span class="tag secondary">${ocorrencia.id_tipo_patologia}</span>`;
        }

        mainContent.classList.remove('hidden');

    } catch (error) {
        console.error('Erro ao carregar detalhes da ocorrência:', error);
        errorContainer.textContent = `❌ ${error.message}`;
        errorContainer.classList.remove('hidden');
        mainContent.classList.add('hidden');
    }
});