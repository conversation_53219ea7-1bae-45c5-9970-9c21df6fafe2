class ReportDetailsManager {
    constructor() {
        this.reportId = this.getReportIdFromUrl();
        this.reportData = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadReportPreview();
    }

    getReportIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 1];
    }

    setupEventListeners() {
        const downloadBtn = document.getElementById('download-btn');

        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadPDF());
        }
    }

    async loadReportPreview() {
        try {
            this.showLoading();
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                throw new Error('Token de autenticação não encontrado');
            }

            const response = await fetch(`/api/relatorios/${this.reportId}/preview`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 401) {
                window.location.href = '/';
                return;
            }

            if (!response.ok) {
                throw new Error(`Erro HTTP: ${response.status}`);
            }

            this.reportData = await response.json();
            this.renderReportPreview();
            this.hideLoading();

        } catch (error) {
            console.error('Erro ao carregar preview do relatório:', error);
            this.showError(error.message);
        }
    }

    renderReportPreview() {
        const { relatorio, inspecao, endereco, equipe, projeto, ambientes, ocorrencias, estatisticas, dataGeracao } = this.reportData;

        this.updateElement('report-title', relatorio.titulo);
        this.updateElement('generation-date', new Date(dataGeracao).toLocaleString('pt-BR'));
        this.updateElement('inspection-name', inspecao.nome);
        this.updateElement('team-name', equipe?.nome || 'Não informado');
        this.updateElement('project-code', projeto?.codigo_projeto || 'Não informado');

        this.updateElement('address', this.formatAddress(endereco));
        this.updateElement('building-type', inspecao.tipo_edificacao || 'Não especificado');
        this.updateElement('status', inspecao.status || 'Em andamento');
        this.updateElement('creation-date', new Date(inspecao.criado_em).toLocaleDateString('pt-BR'));

        this.updateElement('total-environments', estatisticas.totalAmbientes);
        this.updateElement('total-occurrences', estatisticas.totalOcorrencias);
        this.updateElement('affected-environments', estatisticas.ambientesComOcorrencias);
        this.updateElement('affected-percentage', `${estatisticas.percentualAmbientesAfetados}%`);

        this.renderDetailedEnvironments(ambientes);
        this.renderDetailedOccurrences(ocorrencias);

        document.getElementById('report-preview').style.display = 'block';
    }



    renderEnvironmentsList(ambientes) {
        const container = document.getElementById('environments-list');
        
        if (!ambientes || ambientes.length === 0) {
            container.innerHTML = '<p class="empty-state">Nenhum ambiente encontrado.</p>';
            return;
        }

        container.innerHTML = ambientes.map(ambiente => `
            <div class="environment-item">
                <h4>${ambiente.titulo}</h4>
                <p>${ambiente.observacoes || 'Sem observações'}</p>
            </div>
        `).join('');
    }

    renderOccurrencesList(ocorrencias) {
        const container = document.getElementById('occurrences-list');
        
        if (!ocorrencias || ocorrencias.length === 0) {
            container.innerHTML = '<p class="empty-state">Nenhuma ocorrência encontrada.</p>';
            return;
        }

        container.innerHTML = ocorrencias.map(ocorrencia => `
            <div class="occurrence-item">
                <h4>${ocorrencia.titulo}</h4>
                <p>${ocorrencia.descricao || 'Sem descrição'}</p>
                <small>Data: ${ocorrencia.dataocm ? new Date(ocorrencia.dataocm).toLocaleDateString('pt-BR') : 'Não informada'}</small>
            </div>
        `).join('');
    }

    renderDetailedEnvironments(ambientes) {
        const container = document.getElementById('detailed-environments');

        if (!ambientes || ambientes.length === 0) {
            container.innerHTML = '<p class="empty-state">Nenhum ambiente encontrado.</p>';
            return;
        }

        const environmentsHTML = ambientes.map(ambiente => `
            <div class="detailed-environment-item">
                <div class="detailed-environment-header">
                    <h4 class="detailed-environment-title">${ambiente.titulo}</h4>
                    <div class="detailed-environment-meta">
                        <span>📊 ${ambiente.totalOcorrencias || 0} ocorrências</span>
                        <span>📸 ${ambiente.totalFotos || 0} fotos</span>
                        ${ambiente.ambientesFilhos?.length ? `<span>🏠 ${ambiente.ambientesFilhos.length} sub-ambientes</span>` : ''}
                    </div>
                </div>
                <div class="detailed-environment-content">
                    ${ambiente.observacoes ? `
                        <div class="detail-section">
                            <h5>📝 Observações</h5>
                            <p>${ambiente.observacoes}</p>
                        </div>
                    ` : ''}

                    ${ambiente.ambientesFilhos?.length ? `
                        <div class="detail-section">
                            <h5>🏠 Sub-ambientes</h5>
                            <div class="sub-environments">
                                ${ambiente.ambientesFilhos.map(filho => `
                                    <span class="sub-environment-tag">${filho.titulo}</span>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    ${ambiente.ocorrencias?.length ? `
                        <div class="detail-section">
                            <h5>⚠️ Ocorrências neste Ambiente</h5>
                            <div class="environment-occurrences">
                                ${ambiente.ocorrencias.map(ocr => `
                                    <div class="mini-occurrence">
                                        <strong>${ocr.titulo}</strong>
                                        ${ocr.descricao ? `<p>${ocr.descricao}</p>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    ${ambiente.fotos?.length ? `
                        <div class="detail-section">
                            <h5>📸 Fotos do Ambiente (${ambiente.fotos.length} encontradas)</h5>
                            <div class="photos-grid">
                                ${ambiente.fotos.map((foto, index) => `
                                    <div class="photo-item">
                                        <img
                                            src="${foto.url}"
                                            alt="Foto ${index + 1} do ambiente ${ambiente.titulo}"
                                            loading="lazy"
                                            onerror="this.style.display='none'; this.nextElementSibling.innerHTML='❌ Erro ao carregar imagem';"
                                            onload="this.style.opacity='1';"
                                            style="opacity: 0; transition: opacity 0.3s ease;"
                                        >
                                        <div class="photo-caption">${ambiente.titulo} - Foto ${index + 1}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');

        container.innerHTML = environmentsHTML;
    }

    renderDetailedOccurrences(ocorrencias) {
        const container = document.getElementById('detailed-occurrences');

        if (!ocorrencias || ocorrencias.length === 0) {
            container.innerHTML = '<p class="empty-state">Nenhuma ocorrência encontrada.</p>';
            return;
        }

        const occurrencesHTML = ocorrencias.map(ocorrencia => `
            <div class="detailed-occurrence-item">
                <div class="detailed-occurrence-header">
                    <h4 class="detailed-occurrence-title">${ocorrencia.titulo}</h4>
                    <div class="detailed-occurrence-meta">
                        <span>📅 ${ocorrencia.dataFormatada}</span>
                        <span>📸 ${ocorrencia.totalFotos || 0} fotos</span>
                        ${ocorrencia.ambiente_titulo ? `<span>🏠 ${ocorrencia.ambiente_titulo}</span>` : ''}
                    </div>
                </div>
                <div class="detailed-occurrence-content">
                    ${ocorrencia.descricao ? `
                        <div class="detail-section">
                            <h5>📝 Descrição</h5>
                            <p>${ocorrencia.descricao}</p>
                        </div>
                    ` : ''}

                    <div class="detail-section">
                        <h5>🔍 Classificação</h5>
                        <div class="classification-grid">
                            ${ocorrencia.tipoPatologia ? `
                                <div class="classification-item">
                                    <strong>Tipo de Patologia:</strong>
                                    <span>${ocorrencia.tipoPatologia.nome}</span>
                                </div>
                            ` : ''}
                            ${ocorrencia.tipoSistema ? `
                                <div class="classification-item">
                                    <strong>Sistema Afetado:</strong>
                                    <span>${ocorrencia.tipoSistema.nome}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    ${ocorrencia.fotos?.length ? `
                        <div class="detail-section">
                            <h5>📸 Fotos da Ocorrência (${ocorrencia.fotos.length} encontradas)</h5>
                            <div class="photos-grid">
                                ${ocorrencia.fotos.map((foto, index) => `
                                    <div class="photo-item">
                                        <img
                                            src="${foto.url}"
                                            alt="Foto ${index + 1} da ocorrência ${ocorrencia.titulo}"
                                            loading="lazy"
                                            onerror="this.style.display='none'; this.nextElementSibling.innerHTML='❌ Erro ao carregar imagem';"
                                            onload="this.style.opacity='1';"
                                            style="opacity: 0; transition: opacity 0.3s ease;"
                                        >
                                        <div class="photo-caption">${ocorrencia.titulo} - Foto ${index + 1}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');

        container.innerHTML = occurrencesHTML;
    }

    formatAddress(endereco) {
        if (!endereco) return 'Endereço não informado';
        
        let address = `${endereco.numero || 'S/N'}`;
        if (endereco.complemento) address += `, ${endereco.complemento}`;
        if (endereco.cep) address += ` - CEP: ${endereco.cep}`;
        if (endereco.referencia) address += ` (${endereco.referencia})`;
        
        return address;
    }

    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content || '-';
        }
    }



    async downloadPDF() {
        const downloadBtn = document.getElementById('download-btn');
        const originalText = downloadBtn.innerHTML;

        try {
            downloadBtn.innerHTML = '<div class="spinner" style="width: 16px; height: 16px; margin-right: 0.5rem;"></div>Gerando PDF...';
            downloadBtn.disabled = true;

            const reportId = window.location.pathname.split('/').pop();

            const token = localStorage.getItem('authToken');
            if (!token) {
                alert('Acesso negado. Por favor, faça o login.');
                window.location.href = '/';
                return;
            }

            const response = await fetch(`/api/relatorios/${reportId}/download`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Erro ao gerar PDF');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `relatorio_${reportId}_${new Date().toISOString().split('T')[0]}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Erro ao gerar PDF:', error);
            alert('Erro ao gerar PDF. Tente novamente.');
        } finally {
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
        }
    }

    showLoading() {
        document.getElementById('loading').style.display = 'flex';
        document.getElementById('report-preview').style.display = 'none';
        document.getElementById('error-state').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    showError(message) {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('report-preview').style.display = 'none';
        document.getElementById('error-state').style.display = 'flex';
        document.getElementById('error-message').textContent = message;
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new ReportDetailsManager();
});