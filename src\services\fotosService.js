const FotoModel = require('../models/fotosModel');
const AmbienteModel = require('../models/ambientesModel');
const supabase = require('../config/supabaseClient');

class FotosService {
    
    async gerarUrlAssinada(id_foto) {
        try {
            const foto = await FotoModel.findById(id_foto);
            if (!foto) {
                throw new Error('Foto não encontrada.');
            }

            const nomeArquivo = foto.url.split('/').pop();
            const expiresIn = 60;

            const { data, error } = await supabase.storage
                .from('images')
                .createSignedUrl(nomeArquivo, expiresIn);

            if (error) {
                throw new Error(`Falha ao gerar URL assinada: ${error.message}`);
            }

            return { signedUrl: data.signedUrl };
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] gerarUrlAssinada:', error);
            throw error;
        }
    }

    async listarFotosPorAmbiente(id_ambiente, id_ocorrencia) {
        try {
            
            const ambiente = await AmbienteModel.findById(id_ambiente);
            if (!ambiente) {
                throw new Error(`Ambiente com ID ${id_ambiente} não encontrado.`);
            }

            const fotos = await FotoModel.findAllByAmbienteId(id_ambiente, id_ocorrencia);
            return fotos;
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] listarFotosPorAmbiente:', error);
            throw error;
        }
    }

    async listarFotosPorOcorrencia(id_ocorrencia) {
        try {
            const fotos = await FotoModel.findAllByOcorrenciaId(id_ocorrencia);
            return fotos;
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] listarFotosPorOcorrencia:', error);
            throw new Error('Erro ao listar fotos por ocorrência.');
        }
    }

    async validarDadosAdicaoFoto(dadosFoto, files) {
        const { id_ocorrencia, id_ambiente } = dadosFoto;

        if (!files || files.length === 0) {
            throw new Error('Pelo menos um arquivo de imagem é obrigatório.');
        }

        const ambiente = await AmbienteModel.findById(id_ambiente);
        if (!ambiente) {
            throw new Error(`Ambiente com ID ${id_ambiente} não encontrado.`);
        }

        return {
            id_ambiente: parseInt(id_ambiente),
            id_ocorrencia: id_ocorrencia ? parseInt(id_ocorrencia) : null,
            files
        };
    }

    async uploadArquivo(file, id_ambiente, id_ocorrencia) {
        try {
            const nomeUnico = `${Date.now()}-${file.originalname}`;
            
            const { error: uploadError } = await supabase.storage
                .from('images')
                .upload(nomeUnico, file.buffer, { contentType: file.mimetype });

            if (uploadError) {
                throw new Error(`Falha no upload para o Supabase: ${uploadError.message}`);
            }

            const { data: publicUrlData } = supabase.storage
                .from('images')
                .getPublicUrl(nomeUnico);

            return await FotoModel.create({
                id_ambiente: parseInt(id_ambiente),
                id_ocorrencia: id_ocorrencia ? parseInt(id_ocorrencia) : null,
                url: publicUrlData.publicUrl
            });
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] uploadArquivo:', error);
            throw error;
        }
    }

    async adicionarFotoAmbienteOcorrencia(dadosFoto, files) {
        try {
            
            const dadosValidados = await this.validarDadosAdicaoFoto(dadosFoto, files);

            const promessasDeUpload = dadosValidados.files.map(file => 
                this.uploadArquivo(file, dadosValidados.id_ambiente, dadosValidados.id_ocorrencia)
            );

            const fotosAdicionadas = await Promise.all(promessasDeUpload);
            return fotosAdicionadas;
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] adicionarFotoAmbienteOcorrencia:', error);
            throw error;
        }
    }

    async obterMetadadosFotoPorId(id_foto) {
        try {
            const foto = await FotoModel.findById(id_foto);
            if (!foto) {
                throw new Error('Foto não encontrada.');
            }
            return foto;
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] obterMetadadosFotoPorId:', error);
            throw error;
        }
    }

    async deletarFoto(id_foto) {
        try {
            
            const fotoDeletadaDoBD = await FotoModel.remove(id_foto);
            if (!fotoDeletadaDoBD) {
                throw new Error('Foto não encontrada para deleção.');
            }

            const nomeArquivo = fotoDeletadaDoBD.url.split('/').pop();

            const { error: deleteError } = await supabase.storage
                .from('images')
                .remove([nomeArquivo]);

            if (deleteError) {
                console.warn(`Registro da foto ${id_foto} deletado do BD, mas falha ao remover o arquivo do Supabase: ${deleteError.message}`);
                throw new Error('Erro ao deletar arquivo no storage, mas registro do BD removido.');
            }

            return true;
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] deletarFoto:', error);
            throw error;
        }
    }

    async validarFotoExiste(id_foto) {
        try {
            const foto = await FotoModel.findById(id_foto);
            if (!foto) {
                throw new Error('Foto não encontrada.');
            }
            return foto;
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] validarFotoExiste:', error);
            throw error;
        }
    }

    async obterEstatisticasPorAmbiente(id_ambiente) {
        try {
            const fotos = await this.listarFotosPorAmbiente(id_ambiente);
            
            const estatisticas = {
                total: fotos.length,
                comOcorrencia: fotos.filter(foto => foto.id_ocorrencia).length,
                semOcorrencia: fotos.filter(foto => !foto.id_ocorrencia).length
            };

            return estatisticas;
        } catch (error) {
            console.error('[FOTOS_SERVICE_ERROR] obterEstatisticasPorAmbiente:', error);
            throw error;
        }
    }
}

module.exports = new FotosService();