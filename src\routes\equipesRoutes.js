const express = require("express");
const router = express.Router();
const equipeController = require("../controllers/equipesController");

router.get("/", equipeController.listarEquipes);
router.post("/", equipeController.criarEquipe);

router.get("/:id_equipe", equipeController.obterEquipeEspecificaComUsuarios);
router.put("/:id_equipe", equipeController.atualizarEquipe);
router.delete("/:id_equipe", equipeController.deletarEquipe);
router.post("/:id_equipe/usuarios", equipeController.adicionarUsuarioNaEquipe);
router.delete(
  "/:id_equipe/usuarios/:id_usuario",
  equipeController.removerUsuarioDaEquipe
);

module.exports = router;