const iaService = require('../services/iaService');

describe('IAService', () => {
    beforeEach(() => {
        
        delete process.env.OPENAI_API_KEY;
        delete process.env.ANTHROPIC_API_KEY;
        delete process.env.GEMINI_API_KEY;
        delete process.env.IA_PROVIDER;
    });

    describe('isConfigurado', () => {
        it('deve retornar false quando nenhuma API key estiver configurada', () => {
            expect(iaService.isConfigurado()).toBe(false);
        });

        it('deve retornar true quando OpenAI estiver configurada', () => {
            process.env.IA_PROVIDER = 'openai';
            process.env.OPENAI_API_KEY = 'test-key';
            expect(iaService.isConfigurado()).toBe(true);
        });

        it('deve retornar true quando Anthropic estiver configurada', () => {
            process.env.IA_PROVIDER = 'anthropic';
            process.env.ANTHROPIC_API_KEY = 'test-key';
            expect(iaService.isConfigurado()).toBe(true);
        });

        it('deve retornar true quando Gemini estiver configurada', () => {
            process.env.IA_PROVIDER = 'gemini';
            process.env.GEMINI_API_KEY = 'test-key';
            expect(iaService.isConfigurado()).toBe(true);
        });
    });

    describe('construirPromptRelatorio', () => {
        it('deve construir prompt com dados da inspeção', () => {
            const dadosInspecao = {
                inspecao: {
                    nome: 'Teste Inspeção',
                    tipo_edificacao: 'Residencial',
                    criado_em: '2024-01-01',
                    status: 'Concluída'
                },
                endereco: {
                    cep: '01234-567',
                    numero: '123',
                    complemento: 'Apto 45'
                },
                equipe: {
                    nome: 'Equipe Alpha',
                    membros: [{ nome: 'João' }, { nome: 'Maria' }]
                },
                ambientes: [
                    { titulo: 'Sala' },
                    { titulo: 'Cozinha' }
                ],
                ocorrencias: [
                    { titulo: 'Rachadura', descricao: 'Rachadura na parede' }
                ]
            };

            const prompt = iaService.construirPromptRelatorio(dadosInspecao);
            
            expect(prompt).toContain('Teste Inspeção');
            expect(prompt).toContain('Residencial');
            expect(prompt).toContain('01234-567');
            expect(prompt).toContain('Equipe Alpha');
            expect(prompt).toContain('Sala');
            expect(prompt).toContain('Rachadura');
        });
    });

    describe('processarRespostaIA', () => {
        it('deve processar resposta JSON válida', () => {
            const respostaJSON = JSON.stringify({
                titulo: 'Relatório Teste',
                resumoExecutivo: 'Resumo teste',
                introducao: 'Introdução teste'
            });

            const resultado = iaService.processarRespostaIA(respostaJSON);
            
            expect(resultado.titulo).toBe('Relatório Teste');
            expect(resultado.resumoExecutivo).toBe('Resumo teste');
            expect(resultado.introducao).toBe('Introdução teste');
        });

        it('deve extrair JSON de resposta com texto adicional', () => {
            const respostaComTexto = `
                Aqui está o relatório solicitado:
                
                {"titulo": "Relatório Teste", "resumoExecutivo": "Resumo teste"}
                
                Espero que seja útil!
            `;

            const resultado = iaService.processarRespostaIA(respostaComTexto);
            
            expect(resultado.titulo).toBe('Relatório Teste');
            expect(resultado.resumoExecutivo).toBe('Resumo teste');
        });

        it('deve criar fallback quando JSON for inválido', () => {
            const respostaInvalida = 'Esta é uma resposta sem JSON válido';

            const resultado = iaService.processarRespostaIA(respostaInvalida);
            
            expect(resultado.titulo).toBe('Relatório de Inspeção Predial');
            expect(resultado.resumoExecutivo).toContain('Esta é uma resposta sem JSON válido');
            expect(resultado.recomendacoes).toBeInstanceOf(Array);
        });
    });

    describe('criarRelatorioFallback', () => {
        it('deve criar estrutura de relatório padrão', () => {
            const textoIA = 'Texto de exemplo da IA';
            
            const resultado = iaService.criarRelatorioFallback(textoIA);
            
            expect(resultado.titulo).toBe('Relatório de Inspeção Predial');
            expect(resultado.resumoExecutivo).toContain('Texto de exemplo da IA');
            expect(resultado.introducao).toBeTruthy();
            expect(resultado.metodologia).toBeTruthy();
            expect(resultado.resultados).toBeTruthy();
            expect(resultado.recomendacoes).toBeInstanceOf(Array);
            expect(resultado.conclusao).toBeTruthy();
            expect(resultado.observacoes).toBeTruthy();
        });
    });

    describe('extrairRespostaDaIA', () => {
        it('deve extrair resposta do OpenAI', () => {
            const dataOpenAI = {
                choices: [
                    {
                        message: {
                            content: 'Resposta do OpenAI'
                        }
                    }
                ]
            };

            iaService.currentProvider = 'openai';
            
            const resultado = iaService.extrairRespostaDaIA(dataOpenAI);
            expect(resultado).toBe('Resposta do OpenAI');
        });

        it('deve extrair resposta do Anthropic', () => {
            const dataAnthropic = {
                content: [
                    {
                        text: 'Resposta do Anthropic'
                    }
                ]
            };

            iaService.currentProvider = 'anthropic';
            
            const resultado = iaService.extrairRespostaDaIA(dataAnthropic);
            expect(resultado).toBe('Resposta do Anthropic');
        });

        it('deve extrair resposta do Gemini', () => {
            const dataGemini = {
                candidates: [
                    {
                        content: {
                            parts: [
                                {
                                    text: 'Resposta do Gemini'
                                }
                            ]
                        }
                    }
                ]
            };

            iaService.currentProvider = 'gemini';
            
            const resultado = iaService.extrairRespostaDaIA(dataGemini);
            expect(resultado).toBe('Resposta do Gemini');
        });
    });

    describe('gerarResumoOcorrencias', () => {
        it('deve retornar mensagem padrão quando não há ocorrências', async () => {
            const resultado = await iaService.gerarResumoOcorrencias([]);
            expect(resultado).toBe('Nenhuma ocorrência registrada durante a inspeção.');
        });

        it('deve retornar mensagem padrão quando ocorrências é null', async () => {
            const resultado = await iaService.gerarResumoOcorrencias(null);
            expect(resultado).toBe('Nenhuma ocorrência registrada durante a inspeção.');
        });
    });
});