const db = require('../config/db');

const Historico = {
  async create({ id_inspecao, status_id }) {
    const query = `
      INSERT INTO historicos (id_inspecao, status_id, adicionado_em)
      VALUES ($1, $2, NOW())
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [id_inspecao, status_id]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao criar histórico:", error);
      throw error;
    }
  },

  async findByInspecaoId(id_inspecao) {
    const query = `
        SELECT h.id, h.status_id, s.nome as status_nome, s.cor as status_cor, h.adicionado_em
        FROM historicos h
        JOIN status s ON h.status_id = s.nome
        WHERE h.id_inspecao = $1
        ORDER BY h.adicionado_em DESC;
    `;
    try {
      const { rows } = await db.query(query, [id_inspecao]);
      return rows;
    } catch (error) {
      console.error("Erro ao buscar histórico por ID da inspeção:", error);
      throw error;
    }
  },
};

module.exports = Historico;