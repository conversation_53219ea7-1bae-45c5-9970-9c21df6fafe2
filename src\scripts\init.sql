

CREATE TABLE IF NOT EXISTS cargos (
    nome VARCHAR(50) CHECK (nome IN ('COORDENADOR', 'MEMBRO','ADMINISTRADOR')) PRIMARY KEY NOT NULL
);

CREATE TABLE IF NOT EXISTS usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    senha VARCHAR(100) NOT NULL,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    permissao VARCHAR(30)
);

CREATE TABLE IF NOT EXISTS projetos (
    id SERIAL PRIMARY KEY,
    codigo_projeto VARCHAR(50) UNIQUE NOT NULL,
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS equipes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL
);

CREATE TABLE IF NOT EXISTS tipo_edificacao (
    nome VARCHAR(100) PRIMARY KEY NOT NULL
);

CREATE TABLE IF NOT EXISTS enderecos (
    id SERIAL PRIMARY KEY,
    cep VARCHAR(8) NOT NULL,
    numero VARCHAR(20) NOT NULL,
    complemento VARCHAR(50),
    referencia VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS status (
    nome VARCHAR(20) PRIMARY KEY NOT NULL,
    cor VARCHAR(20)
);

CREATE TABLE IF NOT EXISTS afazeres (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL
);

CREATE TABLE IF NOT EXISTS tipo_sistema (
    nome VARCHAR(30) PRIMARY KEY NOT NULL
);



CREATE TABLE IF NOT EXISTS usuarios_cargos_equipes (
    id SERIAL PRIMARY KEY,
    id_cargo VARCHAR(50) REFERENCES cargos(nome),
    id_usuario INTEGER REFERENCES usuarios(id),
    id_equipe INTEGER REFERENCES equipes(id),
    UNIQUE (id_usuario, id_equipe, id_cargo) 
);

CREATE TABLE IF NOT EXISTS inspecoes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    url_planta VARCHAR(200),
    id_tipo_edificacao VARCHAR(100) REFERENCES tipo_edificacao(nome),
    id_equipe INTEGER REFERENCES equipes(id),
    id_endereco INTEGER REFERENCES enderecos(id),
    id_projeto INTEGER REFERENCES projetos(id)
);

CREATE TABLE IF NOT EXISTS tipos_patologia (
    nome VARCHAR(100) PRIMARY KEY NOT NULL,
    id_tipo_sistema VARCHAR(30) REFERENCES tipo_sistema(nome) 
);



CREATE TABLE IF NOT EXISTS historicos (
    id SERIAL PRIMARY KEY,
    status_id VARCHAR(20) REFERENCES status(nome),
    id_inspecao INTEGER REFERENCES inspecoes(id),
    adicionado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ambientes (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL,
    id_usuario INTEGER REFERENCES usuarios(id), 
    id_inspecao INTEGER REFERENCES inspecoes(id),
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    id_ambiente_pai INTEGER REFERENCES ambientes(id) ON DELETE SET NULL, 
    observacoes VARCHAR(150)
);


CREATE TABLE IF NOT EXISTS ocorrencias (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(100) NOT NULL,
    id_inspecao INTEGER REFERENCES inspecoes(id),
    id_ambiente INTEGER REFERENCES ambientes(id), 
    id_tipo_sistema VARCHAR(30) REFERENCES tipo_sistema(nome), 
    id_tipo_patologia VARCHAR(100) REFERENCES tipos_patologia(nome),
    descricao VARCHAR(500),
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    concluido_em TIMESTAMP
);

CREATE TABLE IF NOT EXISTS relatorios (
    id SERIAL PRIMARY KEY, 
    url VARCHAR(200) NOT NULL,
    id_inspecao INTEGER REFERENCES inspecoes(id) 
);



CREATE TABLE IF NOT EXISTS ambientes_afazeres (
    id_afazer INTEGER REFERENCES afazeres(id),
    id_ambiente INTEGER REFERENCES ambientes(id),
    valor BOOLEAN NOT NULL,
    PRIMARY KEY (id_afazer, id_ambiente) 
);

CREATE TABLE IF NOT EXISTS fotos (
    id SERIAL PRIMARY KEY,
    id_ocorrencia INTEGER REFERENCES ocorrencias(id),
    id_ambiente INTEGER REFERENCES ambientes(id),
    url VARCHAR(255) NOT NULL
);

-- Inserir dados iniciais na tabela status
INSERT INTO status (nome, cor) VALUES
    ('Aberta', '#6B7280'),
    ('Em Andamento', '#F59E0B'),
    ('Concluída', '#10B981')
ON CONFLICT (nome) DO NOTHING;

-- Remover status cancelada se existir
DELETE FROM status WHERE nome = 'Cancelada';