const equipesService = require('../services/equipesService');

exports.listarEquipes = async (req, res, next) => {
    try {
        const equipes = await equipesService.listarEquipes();
        res.status(200).json(equipes);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarEquipes:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.criarEquipe = async (req, res, next) => {
    try {
        const novaEquipe = await equipesService.criarEquipe(req.body);
        res.status(201).json(novaEquipe);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarEquipe:', error);
        const status = error.message.includes('obrigatório') || error.message.includes('vazio') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterEquipeEspecificaComUsuarios = async (req, res, next) => {
    try {
        const { id_equipe } = req.params;
        const equipe = await equipesService.obterEquipePorId(id_equipe);
        res.status(200).json(equipe);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterEquipeEspecificaComUsuarios:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.atualizarEquipe = async (req, res, next) => {
    try {
        const { id_equipe } = req.params;
        const equipeAtualizada = await equipesService.atualizarEquipe(id_equipe, req.body);
        res.status(200).json(equipeAtualizada);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] atualizarEquipe:', error);
        const status = error.message.includes('não encontrada') ? 404 :
                      error.message.includes('obrigatório') || error.message.includes('vazio') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.deletarEquipe = async (req, res, next) => {
    try {
        const { id_equipe } = req.params;
        await equipesService.deletarEquipe(id_equipe);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarEquipe:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.adicionarUsuarioNaEquipe = async (req, res, next) => {
    try {
        const { id_equipe } = req.params;
        const resultado = await equipesService.adicionarUsuarioNaEquipe(id_equipe, req.body);
        res.status(201).json(resultado);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] adicionarUsuarioNaEquipe:', error);
        const status = error.message.includes('não encontrada') || error.message.includes('não encontrado') ? 404 :
                      error.message.includes('obrigatórios') || error.message.includes('vazio') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.removerUsuarioDaEquipe = async (req, res, next) => {
    try {
        const { id_equipe, id_usuario } = req.params;
        await equipesService.removerUsuarioDaEquipe(id_equipe, id_usuario);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] removerUsuarioDaEquipe:', error);
        const status = error.message.includes('não encontrada') || error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};