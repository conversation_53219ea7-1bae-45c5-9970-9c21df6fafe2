const TipoSistemaModel = require('../models/tipoSistemaModel');
const TipoPatologiaModel = require('../models/tipoPatologiaModel');
const AfazerModel = require('../models/afazeresModel');
const TipoEdificacaoModel = require('../models/tipoEdificacaoModel');

class LookupService {
    
    async listarTiposSistema() {
        try {
            return await TipoSistemaModel.findAll();
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] listarTiposSistema:', error);
            throw new Error('Erro ao listar tipos de sistema.');
        }
    }

    async listarTiposPatologia() {
        try {
            return await TipoPatologiaModel.findAll();
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] listarTiposPatologia:', error);
            throw new Error('Erro ao listar tipos de patologia.');
        }
    }

    async listarAfazeres() {
        try {
            return await AfazerModel.findAll();
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] listarAfazeres:', error);
            throw new Error('Erro ao listar afazeres.');
        }
    }

    async listarTiposEdificacao() {
        try {
            return await TipoEdificacaoModel.findAll();
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] listarTiposEdificacao:', error);
            throw new Error('Erro ao listar tipos de edificação.');
        }
    }

    async obterTipoSistemaPorId(id) {
        try {
            const tipoSistema = await TipoSistemaModel.findById(id);
            if (!tipoSistema) {
                throw new Error('Tipo de sistema não encontrado.');
            }
            return tipoSistema;
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] obterTipoSistemaPorId:', error);
            throw error;
        }
    }

    async obterTipoPatologiaPorId(id) {
        try {
            const tipoPatologia = await TipoPatologiaModel.findById(id);
            if (!tipoPatologia) {
                throw new Error('Tipo de patologia não encontrado.');
            }
            return tipoPatologia;
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] obterTipoPatologiaPorId:', error);
            throw error;
        }
    }

    async obterTipoEdificacaoPorId(id) {
        try {
            const tipoEdificacao = await TipoEdificacaoModel.findById(id);
            if (!tipoEdificacao) {
                throw new Error('Tipo de edificação não encontrado.');
            }
            return tipoEdificacao;
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] obterTipoEdificacaoPorId:', error);
            throw error;
        }
    }

    async obterAfazerPorId(id) {
        try {
            const afazer = await AfazerModel.findById(id);
            if (!afazer) {
                throw new Error('Afazer não encontrado.');
            }
            return afazer;
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] obterAfazerPorId:', error);
            throw error;
        }
    }

    async obterTodosOsDados() {
        try {
            const [tiposSistema, tiposPatologia, afazeres, tiposEdificacao] = await Promise.all([
                this.listarTiposSistema(),
                this.listarTiposPatologia(),
                this.listarAfazeres(),
                this.listarTiposEdificacao()
            ]);

            return {
                tiposSistema,
                tiposPatologia,
                afazeres,
                tiposEdificacao
            };
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] obterTodosOsDados:', error);
            throw new Error('Erro ao obter dados de lookup.');
        }
    }

    async validarTipoSistemaExiste(id) {
        try {
            await this.obterTipoSistemaPorId(id);
            return true;
        } catch (error) {
            return false;
        }
    }

    async validarTipoPatologiaExiste(id) {
        try {
            await this.obterTipoPatologiaPorId(id);
            return true;
        } catch (error) {
            return false;
        }
    }

    async validarTipoEdificacaoExiste(id) {
        try {
            await this.obterTipoEdificacaoPorId(id);
            return true;
        } catch (error) {
            return false;
        }
    }

    async buscarTiposSistemaPorNome(nome) {
        try {
            const todosTipos = await this.listarTiposSistema();
            return todosTipos.filter(tipo =>
                tipo.nome.toLowerCase().includes(nome.toLowerCase())
            );
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] buscarTiposSistemaPorNome:', error);
            throw error;
        }
    }

    async criarTipoSistema(dados) {
        try {
            // Verificar se já existe
            const tipoExistente = await TipoSistemaModel.findByNome(dados.nome);
            if (tipoExistente) {
                throw new Error(`Tipo de sistema '${dados.nome}' já existe.`);
            }

            const novoTipo = await TipoSistemaModel.create(dados);
            return novoTipo;
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] criarTipoSistema:', error);
            throw error;
        }
    }

    async criarTipoPatologia(dados) {
        try {
            // Verificar se já existe
            const tipoExistente = await TipoPatologiaModel.findByNome(dados.nome);
            if (tipoExistente) {
                throw new Error(`Tipo de patologia '${dados.nome}' já existe.`);
            }

            const novoTipo = await TipoPatologiaModel.create(dados);
            return novoTipo;
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] criarTipoPatologia:', error);
            throw error;
        }
    }

    async buscarTiposPatologiaPorNome(nome) {
        try {
            const todosTipos = await this.listarTiposPatologia();
            return todosTipos.filter(tipo =>
                tipo.nome.toLowerCase().includes(nome.toLowerCase())
            );
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] buscarTiposPatologiaPorNome:', error);
            throw error;
        }
    }

    async criarAfazer(dados) {
        try {
            // Verificar se já existe um afazer com o mesmo título
            const afazeres = await this.listarAfazeres();
            const afazerExistente = afazeres.find(afazer =>
                afazer.titulo.toLowerCase() === dados.titulo.toLowerCase()
            );

            if (afazerExistente) {
                throw new Error(`Afazer '${dados.titulo}' já existe.`);
            }

            const novoAfazer = await AfazerModel.create(dados);
            return novoAfazer;
        } catch (error) {
            console.error('[LOOKUP_SERVICE_ERROR] criarAfazer:', error);
            throw error;
        }
    }
}

module.exports = new LookupService();