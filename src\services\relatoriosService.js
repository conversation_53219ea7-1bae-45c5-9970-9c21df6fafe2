const RelatorioModel = require('../models/relatoriosModel');
const InspecaoModel = require('../models/inspecoesModel');
const AmbienteModel = require('../models/ambientesModel');
const OcorrenciaModel = require('../models/ocorrenciasModel');
const EquipeModel = require('../models/equipesModel');
const EnderecoModel = require('../models/enderecosModel');
const FotoModel = require('../models/fotosModel');
const TipoPatologiaModel = require('../models/tipoPatologiaModel');
const TipoSistemaModel = require('../models/tipoSistemaModel');
const iaService = require('./iaService');
const supabaseStorageService = require('./supabaseStorageService');
const supabase = require('../config/supabaseClient');

class RelatoriosService {
    
    async listarRelatoriosPorInspecao(id_inspecao) {
        try {
            
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
            }

            return await RelatorioModel.findAllByInspecaoId(id_inspecao);
        } catch (error) {
            console.error('[SERVICE_ERROR] listarRelatoriosPorInspecao:', error);
            throw error;
        }
    }

    async adicionarRelatorioNaInspecao(id_inspecao, dadosRelatorio) {
        const { url } = dadosRelatorio;

        if (!url) {
            throw new Error('URL do relatório é obrigatória.');
        }

        if (!this.validarUrl(url)) {
            throw new Error('URL do relatório inválida.');
        }

        try {
            
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
            }

            return await RelatorioModel.create({
                id_inspecao: parseInt(id_inspecao),
                url
            });
        } catch (error) {
            console.error('[SERVICE_ERROR] adicionarRelatorioNaInspecao:', error);
            throw error;
        }
    }

    async listarRelatorios() {
        try {
            return await RelatorioModel.findAll();
        } catch (error) {
            console.error('[SERVICE_ERROR] listarRelatorios:', error);
            throw new Error('Erro ao listar relatórios.');
        }
    }

    async obterRelatorioPorId(id_relatorio) {
        try {
            const relatorio = await RelatorioModel.findById(id_relatorio);
            if (!relatorio) {
                throw new Error('Relatório não encontrado.');
            }
            return relatorio;
        } catch (error) {
            console.error('[SERVICE_ERROR] obterRelatorioPorId:', error);
            throw error;
        }
    }

    async gerarPreviewRelatorio(id_relatorio) {
        try {
            
            const relatorio = await this.obterRelatorioPorId(id_relatorio);

            const dadosCompletos = await this.buscarDadosCompletosInspecao(relatorio.id_inspecao);

            let conteudoIA = null;
            if (iaService.isConfigurado()) {
                try {
                    conteudoIA = await iaService.gerarConteudoRelatorio(dadosCompletos);
                } catch (error) {
                    console.warn('[SERVICE_WARN] Erro ao gerar conteúdo com IA:', error.message);
                }
            }

            return {
                relatorio: {
                    id: relatorio.id,
                    url: relatorio.url,
                    titulo: `Relatório de Inspeção - ${dadosCompletos.inspecao.nome}`
                },
                inspecao: dadosCompletos.inspecao,
                endereco: dadosCompletos.endereco,
                equipe: dadosCompletos.equipe,
                projeto: dadosCompletos.projeto,
                ambientes: dadosCompletos.ambientesDetalhados,
                ocorrencias: dadosCompletos.ocorrenciasDetalhadas,
                fotos: dadosCompletos.fotos,
                estatisticas: this.calcularEstatisticas(dadosCompletos),
                conteudoIA: conteudoIA,
                dataGeracao: new Date().toISOString()
            };
        } catch (error) {
            console.error('[SERVICE_ERROR] gerarPreviewRelatorio:', error);
            throw error;
        }
    }

    async buscarDadosCompletosInspecao(id_inspecao) {
        try {
            
            const [inspecao, ambientes, ocorrencias, equipe, endereco, fotos, projeto] = await Promise.all([
                InspecaoModel.findById(id_inspecao),
                AmbienteModel.findAllByInspecaoId(id_inspecao),
                OcorrenciaModel.findAllByInspecaoId ? OcorrenciaModel.findAllByInspecaoId(id_inspecao) : [],
                this.buscarEquipeInspecao(id_inspecao),
                this.buscarEnderecoInspecao(id_inspecao),
                FotoModel.findAllByInspecaoId ? FotoModel.findAllByInspecaoId(id_inspecao) : [],
                this.buscarProjetoInspecao(id_inspecao)
            ]);

            const ambientesDetalhados = await this.buscarAmbientesDetalhados(ambientes || []);

            const ocorrenciasDetalhadas = await this.buscarOcorrenciasDetalhadas(ocorrencias || []);

            const fotosOrganizadas = await this.organizarFotos(id_inspecao, ambientes || [], ocorrencias || []);

            return {
                inspecao,
                ambientes: ambientes || [],
                ocorrencias: ocorrencias || [],
                ambientesDetalhados,
                ocorrenciasDetalhadas,
                fotos: fotosOrganizadas,
                equipe,
                endereco,
                projeto
            };
        } catch (error) {
            console.error('[SERVICE_ERROR] buscarDadosCompletosInspecao:', error);
            throw new Error('Erro ao buscar dados completos da inspeção.');
        }
    }

    async buscarEquipeInspecao(id_inspecao) {
        try {
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (inspecao && inspecao.id_equipe) {
                return await EquipeModel.findById(inspecao.id_equipe);
            }
            return null;
        } catch (error) {
            console.error('[SERVICE_ERROR] buscarEquipeInspecao:', error);
            return null;
        }
    }

    async buscarEnderecoInspecao(id_inspecao) {
        try {
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (inspecao && inspecao.id_endereco) {
                return await EnderecoModel.findById(inspecao.id_endereco);
            }
            return null;
        } catch (error) {
            console.error('[SERVICE_ERROR] buscarEnderecoInspecao:', error);
            return null;
        }
    }

    async buscarAmbientesDetalhados(ambientes) {
        try {
            const ambientesDetalhados = await Promise.all(
                ambientes.map(async (ambiente) => {
                    
                    const fotosAmbiente = await FotoModel.findAllByAmbienteId(ambiente.id);

                    const fotosComUrlsAssinadas = await Promise.all(
                        (fotosAmbiente || []).map(async (foto) => {
                            try {
                                const urlData = await this.gerarUrlAssinada(foto.id);
                                return {
                                    ...foto,
                                    url: urlData.signedUrl || foto.url,
                                    originalUrl: foto.url
                                };
                            } catch (error) {
                                console.warn(`Erro ao gerar URL assinada para foto ${foto.id}:`, error.message);
                                return foto;
                            }
                        })
                    );

                    const ocorrenciasAmbiente = await OcorrenciaModel.findAllByAmbienteId(ambiente.id);

                    const ambientesFilhos = await AmbienteModel.findAllByAmbientePai ?
                        await AmbienteModel.findAllByAmbientePai(ambiente.id) : [];

                    return {
                        ...ambiente,
                        fotos: fotosComUrlsAssinadas,
                        ocorrencias: ocorrenciasAmbiente || [],
                        ambientesFilhos: ambientesFilhos || [],
                        totalOcorrencias: (ocorrenciasAmbiente || []).length,
                        totalFotos: fotosComUrlsAssinadas.length
                    };
                })
            );

            return ambientesDetalhados;
        } catch (error) {
            console.error('[SERVICE_ERROR] buscarAmbientesDetalhados:', error);
            return ambientes.map(ambiente => ({ ...ambiente, fotos: [], ocorrencias: [], ambientesFilhos: [] }));
        }
    }

    async buscarOcorrenciasDetalhadas(ocorrencias) {
        try {
            const ocorrenciasDetalhadas = await Promise.all(
                ocorrencias.map(async (ocorrencia) => {
                    
                    let fotosOcorrencia = [];
                    try {
                        fotosOcorrencia = await FotoModel.findAllByOcorrenciaId(ocorrencia.id);

                        fotosOcorrencia = await Promise.all(
                            (fotosOcorrencia || []).map(async (foto) => {
                                try {
                                    const urlData = await this.gerarUrlAssinada(foto.id);
                                    return {
                                        ...foto,
                                        url: urlData.signedUrl || foto.url,
                                        originalUrl: foto.url
                                    };
                                } catch (error) {
                                    console.warn(`Erro ao gerar URL assinada para foto ${foto.id}:`, error.message);
                                    return foto;
                                }
                            })
                        );
                    } catch (error) {
                        console.warn('Erro ao buscar fotos da ocorrência:', ocorrencia.id);
                    }

                    const tipoPatologia = ocorrencia.id_tipo_patologia ?
                        { nome: ocorrencia.id_tipo_patologia } : null;

                    const tipoSistema = ocorrencia.id_tipo_sistema ?
                        { nome: ocorrencia.id_tipo_sistema } : null;

                    return {
                        ...ocorrencia,
                        fotos: fotosOcorrencia,
                        tipoPatologia: tipoPatologia,
                        tipoSistema: tipoSistema,
                        totalFotos: fotosOcorrencia.length,
                        dataFormatada: ocorrencia.dataocm ?
                            new Date(ocorrencia.dataocm).toLocaleDateString('pt-BR') : 'Não informada'
                    };
                })
            );

            return ocorrenciasDetalhadas;
        } catch (error) {
            console.error('[SERVICE_ERROR] buscarOcorrenciasDetalhadas:', error);
            return ocorrencias.map(ocorrencia => ({
                ...ocorrencia,
                fotos: [],
                tipoPatologia: null,
                tipoSistema: null,
                totalFotos: 0,
                dataFormatada: 'Não informada'
            }));
        }
    }

    async organizarFotos(id_inspecao, ambientes, ocorrencias) {
        try {
            
            const fotosOrganizadas = {
                inspecao: [],
                total: 0
            };

            let totalFotos = 0;
            for (const ambiente of ambientes || []) {
                try {
                    const fotosAmbiente = await FotoModel.findAllByAmbienteId(ambiente.id);
                    totalFotos += (fotosAmbiente || []).length;
                } catch (error) {
                    console.warn('Erro ao buscar fotos do ambiente:', ambiente.id);
                }
            }

            fotosOrganizadas.total = totalFotos;
            return fotosOrganizadas;
        } catch (error) {
            console.error('[SERVICE_ERROR] organizarFotos:', error);
            return {
                inspecao: [],
                total: 0
            };
        }
    }

    calcularEstatisticas(dadosCompletos) {
        const { ambientes, ocorrencias, ocorrenciasDetalhadas } = dadosCompletos;

        const ocorrenciasPorTipo = {};
        const ocorrenciasPorSistema = {};

        (ocorrenciasDetalhadas || ocorrencias || []).forEach(ocr => {
            
            const tipoPatologia = ocr.tipoPatologia?.nome || 'Não classificado';
            ocorrenciasPorTipo[tipoPatologia] = (ocorrenciasPorTipo[tipoPatologia] || 0) + 1;

            const tipoSistema = ocr.tipoSistema?.nome || 'Não classificado';
            ocorrenciasPorSistema[tipoSistema] = (ocorrenciasPorSistema[tipoSistema] || 0) + 1;
        });

        const ambientesComOcorrencias = new Set((ocorrencias || []).map(ocr => ocr.id_ambiente)).size;

        return {
            
            totalAmbientes: (ambientes || []).length,
            totalOcorrencias: (ocorrencias || []).length,

            ambientesComOcorrencias,
            ambientesSemOcorrencias: (ambientes || []).length - ambientesComOcorrencias,
            percentualAmbientesAfetados: (ambientes || []).length > 0
                ? Math.round((ambientesComOcorrencias / (ambientes || []).length) * 100)
                : 0,

            ocorrenciasPorTipo,
            ocorrenciasPorSistema
        };
    }

    async atualizarRelatorio(id_relatorio, dadosAtualizacao) {
        const { url } = dadosAtualizacao;

        if (url && !this.validarUrl(url)) {
            throw new Error('URL do relatório inválida.');
        }

        try {
            const relatorioAtualizado = await RelatorioModel.update(id_relatorio, {
                url
            });

            if (!relatorioAtualizado) {
                throw new Error('Relatório não encontrado para atualização.');
            }

            return relatorioAtualizado;
        } catch (error) {
            console.error('[SERVICE_ERROR] atualizarRelatorio:', error);
            throw error;
        }
    }

    async deletarRelatorio(id_relatorio) {
        try {
            const deletado = await RelatorioModel.remove(id_relatorio);
            if (!deletado) {
                throw new Error('Relatório não encontrado para deleção.');
            }
            return true;
        } catch (error) {
            console.error('[SERVICE_ERROR] deletarRelatorio:', error);
            throw error;
        }
    }

    validarUrl(url) {
        if (!url || typeof url !== 'string') {
            return false;
        }

        try {
            new URL(url);
            return true;
        } catch (error) {
            
            return url.trim().length > 0 && !url.includes('<script>');
        }
    }

    validarTitulo(titulo) {
        return titulo && titulo.trim().length >= 3;
    }

    async gerarUrlAssinada(fotoId) {
        try {
            const foto = await FotoModel.findById(fotoId);

            if (!foto) {
                throw new Error('Foto não encontrada');
            }

            if (foto.url.includes('/object/public/')) {
                return { signedUrl: foto.url };
            }

            const nomeArquivo = foto.url.split('/').pop();
            const expiresIn = 7200; 

            const { data, error } = await supabase.storage
                .from('images')
                .createSignedUrl(nomeArquivo, expiresIn);

            if (error) {
                console.warn(`Erro ao gerar URL assinada para foto ${fotoId}:`, error.message);
                return { signedUrl: foto.url }; 
            }

            return { signedUrl: data.signedUrl };

        } catch (error) {

            try {
                const foto = await FotoModel.findById(fotoId);
                return { signedUrl: foto?.url || null };
            } catch {
                return { signedUrl: null };
            }
        }
    }

    async buscarProjetoInspecao(id_inspecao) {
        try {
            const inspecao = await InspecaoModel.findById(id_inspecao);

            if (!inspecao || !inspecao.id_projeto) {
                return null;
            }

            const ProjetoModel = require('../models/projetosModel');
            const projeto = await ProjetoModel.findById(inspecao.id_projeto);

            return projeto;
        } catch (error) {
            return null;
        }
    }
}

module.exports = new RelatoriosService();