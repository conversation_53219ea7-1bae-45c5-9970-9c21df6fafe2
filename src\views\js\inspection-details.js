const appState = {
  currentInspectionId: null,
  ambientes: [],
  filteredAmbientes: [],
  bannerPhotosMeta: [],
  signedUrls: {},
  ocorrenciasCounts: {},
  currentFilter: 'all',
  searchTerm: ''
};

let inspectionStatus = "Aberta";

function getInspectionIdFromUrl() {
  const pathParts = window.location.pathname.split("/");
  const inspectionIndex = pathParts.indexOf("inspection-details");
  if (inspectionIndex !== -1 && pathParts[inspectionIndex + 1]) {
    return pathParts[inspectionIndex + 1];
  }
  return null;
}

async function apiRequest(endpoint, options = {}) {
  const token = localStorage.getItem("authToken");
  if (!token) {
    window.location.href = "/";
    throw new Error("Token de autenticação não encontrado.");
  }
  try {
    const response = await fetch(endpoint, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        ...options.headers,
      },
      ...options,
    });
    if (response.status === 401) {
      localStorage.removeItem("authToken");
      window.location.href = "/";
      throw new Error("Sessão expirada. Por favor, faca login novamente.");
    }
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
}

async function loadInspectionData(inspectionId) {
  try {
    const inspection = await apiRequest(`/api/inspecoes/${inspectionId}`);
    document.getElementById("inspection-name").textContent =
      inspection.nome || "Inspeção sem nome";
    inspectionStatus = inspection.analise || "Aberta";
    updateInspectionActions();

    if (inspection.id_endereco) {
      try {
        const endereco = await apiRequest(
          `/api/enderecos/${inspection.id_endereco}`
        );
        const response = await fetch(
          `https://viacep.com.br/ws/${endereco.cep}/json/`
        );
        if (!response.ok)
          throw new Error("Não foi possível conectar ao serviço de CEP.");
        const data = await response.json();
        if (data.erro) throw new Error("CEP não encontrado.");
        document.getElementById(
          "inspection-address"
        ).textContent = `${data.logradouro}, ${data.bairro}, ${data.localidade} - ${data.uf}`;
      } catch (error) {
        console.error("Erro ao carregar endereço:", error);
        document.getElementById("inspection-address").textContent =
          "Endereço não disponível";
      }
    }
  } catch (error) {
    console.error("Erro ao carregar dados da inspeção:", error);
    document.getElementById("inspection-name").textContent =
      "Erro ao carregar inspeção";
    document.getElementById("inspection-address").textContent =
      "Erro ao carregar endereço";
  }
}

function updateInspectionActions() {
  const startBtn = document.getElementById("start-inspection-btn");
  const finishBtn = document.getElementById("finish-inspection-btn");
  if (inspectionStatus === "Aberta") {
    startBtn.style.display = "inline-block";
    finishBtn.style.display = "none";
    setAddAmbienteEnabled(false);
  } else if (inspectionStatus === "Em Andamento") {
    startBtn.style.display = "none";
    finishBtn.style.display = "inline-block";
    setAddAmbienteEnabled(true);
  } else if (
    inspectionStatus === "Concluída" ||
    inspectionStatus === "Cancelada"
  ) {
    startBtn.style.display = "none";
    finishBtn.style.display = "none";
    setAddAmbienteEnabled(false);
  }
}

function setAddAmbienteEnabled(enabled) {
  // Aguarda o botão estar disponível no DOM
  const checkAndSetButton = () => {
    const btn = document.getElementById("add-ambient-btn");
    const shortcutContainer = document.querySelector(".shortcut-container");

    if (btn && shortcutContainer) {
      btn.disabled = !enabled;
      if (enabled) {
        shortcutContainer.style.display = "flex";
      } else {
        shortcutContainer.style.display = "none";
      }
    } else {
      // Se o botão não estiver disponível, tenta novamente em 100ms
      setTimeout(checkAndSetButton, 100);
    }
  };
  checkAndSetButton();
}

async function changeInspectionStatus(newStatus) {
  try {
    const inspectionId = getInspectionIdFromUrl();
    const url = `/api/inspecoes/${inspectionId}/status`;

    // Toast de loading
    if (newStatus === "Concluída") {
      showInfoToast("Finalizando inspeção e gerando relatório...", 3000);
    } else {
      showInfoToast(`Atualizando status para "${newStatus}"...`, 2000);
    }

    await apiRequest(url, {
      method: "PATCH",
      body: JSON.stringify({ status_nome: newStatus }),
    });

    // Se o status for "Concluída", gerar relatório automaticamente
    if (newStatus === "Concluída") {
      try {
        await generateReport(inspectionId);
        showSuccessToast("Inspeção finalizada com sucesso! Relatório gerado automaticamente.", 4000);
      } catch (reportError) {
        // Status foi atualizado, mas relatório falhou
        showWarningToast("Inspeção finalizada, mas houve erro ao gerar o relatório.", 4000);
      }
    } else {
      showSuccessToast(`Status atualizado para "${newStatus}" com sucesso!`);
    }

    inspectionStatus = newStatus;
    updateInspectionActions();
  } catch (e) {
    console.error("Erro ao atualizar status da inspeção:", e);
    showErrorToast("Erro ao atualizar status da inspeção. Tente novamente.");
  }
}

async function generateReport(inspectionId) {
  try {
    const reportTitle = `Relatório - ${inspectionId}`;
    const reportUrl = `/reports/inspection-${inspectionId}-${Date.now()}.pdf`;

    await apiRequest(`/api/inspecoes/${inspectionId}/relatorios`, {
      method: "POST",
      body: JSON.stringify({
        url: reportUrl,
        titulo: reportTitle,
      }),
    });

    // Toast de sucesso será mostrado na função changeInspectionStatus
  } catch (e) {
    console.error("Erro ao gerar relatório:", e);
    showWarningToast("Inspeção finalizada, mas houve erro ao gerar o relatório.");
    throw e; // Re-throw para que a função pai saiba que houve erro
  }
}

async function loadAmbientes(inspectionId) {
  try {
    const ambientes = await apiRequest(
      `/api/inspecoes/${inspectionId}/ambientes`
    );
    appState.ambientes = ambientes.filter((a) => !a.id_ambiente_pai);
    appState.filteredAmbientes = ambientes.filter((a) => !a.id_ambiente_pai);

    if (ambientes.length > 0) {

      // Carregar fotos dos ambientes
      const photoPromises = ambientes.map((ambiente) =>
        apiRequest(`/api/fotos/ambiente/${ambiente.id}`)
      );


      const photosByAmbient = await Promise.all(photoPromises);
      const allPhotos = photosByAmbient.flat();

      appState.bannerPhotosMeta = allPhotos.filter(
        (foto) => !foto.id_ocorrencia
      );
      const bannerPhotoIds = appState.bannerPhotosMeta.map((foto) => foto.id);

      if (bannerPhotoIds.length > 0) {
        const urlPromises = bannerPhotoIds.map((id) =>
          apiRequest(`/api/fotos/${id}/signed-url`)
        );
        const urlData = await Promise.all(urlPromises);
        urlData.forEach((data, index) => {
          if (data && data.signedUrl) {
            const photoId = bannerPhotoIds[index];
            appState.signedUrls[photoId] = data.signedUrl;
          }
        });
      }



      const ocorrenciasPromises = appState.ambientes.map(async (ambiente) => {
        try {
          const ocorrencias = await apiRequest(`/api/ocorrencias/ambiente/${ambiente.id}`);
          ambiente.ocorrencias = ocorrencias || [];
          return ambiente;
        } catch (error) {
          console.error(`Erro ao carregar ocorrências do ambiente ${ambiente.id}:`, error);
          ambiente.ocorrencias = [];
          return ambiente;
        }
      });

      appState.ambientes = await Promise.all(ocorrenciasPromises);
      appState.filteredAmbientes = appState.ambientes;

   
    }
    renderAmbientes();
  } catch (error) {
    console.error("Erro ao carregar ambientes:", error);
    showEmptyState();
  }
}

async function loadOcorrenciasCounts() {
  try {
    const ocorrenciasPromises = appState.ambientes.map(ambiente =>
      apiRequest(`/api/ambientes/${ambiente.id}/ocorrencias`)
    );
    const ocorrenciasResults = await Promise.all(ocorrenciasPromises);

    // Mapear contagem de ocorrências por ambiente
    appState.ambientes.forEach((ambiente, index) => {
      appState.ocorrenciasCounts[ambiente.id] = ocorrenciasResults[index].length;
    });
  } catch (error) {
    console.error('Erro ao carregar contagem de ocorrências:', error);
    // Em caso de erro, definir contagem como 0 para todos os ambientes
    appState.ambientes.forEach(ambiente => {
      appState.ocorrenciasCounts[ambiente.id] = 0;
    });
  }
}

function renderAmbientes() {
  const container = document.getElementById("cards-container");
  const loading = document.getElementById("loading");
  const emptyState = document.getElementById("empty-state");

  loading.style.display = "none";

  if (appState.filteredAmbientes.length === 0) {
    container.style.display = "none";
    emptyState.style.display = "block";
    return;
  }

  emptyState.style.display = "none";
  container.style.display = "grid";

  (async () => {
    const ambientesWithUserNames = await Promise.all(
      appState.filteredAmbientes.map(async (ambiente) => {
        const bannerMeta = appState.bannerPhotosMeta.find(
          (p) => p.id_ambiente === ambiente.id
        );
        let imageUrl = "/assets/templates/ambient-banner.png";
        if (bannerMeta) {
          imageUrl = appState.signedUrls[bannerMeta.id] || imageUrl;
        }
        const userName = await getUserName(ambiente.id_usuario);

        return `
              <div class="card" data-ambient-id="${
                ambiente.id || "sem-id"
              }" onclick="navigateToAmbient('${ambiente.id}')">
                  <img src="${imageUrl}" class="card-image" alt="${
          ambiente.titulo || "Ambiente"
        }" loading="lazy" />
                  <div class="card-content">
                  <div class="card-header">
                      <h3 class="card-title">${
                        ambiente.titulo || "Ambiente sem nome"
                      }</h3>
                      <span class="card-date">${
                        formatDate(ambiente.criado_em) || "Sem data"
                      }</span>
                  </div>
                  <div class="card-details">
                      <p class="card-observations"><strong>Observações:</strong> ${
                        ambiente.observacoes || "Sem observações"
                      }</p>
                      <p class="card-analysis"><strong>Análise:</strong> <span class="analysis-status">${getAnaliseStatus(
                        ambiente
                      )}</span></p>
                      <p class="card-occurrences"><strong>Ocorrências:</strong> ${getOcorrenciasCount(
                        ambiente
                      )}</p>
                  </div>
                  <p class="card-creator">Criado por <a href="/usuario/${
                    ambiente.id_usuario
                  }">${userName}</a></p>
                  </div>
              </div>
          `;
      })
    );
    container.innerHTML = ambientesWithUserNames.join("");
  })();
}

function formatDate(dateString) {
  if (!dateString) return "Sem data";
  return new Date(dateString).toLocaleDateString("pt-BR");
}

function getAnaliseStatus(ambiente) {
  return ambiente.analise || "Aberta";
}

function getOcorrenciasCount(ambiente) {

  if (!ambiente.ocorrencias) {
    return "0";
  }

  const count = ambiente.ocorrencias.length;
  return count.toString();


const userNameCache = {};

async function getUserName(userId) {
  if (!userId) return "Usuário desconhecido";
  if (userNameCache[userId]) return userNameCache[userId];

  try {
    const token = localStorage.getItem("authToken");
    const response = await fetch(`/api/usuarios/${userId}`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    if (!response.ok) throw new Error("Erro ao buscar usuário");
    const user = await response.json();
    const name = user.nome || `Usuário ${userId}`;
    userNameCache[userId] = name;
    return name;
  } catch (e) {
    return `Usuário ${userId}`;
  }
}

function navigateToAmbient(ambientId) {
  if (ambientId && ambientId !== "sem-id") {
    window.location.href = `${window.location.href}/ambient-details/${ambientId}`;
  }
}

function filterAmbientes() {
  let filtered = [...appState.ambientes];
  const filter = appState.currentFilter;
  const searchTerm = appState.searchTerm.toLowerCase();

  if (filter !== "all") {
    filtered = filtered.filter((ambiente) => {
      const status = getAnaliseStatus(ambiente).toLowerCase();
      return status.includes(filter.replace("-", " "));
    });
  }

  if (searchTerm) {
    filtered = filtered.filter(ambiente =>
      (ambiente.titulo || '').toLowerCase().includes(searchTerm) ||
      (ambiente.observacoes || '').toLowerCase().includes(searchTerm)
    );
  }

  appState.filteredAmbientes = filtered;
  renderAmbientes();
}

function setupEventListeners() {
  document.querySelectorAll(".filters .btn").forEach((btn) => {
    btn.addEventListener("click", (e) => {
      document.querySelector(".filters .btn.active").classList.remove("active");
      e.target.classList.add("active");
      appState.currentFilter = e.target.dataset.filter;
      filterAmbientes();
    });
  });

  const searchInput = document.getElementById("search-input");
  searchInput.addEventListener("input", (e) => {
    appState.searchTerm = e.target.value;
    filterAmbientes();
  });

  // Event listeners para os botões de inspeção
  const startBtn = document.getElementById("start-inspection-btn");
  const finishBtn = document.getElementById("finish-inspection-btn");

  if (startBtn) {
    startBtn.onclick = () => changeInspectionStatus("Em Andamento");
  }

  if (finishBtn) {
    finishBtn.onclick = () => changeInspectionStatus("Concluída");
  }
}

function showEmptyState() {
  document.getElementById("loading").style.display = "none";
  document.getElementById("cards-container").style.display = "none";
  document.getElementById("empty-state").style.display = "block";
}

async function initializeApp() {
  const inspectionId = getInspectionIdFromUrl();
  if (!inspectionId) {
    showEmptyState();
    document.getElementById(
      "empty-state"
    ).innerHTML = `<h3>ID da inspeção não encontrado na URL</h3>`;
    return;
  }
  appState.currentInspectionId = inspectionId;
  setupEventListeners();
  await Promise.all([
    loadInspectionData(inspectionId),
    loadAmbientes(inspectionId),
  ]);
}

document.addEventListener("DOMContentLoaded", initializeApp);
