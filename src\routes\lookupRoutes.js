const express = require("express");
const router = express.Router();
const lookupController = require("../controllers/lookupController");

router.get("/tipos-sistema", lookupController.listarTiposSistema);
router.post("/tipos-sistema", lookupController.criarTipoSistema);
router.get("/tipos-patologia", lookupController.listarTiposPatologia);
router.post("/tipos-patologia", lookupController.criarTipoPatologia);
router.get("/afazeres", lookupController.listarAfazeres);
router.post("/afazeres", lookupController.criarAfazer);
router.get("/tipos-edificacao", lookupController.listarTiposEdificacao);

module.exports = router;