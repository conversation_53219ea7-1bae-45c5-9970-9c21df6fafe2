const db = require('../config/db');
jest.mock('../config/db'); 

const Relatorio = require('../models/relatoriosModel');

describe('Model: Relatorio', () => {
  afterEach(() => jest.clearAllMocks());

  it('deve criar um relatório', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, titulo: 'Relatório 1' }] });
    const result = await Relatorio.create({ id_inspecao: 1, url: 'url', titulo: 'Relatório 1' });
    expect(result.titulo || result.nome).toBeTruthy();
  });

  it('deve tratar erro ao criar relatório', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao criar relatório'));
    await expect(Relatorio.create({ id_inspecao: 1, url: 'url', titulo: 'Relatório 1' }))
      .rejects.toThrow('Erro ao criar relatório');
  });

  it('deve buscar relatório por id', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, nome: 'Relatório 1' }] });
    const result = await Relatorio.findById(1);
    expect(result.id).toBe(1);
  });

  it('deve retornar undefined ao buscar relatório por id inexistente', async () => {
    db.query.mockResolvedValueOnce({ rows: [] });
    const result = await Relatorio.findById(999);
    expect(result).toBeUndefined();
  });

  it('deve tratar erro ao buscar relatório por id', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar relatório por ID:'));
    await expect(Relatorio.findById(1)).rejects.toThrow('Erro ao buscar relatório por ID:');
  });

  it('deve buscar todos os relatórios', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ relatorio_id: 1, url: 'url', nome_inspecao: 'Inspecao', endereco: '12345-000' }] });
    const result = await Relatorio.findAll();
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].relatorio_id).toBe(1);
  });

  it('deve tratar erro ao buscar todos os relatórios', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar relatórios:'));
    await expect(Relatorio.findAll()).rejects.toThrow('Erro ao buscar relatórios:');
  });

  it('deve buscar relatórios por id_inspecao', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, id_inspecao: 1 }] });
    const result = await Relatorio.findAllByInspecaoId(1);
    expect(Array.isArray(result)).toBe(true);
    expect(result[0].id_inspecao).toBe(1);
  });

  it('deve tratar erro ao buscar relatórios por id_inspecao', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao buscar relatórios por ID da inspeção:'));
    await expect(Relatorio.findAllByInspecaoId(1)).rejects.toThrow('Erro ao buscar relatórios por ID da inspeção:');
  });

  it('deve atualizar um relatório', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1, url: 'novaurl', nome: 'Novo Relatório' }] });
    const result = await Relatorio.update(1, { url: 'novaurl', titulo: 'Novo Relatório' });
    expect(result.url).toBe('novaurl');
  });

  it('deve tratar erro ao atualizar relatório', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao atualizar relatório:'));
    await expect(Relatorio.update(1, { url: 'novaurl', titulo: 'Novo Relatório' })).rejects.toThrow('Erro ao atualizar relatório:');
  });

  it('deve remover um relatório', async () => {
    db.query.mockResolvedValueOnce({ rows: [{ id: 1 }] });
    const result = await Relatorio.remove(1);
    expect(result.id).toBe(1);
  });

  it('deve tratar erro ao remover relatório', async () => {
    db.query.mockRejectedValueOnce(new Error('Erro ao remover relatório:'));
    await expect(Relatorio.remove(1)).rejects.toThrow('Erro ao remover relatório:');
  });
});