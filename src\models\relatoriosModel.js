const db = require("../config/db");

const Relatorio = {
  async create({ id_inspecao, url, titulo }) {
    const query = `
      INSERT INTO relatorios (id_inspecao, url)
      VALUES ($1, $2)
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [id_inspecao, url]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao criar relatório:", error);
      throw error;
    }
  },

  async findAllByInspecaoId(id_inspecao) {
    const query =
      "SELECT * FROM relatorios WHERE id_inspecao = $1 ORDER BY id;";
    try {
      const { rows } = await db.query(query, [id_inspecao]);
      return rows;
    } catch (error) {
      console.error("Erro ao buscar relatórios por ID da inspeção:", error);
      throw error;
    }
  },

  async findById(id) {
    const query = "SELECT * FROM relatorios WHERE id = $1;";
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao buscar relatório por ID:", error);
      throw error;
    }
  },

  async update(id, { url, titulo }) {
    const query = `
      UPDATE relatorios
      SET url = $1
      WHERE id = $2
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [url, id]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao atualizar relatório:", error);
      throw error;
    }
  },

  async remove(id) {
    const query = "DELETE FROM relatorios WHERE id = $1 RETURNING id;";
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error("Erro ao remover relatório:", error);
      throw error;
    }
  },

  async findAll() {
    const query = `
  SELECT
  relatorios.id AS relatorio_id,
  relatorios.url,
  relatorios.id_inspecao,
  inspecoes.nome AS nome_inspecao,
  inspecoes.criado_em,
  enderecos.cep AS endereco,
  projetos.codigo_projeto
FROM relatorios
JOIN inspecoes ON relatorios.id_inspecao = inspecoes.id
JOIN enderecos ON inspecoes.id_endereco = enderecos.id
LEFT JOIN projetos ON inspecoes.id_projeto = projetos.id
ORDER BY relatorios.id;
`;

    try {
      const { rows } = await db.query(query);
      return rows;
    } catch (error) {
      console.error("Erro ao buscar relatórios:", error);
      throw error;
    }
  },

  async findAllByPeriodo(dataInicio, dataFim) {
    const query = `
      SELECT * FROM relatorios
      WHERE DATE(criado_em) >= DATE($1) AND DATE(criado_em) <= DATE($2)
      ORDER BY criado_em DESC;
    `;
    try {
      const { rows } = await db.query(query, [dataInicio, dataFim]);
      return rows;
    } catch (error) {
      console.error("Erro ao buscar relatórios por período:", error);
      throw error;
    }
  },

  async count() {
    const query = 'SELECT COUNT(*) as total FROM relatorios;';
    try {
      const { rows } = await db.query(query);
      return parseInt(rows[0].total);
    } catch (error) {
      console.error("Erro ao contar relatórios:", error);
      throw error;
    }
  },
};

module.exports = Relatorio;