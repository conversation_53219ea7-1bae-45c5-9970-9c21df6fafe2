const express = require("express");
const apiRoutes = express.Router();

const authRoutes = require("./authRoutes");
const fotoRoutes = require("./fotosRoutes");
const equipeRoutes = require("./equipesRoutes");
const usuarioRoutes = require("./usuariosRoutes");
const inspecaoRoutes = require("./inspecoesRoutes");
const enderecoRoutes = require("./enderecosRoutes");
const ambienteRoutes = require("./ambientesRoutes");
const relatorioRoutes = require("./relatoriosRoutes");
const ocorrenciaRoutes = require("./ocorrenciasRoutes");
const projetoRoutes = require("./projetosRoutes");
const auth = require("../middlewares/authMiddleware");

apiRoutes.use("/login", authRoutes);
apiRoutes.use("/usuarios", usuarioRoutes);

apiRoutes.use(auth);

apiRoutes.use("/fotos", fotoRoutes);
apiRoutes.use("/equipes", equipeRoutes);
apiRoutes.use("/inspecoes", inspecaoRoutes);
apiRoutes.use("/enderecos", enderecoRoutes);
apiRoutes.use("/ambientes", ambienteRoutes);
apiRoutes.use("/relatorios", relatorioRoutes);
apiRoutes.use("/ocorrencias", ocorrenciaRoutes);
apiRoutes.use("/projetos", projetoRoutes);
apiRoutes.use("/lookup", require("./lookupRoutes"));
module.exports = apiRoutes;