const express = require("express");
const router = express.Router();
const multer = require("multer");
const fotoController = require("../controllers/fotosController");

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

router.get("/:id_foto/signed-url", fotoController.gerarUrlAssinada);
router.get(
  "/ocorrencia/:id_ocorrencia",
  fotoController.listarFotosPorOcorrencia
);
router.get("/ambiente/:id_ambiente", fotoController.listarFotosPorAmbiente);
router.post(
  "/upload/ambiente_ocorrencia",
  upload.array("fotos", 10),
  fotoController.adicionarFotoAmbienteOcorrencia
);
router.get("/:id_foto", fotoController.obterMetadadosFotoPorId);
router.delete("/:id_foto", fotoController.deletarFoto);

module.exports = router;