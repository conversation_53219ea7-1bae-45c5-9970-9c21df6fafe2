const lookupService = require('../services/lookupService');

exports.listarTiposSistema = async (req, res, next) => {
    try {
        const data = await lookupService.listarTiposSistema();
        res.status(200).json(data);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarTiposSistema:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.listarAfazeres = async (req, res, next) => {
    try {
        const afazeres = await lookupService.listarAfazeres();
        res.status(200).json(afazeres);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarAfazeres:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.listarTiposEdificacao = async (req, res, next) => {
    try {
        const data = await lookupService.listarTiposEdificacao();
        res.status(200).json(data);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarTiposEdificacao:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.listarTiposPatologia = async (req, res, next) => {
    try {
        const data = await lookupService.listarTiposPatologia();
        res.status(200).json(data);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarTiposPatologia:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.criarTipoSistema = async (req, res, next) => {
    try {
        const { nome, descricao } = req.body;

        if (!nome || nome.trim() === '') {
            return res.status(400).json({ message: 'Nome do tipo de sistema é obrigatório.' });
        }

        const novoTipo = await lookupService.criarTipoSistema({ nome: nome.trim(), descricao });
        res.status(201).json(novoTipo);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarTipoSistema:', error);
        if (error.message.includes('já existe')) {
            res.status(409).json({ message: error.message });
        } else {
            res.status(500).json({ message: error.message });
        }
    }
};

exports.criarTipoPatologia = async (req, res, next) => {
    try {
        const { nome, descricao } = req.body;

        if (!nome || nome.trim() === '') {
            return res.status(400).json({ message: 'Nome do tipo de patologia é obrigatório.' });
        }

        const novoTipo = await lookupService.criarTipoPatologia({ nome: nome.trim(), descricao });
        res.status(201).json(novoTipo);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarTipoPatologia:', error);
        if (error.message.includes('já existe')) {
            res.status(409).json({ message: error.message });
        } else {
            res.status(500).json({ message: error.message });
        }
    }
};

exports.criarAfazer = async (req, res, next) => {
    try {
        const { titulo } = req.body;

        if (!titulo || titulo.trim() === '') {
            return res.status(400).json({ message: 'Título do afazer é obrigatório.' });
        }

        const novoAfazer = await lookupService.criarAfazer({ titulo: titulo.trim() });
        res.status(201).json(novoAfazer);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarAfazer:', error);
        if (error.message.includes('já existe')) {
            res.status(409).json({ message: error.message });
        } else {
            res.status(500).json({ message: error.message });
        }
    }
};