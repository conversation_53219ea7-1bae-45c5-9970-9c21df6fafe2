/* Kanban Board - Design Consistente */
.kanban-container {
  padding: clamp(1rem, 3vw, 1.5rem);
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg,
    var(--background-color) 0%,
    rgba(0, 85, 140, 0.01) 50%,
    var(--background-color) 100%);
  position: relative;
  animation: fadeInUp 0.6s ease-out;
}

/* Efeito sutil de fundo */
.kanban-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 85, 140, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 85, 140, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.kanban-container > * {
  position: relative;
  z-index: 1;
}

/* Header */
.kanban-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.kanban-header h1 {
  margin: 0;
  color: var(--text-color);
  position: relative;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.kanban-header h1::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), transparent);
  border-radius: 2px;
  animation: expandWidth 1s ease-out 0.5s both;
}

.kanban-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.kanban-actions button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.kanban-actions img {
  width: 16px;
  height: 16px;
}

/* Estatísticas */
.kanban-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 85, 140, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 85, 140, 0.15);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Board */
.kanban-board {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.kanban-column {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 85, 140, 0.1);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 500px;
}

.kanban-column:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 85, 140, 0.15);
}

.column-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 85, 140, 0.1);
  background: rgba(248, 250, 252, 0.8);
}

.column-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.column-title h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
}

.count {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

/* Status Indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.aberta {
  background-color: #6B7280;
}

.status-indicator.em-andamento {
  background-color: #F59E0B;
}

.status-indicator.concluida {
  background-color: #10B981;
}

.status-indicator.cancelada {
  background-color: #EF4444;
}

.column-content {
  padding: 1rem;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Kanban Cards */
.kanban-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 85, 140, 0.1);
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.kanban-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-color);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.kanban-card:hover::before {
  transform: scaleX(1);
}

.kanban-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 85, 140, 0.1);
}

.card-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.card-id {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: 0.75rem;
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--text-muted);
}

.card-date {
  font-size: 0.75rem;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.card-action-btn {
  padding: 0.25rem 0.5rem;
  border: 1px solid rgba(0, 85, 140, 0.2);
  background: transparent;
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-color);
}

.card-action-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-muted);
  text-align: center;
}

.empty-state .icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--background-color);
  border-radius: 16px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

.modal-body {
  padding: 1.5rem;
}

.status-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.status-btn {
  padding: 0.75rem;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
  color: white;
}

.status-btn.aberta {
  background-color: #6B7280;
}

.status-btn.em-andamento {
  background-color: #F59E0B;
}

.status-btn.concluida {
  background-color: #10B981;
}

.status-btn.cancelada {
  background-color: #EF4444;
}

.status-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Dark Mode */
[data-theme="dark"] .kanban-container {
  background: linear-gradient(135deg,
    var(--background-color) 0%,
    rgba(59, 130, 246, 0.02) 50%,
    var(--background-color) 100%);
}

[data-theme="dark"] .stat-card,
[data-theme="dark"] .kanban-column,
[data-theme="dark"] .kanban-card {
  background: rgba(31, 41, 55, 0.9);
  border-color: rgba(75, 85, 99, 0.3);
}

[data-theme="dark"] .column-header {
  background: rgba(17, 24, 39, 0.8);
  border-color: rgba(75, 85, 99, 0.3);
}

[data-theme="dark"] .modal-content {
  background: var(--background-color);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .kanban-board {
    grid-template-columns: 1fr;
  }
  
  .kanban-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .kanban-actions {
    justify-content: center;
  }
  
  .kanban-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Animações Consistentes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandWidth {
  from {
    width: 0;
  }
  to {
    width: 40px;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
