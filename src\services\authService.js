const UsuarioModel = require('../models/usuariosModel');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

class AuthService {
    
    validarDadosLogin(dadosLogin) {
        const { email, senha } = dadosLogin;
        
        if (!email || !senha) {
            throw new Error('Email e senha são obrigatórios.');
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error('Formato de email inválido.');
        }

        return { email, senha };
    }

    async verificarCredenciais(email, senha) {
        try {
            
            const usuario = await UsuarioModel.findByEmail(email);
            if (!usuario) {
                throw new Error('Credenciais inválidas.');
            }

            const senhaValida = await bcrypt.compare(senha, usuario.senha);
            if (!senhaValida) {
                throw new Error('Credenciais inválidas.');
            }

            return usuario;
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] verificarCredenciais:', error);
            throw error;
        }
    }

    gerarToken(usuario) {
        try {
            const payload = {
                usuarioId: usuario.id,
                email: usuario.email,
                nome: usuario.nome
            };

            const token = jwt.sign(payload, process.env.JWT_SECRET, { 
                expiresIn: '1h' 
            });

            return token;
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] gerarToken:', error);
            throw new Error('Erro ao gerar token de autenticação.');
        }
    }

    verificarToken(token) {
        try {
            const payload = jwt.verify(token, process.env.JWT_SECRET);
            return payload;
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] verificarToken:', error);
            throw new Error('Token inválido ou expirado.');
        }
    }

    async loginUsuario(dadosLogin) {
        try {
            
            const { email, senha } = this.validarDadosLogin(dadosLogin);

            const usuario = await this.verificarCredenciais(email, senha);

            const token = this.gerarToken(usuario);

            return {
                success: true,
                message: "Login bem-sucedido!",
                token: token,
                usuarioId: usuario.id,
                permissao: usuario.permissao,
                nome: usuario.nome,
                email: usuario.email
            };
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] loginUsuario:', error);

            return {
                success: false,
                message: error.message || "Erro interno no servidor."
            };
        }
    }

    validarPermissao(usuario, permissaoRequerida) {
        try {

            return true;
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] validarPermissao:', error);
            return false;
        }
    }

    extrairToken(authHeader) {
        if (!authHeader || !authHeader.startsWith("Bearer ")) {
            throw new Error('Token não fornecido ou mal formatado.');
        }

        return authHeader.split(" ")[1];
    }

    async autenticarToken(authHeader) {
        try {
            
            const token = this.extrairToken(authHeader);

            const payload = this.verificarToken(token);

            return payload;
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] autenticarToken:', error);
            throw error;
        }
    }

    async logoutUsuario(token) {
        try {

            return {
                success: true,
                message: "Logout realizado com sucesso."
            };
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] logoutUsuario:', error);
            throw new Error('Erro ao realizar logout.');
        }
    }

    async refreshToken(token) {
        try {
            
            const payload = this.verificarToken(token);

            const usuario = await UsuarioModel.findById(payload.usuarioId);
            if (!usuario) {
                throw new Error('Usuário não encontrado.');
            }

            const novoToken = this.gerarToken(usuario);

            return {
                success: true,
                token: novoToken,
                message: "Token renovado com sucesso."
            };
        } catch (error) {
            console.error('[AUTH_SERVICE_ERROR] refreshToken:', error);
            throw error;
        }
    }
}

module.exports = new AuthService();