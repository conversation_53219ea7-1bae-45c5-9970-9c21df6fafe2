<link rel="stylesheet" href="/css/adm/add.css">
<link rel="stylesheet" href="/css/toast.css">

<div class="form-page-container">
  <div class="form-wrapper">
    <h1 class="h4 form-title">Adicionar Novo Usuário</h1>

    <div id="messageArea" class="message-area"></div>

    <form id="addUserForm" class="form-body" novalidate>

      <div class="form-group">
        <label for="nome" class="form-label">Nome Completo</label>
        <input type="text" id="nome" name="nome" placeholder="Insira o nome completo" required>
      </div>

      <div class="form-group">
        <label for="email" class="form-label">Email</label>
        <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
      </div>

      <div class="form-group">
        <label for="senha" class="form-label"><PERSON>ha</label>
        <input type="password" id="senha" name="senha" placeholder="Crie uma senha forte (mín. 8 caracteres)" required>
      </div>

      <div class="form-group">
        <label for="confirmarSenha" class="form-label">Confirmar Senha</label>
        <input type="password" id="confirmarSenha" name="confirmarSenha" placeholder="Confirme a senha" required>
      </div>

      <div class="form-footer">
        <button type="submit" class="btn-primary">Criar Usuário</button>
      </div>
    </form>
  </div>
</div>

<script src="/js/toast.js"></script>
<script src="/js/adm/add-user.js"></script>