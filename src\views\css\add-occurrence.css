/* Enhanced Add Occurrence - Consistent Design */
.form-page-container {
    padding: clamp(1rem, 4vw, 1.5rem) clamp(1rem, 3vw, 1.25rem);
    width: 100%;
    margin: 0 auto;
    background: linear-gradient(135deg,
        var(--background-color) 0%,
        rgba(0, 85, 140, 0.01) 50%,
        var(--background-color) 100%);
    position: relative;
    min-height: 100vh;
}

/* Efeito sutil de fundo */
.form-page-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(0, 85, 140, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 85, 140, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.form-page-container > * {
    position: relative;
    z-index: 1;
}

.form-wrapper {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    animation: fadeInUp 0.6s ease-out;
}

.form-title {
    margin-bottom: 24px;
    position: relative;
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.form-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    border-radius: 2px;
    animation: expandWidth 1s ease-out 0.5s both;
}

.form-body {
    display: grid;
    grid-template-columns: 1fr;
    gap: clamp(1.25rem, 3vw, 1.5rem);
    animation: fadeInUp 0.6s ease-out 0.3s both;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px
}

.form-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--foreground-color);
    position: relative;
}

.form-label::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    border-radius: 1px;
    animation: expandWidth 1s ease-out 0.5s both;
}

.form-divider {
    border: none;
    border-top: 1px solid var(--muted-color, #E4E4E7)
}

.form-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px
}

input[type=text],
input[type=date],
textarea,
select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--muted-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    background-color: var(--background-color);
    color: var(--foreground-color)
}

textarea {
    min-height: 120px;
    resize: vertical
}

input::placeholder,
textarea::placeholder {
    color: #a1a1aa;
    font-family: inherit
}

select {
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right .7rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem
}

select:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
    color: #9ca3af
}

.upload-area {
    border: 2px dashed var(--muted-color);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    background-color: #f9fafb;
    min-height: 150px;
    transition: all 0.3s ease;
    color: var(--foreground-color);
}

.upload-area:hover {
    background-color: #f3f4f6;
    border-color: var(--primary-color);
}

/* Upload area no modo escuro */
[data-theme="dark"] .upload-area {
    background-color: #374151;
    border-color: #6b7280;
    color: #d1d5db;
}

[data-theme="dark"] .upload-area:hover {
    background-color: #4b5563;
    border-color: #3b82f6;
}

.upload-icons {
    display: flex;
    gap: 32px;
    margin-bottom: 12px
}

.icon-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--muted-foreground-color);
    font-size: .8rem
}

.icon-wrapper svg {
    stroke: var(--muted-foreground-color);
    transition: stroke 0.3s ease;
}

.upload-area:hover .icon-wrapper svg {
    stroke: var(--primary-color);
}

/* Ícones no modo escuro */
[data-theme="dark"] .icon-wrapper {
    color: #d1d5db;
}

[data-theme="dark"] .icon-wrapper svg {
    stroke: #d1d5db;
}

[data-theme="dark"] .upload-area:hover .icon-wrapper {
    color: #3b82f6;
}

[data-theme="dark"] .upload-area:hover .icon-wrapper svg {
    stroke: #3b82f6;
}

input[type=file] {
    display: none
}

.image-preview {
    max-height: 120px;
    width: auto;
    max-width: 100%;
    border-radius: 8px;
    margin-bottom: 12px;
    object-fit: cover
}

.preview-info {
    font-size: .9rem;
    font-weight: 500;
    color: var(--foreground-color);
    word-break: break-all
}

.upload-text {
    color: var(--muted-foreground-color);
    font-size: 0.9rem;
    margin-top: 8px;
}

/* Texto no modo escuro */
[data-theme="dark"] .preview-info {
    color: #ffffff;
}

[data-theme="dark"] .upload-text {
    color: #d1d5db;
}

.remove-selection-btn {
    background: 0 0;
    border: none;
    color: var(--destructive-color, #E53935);
    text-decoration: underline;
    cursor: pointer;
    font-size: .8rem;
    margin-top: 8px;
    padding: 4px;
    transition: color 0.3s ease;
}

.remove-selection-btn:hover {
    color: #d32f2f;
}

/* Botão remover no modo escuro */
[data-theme="dark"] .remove-selection-btn {
    color: #f87171;
}

[data-theme="dark"] .remove-selection-btn:hover {
    color: #fca5a5;
}

.pathology-search-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-bar {
    flex-grow: 1;
    position: relative;
}

.search-bar input {
    width: 100%;
    padding-left: 36px;
}

.search-bar svg {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    stroke: #9ca3af;
}

.btn-add-new-square {
    width: 45px;
    height: 45px;
    flex-shrink: 0;
    border: 1px solid var(--muted-color);
    border-radius: 8px;
    background: #fafafa;
    font-size: 1.5rem;
    color: var(--muted-foreground-color);
    cursor: pointer;
}

.selection-prompt {
    font-size: .8rem;
    color: var(--muted-foreground-color);
    margin-top: 12px;
}

.tags-container-grid {
    gap: 12px;
}

.tag-select-grid {
    border: 1px solid var(--muted-color);
    color: var(--muted-foreground-color);
    background-color: #f9fafb;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: .9rem;
    cursor: pointer;
    font-weight: 500;
    text-align: center;
}

.tag-select-grid.active {
    border-color: var(--primary-color);
    background-color: #eef7ff;
    color: var(--primary-color)
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.btn-add-new {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background-color: #eef7ff;
    padding: 6px 16px;
    border-radius: 999px;
    font-size: .85rem;
    font-weight: 500;
    cursor: pointer
}

.tag-select {
    border: 1px solid var(--muted-color);
    color: var(--muted-foreground-color);
    background-color: transparent;
    padding: 6px 16px;
    border-radius: 999px;
    font-size: .85rem;
    font-weight: 500;
    cursor: pointer;
}

.tag-select.active {
    background-color: var(--primary-color);
    color: var(--background-color);
    border-color: var(--primary-color);
}

/* Tags selecionadas no modo escuro */
[data-theme="dark"] .tag-select {
    border-color: #6b7280;
    color: #d1d5db;
    background-color: #374151;
}

[data-theme="dark"] .tag-select.active {
    background-color: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .tag-select-grid {
    border-color: #6b7280;
    color: #d1d5db;
    background-color: #374151;
}

[data-theme="dark"] .tag-select-grid.active {
    background-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.loading-text {
    font-size: .9rem;
    color: var(--muted-foreground-color);
    font-style: italic;
}

/* Estilos dos Modais */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: var(--background-color);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--muted-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--muted-foreground-color);
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--muted-color);
    color: var(--foreground-color);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid var(--muted-color);
}

.btn-secondary {
    background-color: transparent;
    border: 1px solid var(--muted-color);
    color: var(--muted-foreground-color);
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background-color: var(--muted-color);
    color: var(--foreground-color);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modo escuro para modais */
[data-theme="dark"] .modal-content {
    background-color: #1f2937;
    border: 1px solid #374151;
}

[data-theme="dark"] .modal-header {
    border-bottom-color: #374151;
}

[data-theme="dark"] .modal-footer {
    border-top-color: #374151;
}

[data-theme="dark"] .modal-close:hover {
    background-color: #374151;
}

[data-theme="dark"] .btn-secondary {
    border-color: #6b7280;
    color: #d1d5db;
}

[data-theme="dark"] .btn-secondary:hover {
    background-color: #374151;
    color: #ffffff;
}

@media (min-width:768px) {
    .form-body {
        grid-template-columns: 1fr 1fr;
    }

    .grid-span-2 {
        grid-column: 1 / -1;
    }

    .form-footer .btn-primary {
        width: auto;
        min-width: 320px;
        padding: 12px 32px;
    }
}

/* Animações Consistentes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes expandWidth {
    from {
        width: 0;
    }
    to {
        width: 30px;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}