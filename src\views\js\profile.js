const editBtn = document.querySelector("#edit");
const cancelBtn = document.querySelector("#cancel");
const saveBtn = document.querySelector("#save");
const formInputs = document.querySelectorAll(".profile-form input");
const editModeButtons = document.querySelector(".buttons-edit-mode");
const title = document.getElementById("profile-title");
const editIcons = document.querySelectorAll(".edit-icon");
const inputIconMail = document.querySelector(".input-iconMail");
const form = document.querySelector(".profile-form");

let originalValues = [];

editBtn.addEventListener("click", () => {
  originalValues = Array.from(formInputs).map((input) => input.value);

  form.classList.add("edit-mode");

  formInputs.forEach((input) => {
    if (input.type !== "password") {
      input.disabled = false;
    }
  });

  editModeButtons.classList.remove("hidden");
  editBtn.classList.add("hidden");

  title.textContent = "Editar Perfil";
  editIcons.forEach((icon) => icon.classList.remove("hidden"));
  inputIconMail.classList.remove("hidden");
});

cancelBtn.addEventListener("click", () => {
  form.classList.remove("edit-mode");

  formInputs.forEach((input, index) => {
    input.value = originalValues[index];
    input.disabled = true;
  });

  editModeButtons.classList.add("hidden");
  editBtn.classList.remove("hidden");

  title.textContent = "Seu perfil";
  editIcons.forEach((icon) => icon.classList.add("hidden"));
  inputIconMail.classList.add("hidden");
});

saveBtn.addEventListener("click", async (e) => {
  e.preventDefault();

  form.classList.remove("edit-mode");

  formInputs.forEach((input) => (input.disabled = true));

  editModeButtons.classList.add("hidden");
  editBtn.classList.remove("hidden");

  title.textContent = "Seu perfil";
  editIcons.forEach((icon) => icon.classList.add("hidden"));
  inputIconMail.classList.add("hidden");

  const updatedData = {};
  formInputs.forEach((input) => {
    updatedData[input.name] = input.value;
  });

  try {
    const usuarioId = localStorage.getItem("usuarioId");

    if (!usuarioId) {
      alert("Usuário não autenticado.");
      return;
    }

    const token = localStorage.getItem("authToken");
    if (!token) {
      window.location.href = "/";
      return;
    }

    const response = await fetch(`/api/usuarios/${usuarioId}`, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updatedData),
    });

    if (!response.ok) throw new Error("Erro ao salvar dados!");

    console.log("Dados salvos:", updatedData);
  } catch (error) {
    console.error("Erro ao salvar perfil:", error);
    alert("Erro ao salvar perfil. Tente novamente.");
  }
});

const passwordToggle = document.querySelector(".input-iconSenha");
const passwordInput = document.querySelector('input[type="password"]');

if (passwordToggle && passwordInput) {
  passwordToggle.addEventListener("click", () => {
    const isPassword = passwordInput.type === "password";
    passwordInput.type = isPassword ? "text" : "password";
    passwordToggle.src = isPassword
      ? "/assets/icons/eye-muted.svg"
      : "/assets/icons/eye-slash-muted.svg";
  });
}