# Seção 4.1: Descrição do Desenvolvimento do Projeto 

Durante o ciclo de desenvolvimento do InsPecT na sua primeira versão, diversas etapas foram concluídas tanto na frente de código quanto na de design para estabelecer a fundação e a funcionalidade da aplicação, seguindo a arquitetura MVC (Model-View-Controller) para o backend e estruturando as views iniciais.

### Código 
---
#### Estrutura do Banco de Dados 
Foi elaborado e implementado o arquivo SQL contendo todos os comandos CREATE TABLE necessários para a criação do esquema do banco de dados PostgreSQL. A ordem de criação das tabelas foi definida para respeitar as dependências de chaves estrangeiras.

O Código de criação das tabelas pode ser encontrado em  **[Arquivo da criação das tabelas](../../src/scripts/init.sql).**

---
#### Views
Foram desenvolvidos os arquivos das views. Estes arquivos são responsáveis pela aparência direta com a interface do usuário, contendo EJS e CSS para seu desenvolvimento.

As views podem ser encontradas em:

##### Login:
- **[login.ejs](../../src/views/pages/login.ejs):** Página de autenticação do usuário com campos para email e senha  
- **[login.css](../../src/views/css/login.css):** Estilização da página de login com layout responsivo

##### Adicionar ambiente:
- **[add-ambient.ejs](../../src/views/pages/add-ambient.ejs):** Formulário para cadastro de novos ambientes/áreas  
- **[add-ambient.css](../../src/views/css/add-ambient.css):** Estilos específicos para o formulário de cadastro

##### Detalhes do ambiente:
- **[ambient-details.ejs](../../src/views/pages/ambient-details.ejs):** Página com informações detalhadas sobre um ambiente específico  
- **[ambient-details.css](../../src/views/css/ambient-details.css):** Design da página de detalhes com seções organizadas

##### Home:
- **[home.ejs](../../src/views/pages/home.ejs):** Página principal do sistema após login  
- **[home.css](../../src/views/css/home.css):** Estilização da Página principal e componentes principais

##### Detalhes da inspeção:
- **[inspection-details.ejs](../../src/views/pages/inspection-details.ejs):** Visualização completa dos dados de uma inspeção  
- **[inspection-details.css](../../src/views/css/inspection-details.css):** Layout para apresentação de relatórios de inspeção

##### Adicionar ocorrência:
- **[add-occurrence.ejs](../../src/views/pages/add-occurrence.ejs):** Formulário para registro de novas ocorrências  
- **[add-occurrence.css](../../src/views/css/add-occurrence.css):** Estilos do formulário de ocorrências com validação visual.

---
#### Models
Foram desenvolvidos os arquivos de modelo para cada entidade do sistema (ex: usuarioModel.js, inspecaoModel.js, etc.). Estes modelos são responsáveis pela interação direta com o banco de dados, contendo a lógica para realizar operações CRUD (Criar, Ler, Atualizar, Deletar) e outras consultas específicas.

As models podem ser encontradas em:

- **[ambientesModel.js](../../src/models/ambientesModel.js):** Gerencia a tabela de ambientes, com métodos para cadastro, edição e consulta de locais vistoriáveis  
- **[enderecosModel.js](../../src/models/enderecosModel.js):** Responsável pelo CRUD de endereços físicos vinculados aos ambientes  
- **[equipesModel.js](../../src/models/equipesModel.js):** Controla as operações relacionadas a grupos de inspeção e usuários associados  
- **[fotosModel.js](../../src/models/fotosModel.js):** Manipula o armazenamento e recuperação de imagens das vistorias e ocorrências  
- **[inspecoesModel.js](../../src/models/inspecoesModel.js):** Contém a lógica principal de criação e gerenciamento de inspeções  
- **[ocorrenciasModel.js](../../src/models/ocorrenciasModel.js):** Gerencia o registro e histórico de ocorrências identificadas nas vistorias  
- **[relatoriosModel.js](../../src/models/relatoriosModel.js):** Responsável pela geração e armazenamento de relatórios consolidados  
- **[usuariosModel.js](../../src/models/usuariosModel.js):** Controla todas as operações relacionadas a usuários do sistema (autenticação, permissões, etc.)

---
#### Routes
Foram criados os arquivos de rotas para cada recurso da API (ex: usuariosRoutes.js, inspecoesRoutes.js, authRoutes.js, etc.). Estes arquivos definem os endpoints da API, os métodos HTTP associados e direcionam as requisições para as funções correspondentes nos controllers. Um arquivo `routes/index.js` foi utilizado para agregar e gerenciar todas as rotas da aplicação.

As rotas podem ser encontradas em:

- **[ambientesRoutes.js](../../src/routes/ambientesRoutes.js):** Define endpoints para gestão de ambientes (CRUD, listagem, filtros)  
- **[apiRoutes.js](../../src/routes/apiRoutes.js):** Configuração base da API e rotas globais  
- **[authRoutes.js](../../src/routes/authRoutes.js):** Rotas de autenticação (login, logout, recuperação de senha)  
- **[enderecosRoutes.js](../../src/routes/enderecosRoutes.js):** Endpoints para manipulação de endereços vinculados a ambientes  
- **[equipesRoutes.js](../../src/routes/equipesRoutes.js):** Rotas para gerenciamento de equipes de inspeção  
- **[fotosRoutes.js](../../src/routes/fotosRoutes.js):** Endpoints para upload/download de imagens das vistorias  
- **[index.js](../../src/routes/index.js):** Arquivo central que unifica todas as rotas da aplicação  
- **[inspecoesRoutes.js](../..src/routes/inspecoesRoutes.js):** Rotas principais para criação e consulta de inspeções  
- **[ocorrenciasRoutes.js](../../src/routes/ocorrenciasRoutes.js):** Endpoints para registro e acompanhamento de ocorrências  
- **[relatoriosRoutes.js](../../src/routes/relatoriosRoutes.js):** Rotas para geração e exportação de relatórios  
- **[usuariosRoutes.js](../../src/routes/usuariosRoutes.js):** Gerenciamento de usuários (cadastro, perfil, permissões)

---
#### Controllers
Foram implementados os arquivos de controller (ex: usuarioController.js, inspecaoController.js, etc.). Os controllers recebem as requisições das rotas, processam os dados de entrada, interagem com os modelos para operações de banco de dados e preparam as respostas a serem enviadas de volta ao cliente.

Os controllers podem ser encontrados em:

- **[ambientesController.js](../../src/controllers/ambientesController.js):** Controla a lógica de negócio relacionada a ambientes, incluindo validações e processamento de dados  
- **[authController.js](../../src/controllers/authController.js):** Gerencia autenticação, autorização e segurança de acesso ao sistema  
- **[enderecosController.js](../../src/controllers/enderecosController.js):** Processa operações relacionadas a endereços físicos e suas associações  
- **[equipesController.js](../../src/controllers/equipesController.js):** Implementa a lógica para formação e gestão de equipes de inspeção  
- **[fotosController.js](../../src/controllers/fotosController.js):** Manipula o armazenamento, recuperação e processamento de imagens das vistorias  
- **[inspecoesController.js](../../src/controllers/inspecoesController.js):** Contém a lógica principal para criação, edição e consulta de inspeções  
- **[ocorrenciasController.js](../../src/controllers/ocorrenciasController.js):** Gerencia o fluxo de registro, classificação e tratamento de ocorrências  
- **[relatoriosController.js](../../src/controllers/relatoriosController.js):** Responsável pela geração e formatação de relatórios analíticos  
- **[usuariosController.js](../../src/controllers/usuariosController.js):** Controla todas as operações relacionadas a gestão de usuários e perfis

---
#### Configuração do Servidor (server.js)
O arquivo principal da aplicação backend foi desenvolvido para configurar o servidor Express.js. Isso inclui a inicialização do servidor, configuração de middlewares essenciais (como express.json para parsing de JSON), e a montagem do roteador principal da API.

O arquivo de configuração principal pode ser encontrado em:

- **[server.js](../../src/server.js):** Arquivo central que realiza a configuração e inicialização do servidor backend.

---
## Design

### Guia de Estilos (Style Guide)
Foi desenvolvido um guia de estilos detalhado para garantir a consistência visual e a identidade da marca em toda a aplicação. Este guia define a paleta de cores, tipografia, componentes de interface, espaçamentos, iconografia e outros elementos visuais.

---

#### Tipografia
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 1</strong> – Tipografia </p>
    <img src="../../assets/tipografia.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
#### Cores
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 2</strong> – Cores</p>
    <img src="../../assets/cores.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---

#### Iconografia
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 3</strong> – Iconografia</p>
    <img src="../../assets/iconografia.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---

### Protótipo de Alta Fidelidade
Foi criado um protótipo de alta fidelidade da interface do usuário (UI). Este protótipo simula a aparência final e a interatividade da aplicação, servindo como referência visual para o desenvolvimento frontend e validação da experiência do usuário (UX).

#### Telas Principais:

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 4</strong> – Tela Login</p>
    <img src="../../assets/ux/telaLogin.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---

<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 5</strong> - Tela Inicial</p>
    <img src="../../assets/ux/telaHome.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 6</strong> – Tela de detalhes das Inspeções</p>
    <img src="../../assets/ux/telaDetalhesInspecao.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 7</strong> – Tela de detalhes dos ambientes</p>
    <img src="../../assets/ux/telaDetalhesAmbientes.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 8</strong> – Tela de detalhes das ocorrências</p>
    <img src="../../assets/ux/telaDetalhesOcorrencias.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 9</strong> –Tela de Perfil</p>
    <img src="../../assets/ux/telaPerfil.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 10</strong> – Tela de  lista de relatórios  </p>
    <img src="../../assets/ux/telaRelatorios.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 11</strong> – Tela de visualização dos relatórios</p>
    <img src="../../assets/ux/telaRelatorio.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 12</strong> – Tela de Adição das ocorrências </p>
    <img src="../../assets/ux/telaAdicionarOcorrencia.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---
<div align="center" style="margin-bottom: 1em;">
    <p style="margin-bottom: 0.3em; font-style: italic;"><strong>Figura 13</strong> – Tela de adição dos ambientes</p>
    <img src="../../assets/ux/telaAdicionarAmbiente.png">
    <p style="margin-top: 0.3em; font-size: 0.9em; font-style: italic;">
        Fonte: Grupo BugBusters, Faculdade Inteli 2025.
    </p>
</div>

---

## Dificuldades Encontradas

Ao longo do desenvolvimento, a equipe enfrentou alguns desafios que foram importantes para o aprendizado e evolução do projeto:

1. **Definição Inicial do Escopo**  
   Conciliar a visão abrangente do projeto com os prazos das sprints, exigindo priorização e foco nas funcionalidades essenciais.

2. **Modelagem do Banco de Dados**  
   Garantir que o esquema fosse flexível o suficiente para acomodar diversas tipologias construtivas, mantendo ao mesmo tempo a integridade e desempenho das consultas.

3. **Atribuição Eficaz de Tasks**  
   Distribuir tarefas equilibradas entre os membros da equipe, considerando complexidade e tempo de desenvolvimento.

---

## Próximos Passos

Para as futuras iterações e finalização do projeto InsPecT, planeja-se:

1. **Desenvolvimento das telas de administrador e Integração do Frontend**  
   - Implementar o protótipo de alta fidelidade utilizando EJS
   - Conectar com os endpoints da API backend
   - Garantir responsividade e boa UX em diferentes dispositivos

2. **Testes e Validações**  
   - Realizar testes de usabilidade com usuários reais
   - Ajustar fluxos de navegação conforme feedback

3. **Documentação Final**  
   - Completar a documentação técnica
   



