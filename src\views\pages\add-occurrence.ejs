<link rel="stylesheet" href="/css/add-occurrence.css" />
<link rel="stylesheet" href="/css/toast.css" />

<div class="form-page-container">
    <div class="form-wrapper">
        <h1 class="h4 form-title">Adicionar ocorrência</h1>

        <div id="messageArea" class="message-area"></div>

        <form id="addOccurrenceForm" class="form-body">
            
            <div class="form-group">
                <label for="inspecaoSelect" class="form-label">Inspeção</label>
                <select id="inspecaoSelect" name="inspecao" required>
                    <option value="" disabled selected>Carregando inspeções...</option>
                </select>
            </div>
            
            <div class="form-group" id="ambienteGroup" style="display: none;">
                <label for="ambienteSelect" class="form-label">Local (Ambiente)</label>
                <select id="ambienteSelect" name="ambiente" required disabled>
                    <option value="">Selecione primeiro uma inspeção</option>
                </select>
            </div>
            
            <hr class="form-divider grid-span-2">

            <div class="form-group grid-span-2">
                <label for="imageUpload" class="upload-area">
                    <div class="upload-icons">
                        <div class="icon-wrapper">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg>
                            <span>Abrir câmera</span>
                        </div>
                        <div class="icon-wrapper">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>
                            <span>Adicionar imagem</span>
                        </div>
                    </div>
                    <span class="upload-text">Escolha ou arraste uma imagem aqui</span>
                    <input type="file" id="imageUpload" name="fotos" accept="image/*" multiple>
                </label>
            </div>

            <div class="form-group">
                <label for="title" class="form-label">Título</label>
                <input type="text" id="title" name="title" placeholder="Insira o título da ocorrência" required>
            </div>
            
            <div class="form-group">
                <label for="dataOcorrencia" class="form-label">Data da ocorrência</label>
                <input type="date" id="dataOcorrencia" name="dataOcorrencia" required>
            </div>

            <div class="form-group grid-span-2">
                <label for="description" class="form-label">Descrição</label>
                <textarea id="description" name="description" placeholder="Descreva o problema aqui"></textarea>
            </div>

            <div class="form-group grid-span-2">
                <label class="form-label">Tipo de sistema</label>
                <div class="tags-container" id="system-type-tags">
                    <button type="button" class="btn-add-new" disabled>+ Adicionar novo</button>
                    <span class="loading-text">Carregando...</span>
                </div>
            </div>

            <div class="form-group grid-span-2">
                <label class="form-label">Patologia</label>
                <div class="pathology-search-container">
                    <div class="search-bar">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                        <input type="text" id="pathologySearch" placeholder="Pesquisar">
                    </div>
                    <button type="button" class="btn-add-new-square">+</button>
                </div>
                <p class="selection-prompt">Selecione a que mais se aplica:</p>
                <div class="tags-container-grid" id="pathology-tags">
                     <span class="loading-text">Carregando...</span>
                </div>
            </div>

             <div class="form-footer grid-span-2">
                <button type="submit" class="btn-primary">Adicionar</button>
            </div>
        </form>
    </div>
</div>

<!-- Popup para adicionar novo tipo de sistema -->
<div id="systemTypeModal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Adicionar Novo Tipo de Sistema</h3>
            <button type="button" class="modal-close" onclick="closeSystemTypeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="systemTypeForm">
                <div class="form-group">
                    <label for="systemTypeName" class="form-label">Nome do Tipo de Sistema</label>
                    <input type="text" id="systemTypeName" name="nome" placeholder="Ex: Estrutural, Elétrico, Hidráulico..." required>
                </div>
                <div class="form-group">
                    <label for="systemTypeDescription" class="form-label">Descrição (opcional)</label>
                    <textarea id="systemTypeDescription" name="descricao" placeholder="Descrição do tipo de sistema..."></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeSystemTypeModal()">Cancelar</button>
            <button type="button" class="btn-primary" onclick="saveSystemType()">Salvar</button>
        </div>
    </div>
</div>

<!-- Popup para adicionar novo tipo de patologia -->
<div id="pathologyTypeModal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Adicionar Novo Tipo de Patologia</h3>
            <button type="button" class="modal-close" onclick="closePathologyTypeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="pathologyTypeForm">
                <div class="form-group">
                    <label for="pathologyTypeName" class="form-label">Nome da Patologia</label>
                    <input type="text" id="pathologyTypeName" name="nome" placeholder="Ex: Fissura, Infiltração, Corrosão..." required>
                </div>
                <div class="form-group">
                    <label for="pathologyTypeDescription" class="form-label">Descrição (opcional)</label>
                    <textarea id="pathologyTypeDescription" name="descricao" placeholder="Descrição da patologia..."></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closePathologyTypeModal()">Cancelar</button>
            <button type="button" class="btn-primary" onclick="savePathologyType()">Salvar</button>
        </div>
    </div>
</div>

<script src="/js/toast.js"></script>
<script src="/js/add-occurrence.js"></script>