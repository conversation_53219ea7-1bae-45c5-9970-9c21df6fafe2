header {
    display: none;
}

main {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
}

.recover-password-container {
    background-color: var(--card-color);
    color: var(--background-color);
    padding: 24px;
    border-radius: 12px;
    min-width: 300px;
    max-width: 400px;
    width: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.logoIPT {
    align-self: center;
}

.logoIPT img {
    height: 92px;
    margin: auto;
    display: block;
}

.RecuperarSenha {
    text-align: left;
    margin-top: 20px;
    margin-bottom: 20px;
}

h1 {
    font-size: 24px;
    color: var(--foreground-color);
    margin-bottom: 8px;
}

p {
    font-size: 14px;
    color: var(--muted-foreground-color);
}

.input-group {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid var(--muted-color);
    border-radius: 8px;
}

.input-group img {
    width: 20px;
    height: 20px;
}

.input-group input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 14px;
    background-color: transparent;
    color: var(--foreground-color);
}

.btn-entrar {
    background-color: var(--primary-color);
    color: var(--background-color);
    border: none;
    padding: 12px;
    width: 100%;
    border-radius: 8px;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-entrar:hover {
    background-color: var(--primary-color-hover, #003f75);
}

.btn-voltar {
    display: block;
    text-align: center;
    margin-top: 12px;
    padding: 12px;
    border: 2px solid var(--primary-color);
    background-color: transparent;
    color: var(--primary-color);
    border-radius: 8px;
    font-weight: bold;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.btn-voltar:hover {
    background-color: rgba(0, 74, 135, 0.05);
}
