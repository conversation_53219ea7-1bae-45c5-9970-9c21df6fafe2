.popup-shadow.active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.35);
  z-index: 9999;
  transition: opacity 300ms ease-in-out;
  opacity: 1;
  pointer-events: auto;
}

.popup-shadow {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.35);
  z-index: 9999;
  opacity: 0;
  transition: opacity 300ms ease-in-out;
  pointer-events: none;
}

.sidebar-container.active {
  position: fixed;
  top: 0;
  left: 0;
  width: 260px;
  height: 100vh;
  background-color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  transition: all 500ms ease-in-out;
}

.sidebar-container {
  position: fixed;
  top: 0;
  left: -260px;
  width: 260px;
  height: 100vh;
  background-color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  transition: all 500ms ease-in-out;
}

/* Sidebar no modo escuro - com maior especificidade */
[data-theme="dark"] .sidebar-container {
  background-color: #1f2937 !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
  border-right: 1px solid #374151 !important;
}

[data-theme="dark"] .sidebar-container.active {
  background-color: #1f2937 !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
  border-right: 1px solid #374151 !important;
}

.sidebar-container .top {
  display: flex;
  gap: 1em;
  padding: 32px 24px;
  align-items: center;
  color: #ffffff;
  font-size: 14px;
  background: linear-gradient(135deg, #00558C 0%, #004070 100%);
  transition: all 0.3s ease;
}

/* Top da sidebar no modo escuro - com maior especificidade */
[data-theme="dark"] .sidebar-container .top {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
  color: #ffffff !important;
  border-bottom: 1px solid #374151 !important;
}

.sidebar-container .top img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
}

.sidebar-container .top .user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-container .top .user-info h3 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.sidebar-container .top .user-info p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
}

/* User info no modo escuro */
[data-theme="dark"] .sidebar-container .top .user-info h3 {
  color: #ffffff !important;
}

[data-theme="dark"] .sidebar-container .top .user-info p {
  color: rgba(255, 255, 255, 0.7) !important;
}

.sidebar-container nav {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-container nav button {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  min-height: 45px;
  background: transparent;
  color: #374151;
  transition: all 0.3s ease;
}

.sidebar-container nav button:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #00558C;
}

.sidebar-container nav button.active {
  background: #e2e8f0;
  color: #1e293b;
}

/* Botões da sidebar no modo escuro - com maior especificidade */
[data-theme="dark"] .sidebar-container nav button {
  color: #d1d5db !important;
  background: transparent !important;
}

[data-theme="dark"] .sidebar-container nav button:hover {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .sidebar-container nav button.active {
  background: #374151 !important;
  color: #ffffff !important;
}

/* Ícones da sidebar no modo escuro */
[data-theme="dark"] .sidebar-container nav button img {
  filter: brightness(1.2) contrast(1.1) !important;
}

/* Regra global para garantir que o modo escuro funcione na sidebar */
body[data-theme="dark"] .sidebar-container,
body[data-theme="dark"] .sidebar-container.active {
  background-color: #1f2937 !important;
  color: #ffffff !important;
}

body[data-theme="dark"] .sidebar-container .top {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
  color: #ffffff !important;
  border-bottom: 1px solid #374151 !important;
}

body[data-theme="dark"] .sidebar-container nav button {
  color: #d1d5db !important;
}

body[data-theme="dark"] .sidebar-container nav button:hover {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #ffffff !important;
}

body[data-theme="dark"] .sidebar-container nav button.active {
  background: #374151 !important;
  color: #ffffff !important;
}

/* Corrigir a classe bg-primary no modo escuro */
[data-theme="dark"] .sidebar-container .top.bg-primary,
body[data-theme="dark"] .sidebar-container .top.bg-primary {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
  color: #ffffff !important;
  border-bottom: 1px solid #374151 !important;
}

/* Corrigir textos com classes específicas no modo escuro */
[data-theme="dark"] .sidebar-container .name,
[data-theme="dark"] .sidebar-container .email,
[data-theme="dark"] .sidebar-container .teams,
body[data-theme="dark"] .sidebar-container .name,
body[data-theme="dark"] .sidebar-container .email,
body[data-theme="dark"] .sidebar-container .teams {
  color: #ffffff !important;
}

[data-theme="dark"] .sidebar-container .text-muted,
body[data-theme="dark"] .sidebar-container .text-muted {
  color: rgba(255, 255, 255, 0.7) !important;
}