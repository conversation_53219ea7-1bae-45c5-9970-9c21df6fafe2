const UsuarioModel = require('../models/usuariosModel');
const bcrypt = require('bcryptjs');

class UsuariosService {
    async listarUsuarios() {
        try {
            return await UsuarioModel.findAll();
        } catch (error) {
            console.error('[USUARIOS_SERVICE_ERROR] listarUsuarios:', error);
            throw new Error('Erro ao listar usuários.');
        }
    }

    async obterUsuarioPorId(id_usuario) {
        try {
            const usuario = await UsuarioModel.findById(id_usuario);
            if (!usuario) {
                throw new Error('Usuário não encontrado.');
            }
            return usuario;
        } catch (error) {
            console.error('[USUARIOS_SERVICE_ERROR] obterUsuarioPorId:', error);
            throw error;
        }
    }

    validarDadosCriacao(dadosUsuario) {
        const { nome, email, senha } = dadosUsuario;

        if (!nome || !email || !senha) {
            throw new Error('Nome, email e senha são obrigatórios.');
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error('Formato de email inválido.');
        }

        if (senha.length < 6) {
            throw new Error('Senha deve ter pelo menos 6 caracteres.');
        }

        return { nome, email, senha };
    }

    async criarUsuario(dadosUsuario) {
        try {
            const dadosValidados = this.validarDadosCriacao(dadosUsuario);

            const usuarioExistente = await UsuarioModel.findByEmail(dadosValidados.email);
            if (usuarioExistente) {
                throw new Error('Este email já está em uso.');
            }

            const senhaHash = await bcrypt.hash(dadosValidados.senha, 10);

            const novoUsuario = await UsuarioModel.create({
                ...dadosValidados,
                senha: senhaHash
            });

            const { senha: _, ...usuarioSemSenha } = novoUsuario;
            return usuarioSemSenha;
        } catch (error) {
            console.error('[USUARIOS_SERVICE_ERROR] criarUsuario:', error);

            if (error.message && error.message.includes('duplicate key value violates unique constraint "usuarios_email_key"')) {
                throw new Error('Este email já está em uso.');
            }

            throw error;
        }
    }

    validarDadosAtualizacao(dadosUsuario) {
        const { nome, email } = dadosUsuario;
        
        if (!nome && !email) {
            throw new Error('Pelo menos um campo (nome ou email) deve ser fornecido para atualização.');
        }

        if (email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                throw new Error('Formato de email inválido.');
            }
        }

        return { nome, email };
    }

    async atualizarUsuario(id_usuario, dadosUsuario) {
        try {
            
            const dadosValidados = this.validarDadosAtualizacao(dadosUsuario);

            await this.obterUsuarioPorId(id_usuario);

            if (dadosValidados.email) {
                const usuarioComEmail = await UsuarioModel.findByEmail(dadosValidados.email);
                if (usuarioComEmail && usuarioComEmail.id !== parseInt(id_usuario)) {
                    throw new Error('Este email já está em uso por outro usuário.');
                }
            }

            const usuarioAtualizado = await UsuarioModel.update(id_usuario, dadosValidados);
            if (!usuarioAtualizado) {
                throw new Error('Usuário não encontrado para atualização.');
            }

            return usuarioAtualizado;
        } catch (error) {
            console.error('[USUARIOS_SERVICE_ERROR] atualizarUsuario:', error);
            throw error;
        }
    }

    async deletarUsuario(id_usuario) {
        try {
            
            await this.obterUsuarioPorId(id_usuario);

            const deletado = await UsuarioModel.remove(id_usuario);
            if (!deletado) {
                throw new Error('Usuário não encontrado para deleção.');
            }

            return true;
        } catch (error) {
            console.error('[USUARIOS_SERVICE_ERROR] deletarUsuario:', error);
            throw error;
        }
    }

    async buscarPorEmail(email) {
        try {
            const usuario = await UsuarioModel.findByEmail(email);
            if (!usuario) {
                throw new Error('Usuário não encontrado.');
            }
            return usuario;
        } catch (error) {
            console.error('[USUARIOS_SERVICE_ERROR] buscarPorEmail:', error);
            throw error;
        }
    }
}

module.exports = new UsuariosService();