<link rel="stylesheet" href="/css/home.css" />

<div class="container">
  <div class="near">
    <h1 class="h4">Início</h1>
    <div class="actions">
      <button class="btn-outline" onclick="navigateTo('/reports')">Ver relatórios</button>
      <button class="btn-outline" onclick="navigateTo('/my-teams')">Ver equipes</button>
      <button class="btn-outline" onclick="navigateTo('/map')">Ver mapa</button>
      <button class="btn-primary" onclick="navigateTo('/add-occurrence')">Criar ocorrência</button>
    </div>
  </div>

  <h2 class="h5">Inspeções</h2>

  <div class="too near">
    <div class="tags">
      <span class="tag primary active" data-filter="todos">Todos</span>
      <span class="tag neutral" data-filter="aberta">Aberta</span>
      <span class="tag ongoing" data-filter="em-andamento">Em Andamento</span>
      <span class="tag success" data-filter="concluida">Concluída</span>
    </div>

    <div class="search-filter">
      <div class="search-bar">
        <input type="text" placeholder="Buscar" id="search-input" />
        <img src="/assets/icons/search-muted.svg" alt="Ícone de busca" />
      </div>
    </div>
  </div>

  <div class="inspection-list" id="inspection-list">
    <div class="loading-state">Carregando inspeções...</div>
  </div>
</div>

<script src="/js/home.js"></script>
