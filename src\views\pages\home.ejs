<link rel="stylesheet" href="/css/home.css" />

<div class="home-container">
  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-content">
      <div class="welcome-text">
        <h1 class="hero-title">
          <span class="gradient-text">Bem-vindo ao</span>
          <span class="brand-text">InsPecT</span>
        </h1>
        <p class="hero-subtitle">Gerencie suas inspeções de forma inteligente e eficiente</p>
      </div>
      <div class="hero-stats">
        <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
          <div class="stat-icon">📊</div>
          <div class="stat-number" id="total-inspections">0</div>
          <div class="stat-label">Inspeções</div>
        </div>
        <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
          <div class="stat-icon">🏗️</div>
          <div class="stat-number" id="active-inspections">0</div>
          <div class="stat-label">Ativas</div>
        </div>
        <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
          <div class="stat-icon">✅</div>
          <div class="stat-number" id="completed-inspections">0</div>
          <div class="stat-label">Concluídas</div>
        </div>
      </div>
    </div>
    <div class="hero-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions-section" data-aos="fade-up" data-aos-delay="400">
    <h2 class="section-title">Ações Rápidas</h2>
    <div class="actions-grid">
      <div class="action-card primary" onclick="navigateTo('/add-occurrence')" data-aos="zoom-in" data-aos-delay="500">
        <div class="action-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 5v14M5 12h14"/>
          </svg>
        </div>
        <h3>Criar Ocorrência</h3>
        <p>Registre uma nova ocorrência</p>
        <div class="action-arrow">→</div>
      </div>

      <div class="action-card" onclick="navigateTo('/reports')" data-aos="zoom-in" data-aos-delay="600">
        <div class="action-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
        </div>
        <h3>Relatórios</h3>
        <p>Visualize relatórios detalhados</p>
        <div class="action-arrow">→</div>
      </div>

      <div class="action-card" onclick="navigateTo('/map')" data-aos="zoom-in" data-aos-delay="700">
        <div class="action-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6"/>
            <line x1="8" y1="2" x2="8" y2="18"/>
            <line x1="16" y1="6" x2="16" y2="22"/>
          </svg>
        </div>
        <h3>Mapa</h3>
        <p>Localize inspeções no mapa</p>
        <div class="action-arrow">→</div>
      </div>

      <div class="action-card" onclick="navigateTo('/my-teams')" data-aos="zoom-in" data-aos-delay="800">
        <div class="action-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
            <circle cx="9" cy="7" r="4"/>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
          </svg>
        </div>
        <h3>Equipes</h3>
        <p>Gerencie suas equipes</p>
        <div class="action-arrow">→</div>
      </div>
    </div>
  </div>

  <!-- Inspections Section -->
  <div class="inspections-section" data-aos="fade-up" data-aos-delay="900">
    <div class="section-header">
      <h2 class="section-title">Suas Inspeções</h2>
      <div class="view-toggle">
        <button class="toggle-btn active" data-view="grid">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="7" height="7"/>
            <rect x="14" y="3" width="7" height="7"/>
            <rect x="14" y="14" width="7" height="7"/>
            <rect x="3" y="14" width="7" height="7"/>
          </svg>
        </button>
        <button class="toggle-btn" data-view="list">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="8" y1="6" x2="21" y2="6"/>
            <line x1="8" y1="12" x2="21" y2="12"/>
            <line x1="8" y1="18" x2="21" y2="18"/>
            <line x1="3" y1="6" x2="3.01" y2="6"/>
            <line x1="3" y1="12" x2="3.01" y2="12"/>
            <line x1="3" y1="18" x2="3.01" y2="18"/>
          </svg>
        </button>
      </div>
    </div>

    <div class="filters-container">
      <div class="filter-tags">
        <span class="filter-tag active" data-filter="todos">
          <span class="tag-icon">🔍</span>
          Todos
        </span>
        <span class="filter-tag" data-filter="aberta">
          <span class="tag-icon">📋</span>
          Abertas
        </span>
        <span class="filter-tag" data-filter="em-andamento">
          <span class="tag-icon">⚡</span>
          Em Andamento
        </span>
        <span class="filter-tag" data-filter="concluida">
          <span class="tag-icon">✅</span>
          Concluídas
        </span>
      </div>

      <div class="search-container">
        <div class="search-input-wrapper">
          <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
          </svg>
          <input type="text" placeholder="Buscar inspeções..." id="search-input" class="search-input" />
          <button class="search-clear" id="search-clear" style="display: none;">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div class="inspection-list grid-view" id="inspection-list">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">Carregando suas inspeções...</p>
      </div>
    </div>
  </div>
</div>

<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script src="/js/home.js"></script>
