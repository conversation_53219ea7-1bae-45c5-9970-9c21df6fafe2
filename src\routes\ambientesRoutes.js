const express = require("express");
const router = express.Router();
const ambienteController = require("../controllers/ambientesController");
const ocorrenciaController = require("../controllers/ocorrenciasController");
const fotoController = require("../controllers/fotosController");

router.get("/:id_ambiente", ambienteController.obterAmbientePorId);
router.put("/:id_ambiente", ambienteController.atualizarAmbiente);
router.delete("/:id_ambiente", ambienteController.deletarAmbiente);
router.get(
  "/:id_ambiente/ocorrencias",
  ocorrenciaController.listarOcorrenciasPorAmbiente
);
router.post(
  "/:id_ambiente/ocorrencias",
  ocorrenciaController.criarOcorrenciaNoAmbiente
);
router.get("/:id_ambiente/fotos", fotoController.listarFotosPorAmbiente);
router.post(
  "/:id_ambiente/fotos",
  fotoController.adicionarFotoAmbienteOcorrencia
);

// Rotas para afazeres do ambiente
router.get("/:id_ambiente/afazeres", ambienteController.listarAfazeresPorAmbiente);
router.put("/:id_ambiente/afazeres/:id_afazer", ambienteController.atualizarStatusAfazer);

module.exports = router;