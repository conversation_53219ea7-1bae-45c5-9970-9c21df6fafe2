const AmbienteModel = require('../models/ambientesModel');
const InspecaoModel = require('../models/inspecoesModel');
const AfazerModel = require('../models/afazeresModel');

class AmbientesService {
    
    async validarInspecaoExiste(id_inspecao) {
        try {
            const inspecao = await InspecaoModel.findById(id_inspecao);
            if (!inspecao) {
                throw new Error(`Inspeção com ID ${id_inspecao} não encontrada.`);
            }
            return inspecao;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] validarInspecaoExiste:', error);
            throw error;
        }
    }

    validarDadosCriacao(dadosAmbiente) {
        const { titulo, id_usuario, id_inspecao, id_ambiente_pai, observacoes } = dadosAmbiente;
        
        if (!titulo) {
            throw new Error('Título do ambiente é obrigatório.');
        }

        if (!id_inspecao) {
            throw new Error('ID da inspeção é obrigatório.');
        }

        if (titulo.trim().length === 0) {
            throw new Error('Título do ambiente não pode estar vazio.');
        }

        return {
            titulo: titulo.trim(),
            id_usuario,
            id_inspecao: parseInt(id_inspecao),
            id_ambiente_pai,
            observacoes
        };
    }

    async listarAmbientesPorInspecao(id_inspecao) {
        try {
            
            await this.validarInspecaoExiste(id_inspecao);

            const ambientes = await AmbienteModel.findAllByInspecaoId(id_inspecao);
            return ambientes;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] listarAmbientesPorInspecao:', error);
            throw error;
        }
    }

    async criarAmbienteNaInspecao(id_inspecao, dadosAmbiente) {
        try {
            
            await this.validarInspecaoExiste(id_inspecao);

            const dadosValidados = this.validarDadosCriacao({
                ...dadosAmbiente,
                id_inspecao
            });

            if (dadosValidados.id_ambiente_pai) {
                const ambientePai = await AmbienteModel.findById(dadosValidados.id_ambiente_pai);
                if (!ambientePai) {
                    throw new Error('Ambiente pai não encontrado.');
                }

                if (ambientePai.id_inspecao !== dadosValidados.id_inspecao) {
                    throw new Error('Ambiente pai deve pertencer à mesma inspeção.');
                }
            }

            const novoAmbiente = await AmbienteModel.create(dadosValidados);
            return novoAmbiente;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] criarAmbienteNaInspecao:', error);
            throw error;
        }
    }

    async obterAmbientePorId(id_ambiente) {
        try {
            const ambiente = await AmbienteModel.findById(id_ambiente);
            if (!ambiente) {
                throw new Error('Ambiente não encontrado.');
            }
            return ambiente;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] obterAmbientePorId:', error);
            throw error;
        }
    }

    validarDadosAtualizacao(dadosAmbiente) {
        const { titulo, id_usuario, id_ambiente_pai, observacoes } = dadosAmbiente;
        
        if (!titulo) {
            throw new Error('Título do ambiente é obrigatório.');
        }

        if (titulo.trim().length === 0) {
            throw new Error('Título do ambiente não pode estar vazio.');
        }

        return {
            titulo: titulo.trim(),
            id_usuario,
            id_ambiente_pai,
            observacoes
        };
    }

    async atualizarAmbiente(id_ambiente, dadosAmbiente) {
        try {
            
            const ambienteExistente = await this.obterAmbientePorId(id_ambiente);

            const dadosValidados = this.validarDadosAtualizacao(dadosAmbiente);

            if (dadosValidados.id_ambiente_pai) {
                const ambientePai = await AmbienteModel.findById(dadosValidados.id_ambiente_pai);
                if (!ambientePai) {
                    throw new Error('Ambiente pai não encontrado.');
                }

                if (ambientePai.id_inspecao !== ambienteExistente.id_inspecao) {
                    throw new Error('Ambiente pai deve pertencer à mesma inspeção.');
                }

                if (parseInt(dadosValidados.id_ambiente_pai) === parseInt(id_ambiente)) {
                    throw new Error('Um ambiente não pode ser pai de si mesmo.');
                }
            }

            const ambienteAtualizado = await AmbienteModel.update(id_ambiente, dadosValidados);
            if (!ambienteAtualizado) {
                throw new Error('Ambiente não encontrado para atualização.');
            }

            return ambienteAtualizado;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] atualizarAmbiente:', error);
            throw error;
        }
    }

    async verificarPodeDeletar(id_ambiente) {
        try {
            
            const ambientesFilhos = await AmbienteModel.findAllByAmbientePai(id_ambiente);
            if (ambientesFilhos && ambientesFilhos.length > 0) {
                throw new Error('Não é possível deletar ambiente que possui sub-ambientes. Delete os sub-ambientes primeiro.');
            }

            return true;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] verificarPodeDeletar:', error);
            throw error;
        }
    }

    async deletarAmbiente(id_ambiente) {
        try {
            
            await this.obterAmbientePorId(id_ambiente);

            await this.verificarPodeDeletar(id_ambiente);

            const deletado = await AmbienteModel.remove(id_ambiente);
            if (!deletado) {
                throw new Error('Ambiente não encontrado para deleção.');
            }

            return true;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] deletarAmbiente:', error);
            throw error;
        }
    }

    async listarAmbientesHierarquicos(id_inspecao) {
        try {
            const ambientes = await this.listarAmbientesPorInspecao(id_inspecao);

            const ambientesRaiz = ambientes.filter(amb => !amb.id_ambiente_pai);
            const ambientesFilhos = ambientes.filter(amb => amb.id_ambiente_pai);
            
            const organizarHierarquia = (ambientePai) => {
                const filhos = ambientesFilhos.filter(filho => 
                    filho.id_ambiente_pai === ambientePai.id
                );
                
                return {
                    ...ambientePai,
                    filhos: filhos.map(organizarHierarquia)
                };
            };
            
            return ambientesRaiz.map(organizarHierarquia);
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] listarAmbientesHierarquicos:', error);
            throw error;
        }
    }

    async listarAfazeresPorAmbiente(id_ambiente) {
        try {
            // Verificar se o ambiente existe
            const ambiente = await AmbienteModel.findById(id_ambiente);
            if (!ambiente) {
                throw new Error('Ambiente não encontrado.');
            }

            const afazeres = await AfazerModel.findByAmbienteId(id_ambiente);
            return afazeres;
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] listarAfazeresPorAmbiente:', error);
            throw error;
        }
    }

    async atualizarStatusAfazer(id_ambiente, id_afazer, concluido) {
        try {
            // Verificar se o ambiente existe
            const ambiente = await AmbienteModel.findById(id_ambiente);
            if (!ambiente) {
                throw new Error('Ambiente não encontrado.');
            }

            // Verificar se o afazer existe
            const afazeres = await AfazerModel.findAll();
            const afazerExiste = afazeres.find(a => a.id === parseInt(id_afazer));
            if (!afazerExiste) {
                throw new Error('Afazer não encontrado.');
            }

            const resultado = await AfazerModel.updateStatusAmbiente(id_afazer, id_ambiente, concluido);
            return {
                id_afazer: parseInt(id_afazer),
                id_ambiente: parseInt(id_ambiente),
                concluido: concluido,
                titulo: afazerExiste.titulo
            };
        } catch (error) {
            console.error('[AMBIENTES_SERVICE_ERROR] atualizarStatusAfazer:', error);
            throw error;
        }
    }
}

module.exports = new AmbientesService();