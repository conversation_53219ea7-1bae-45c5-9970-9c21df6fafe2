<header>
  <% if (useBackButton) { %>
    <button onclick="goBack()">
      <img src="/assets/icons/arrow-left-white.svg" alt="back button icon">
    </button>
  <% } else { %>
    <div class="left">
      <img onclick="openSidebar()" src="/assets/icons/menu-white.svg" alt="menu icon">
      <img src="/assets/logo-white.png" alt="Logo da IPT" class="logoIPT">
    </div>
  <% } %>

  <div class="right">
    <!-- Toggle de Tema -->
    <button class="theme-toggle-btn" onclick="toggleGlobalTheme()" title="Alternar tema">
      <span class="theme-icon-global">🌙</span>
    </button>

    <% if (useBackButton) { %>
      <button onclick="navigateTo('/home')">
        <img src="/assets/icons/home-white.svg" alt="home icon">
      </button>
      <% } else { %>
        <button onclick="navigateTo('/profile')">
          <img src="/assets/icons/circle-user-white.svg" alt="user icon">
        </button>
    <% } %>
  </div>
</header>

<script>
  function navigateTo(url) {
    window.location.href = url;
  }

  function goBack() {
    window.history.back();
  }

  function openSidebar() {
    document.querySelector('.popup-shadow').classList.add('active');
    document.querySelector('.sidebar-container').classList.add('active');
  }

  // ===== SISTEMA DE TEMA GLOBAL =====

  // Inicializar tema ao carregar a página
  document.addEventListener('DOMContentLoaded', function() {
    initializeGlobalTheme();
  });

  // Inicializar tema global
  function initializeGlobalTheme() {
    const savedTheme = localStorage.getItem('global-theme') || 'light';
    applyGlobalTheme(savedTheme);
  }

  // Alternar tema global
  function toggleGlobalTheme() {
    const currentTheme = document.body.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    applyGlobalTheme(newTheme);
    localStorage.setItem('global-theme', newTheme);
  }

  // Aplicar tema global
  function applyGlobalTheme(theme) {
    const body = document.body;
    const themeIcon = document.querySelector('.theme-icon-global');

    if (theme === 'dark') {
      body.setAttribute('data-theme', 'dark');
      if (themeIcon) themeIcon.textContent = '☀️';
    } else {
      body.removeAttribute('data-theme');
      if (themeIcon) themeIcon.textContent = '🌙';
    }

    // Atualizar gráficos se existirem (para dashboard)
    if (typeof updateChartTheme === 'function' && typeof dashboardState !== 'undefined') {
      updateChartTheme(theme);
    }
  }
</script>