const db = require('../config/db');
const Ocorrencia = {

  async create({ titulo, id_inspecao, id_ambiente, id_tipo_sistema, id_tipo_patologia, descricao, concluido_em }) {

    const queryText = `
      INSERT INTO ocorrencias (titulo, id_inspecao, id_ambiente, id_tipo_sistema, id_tipo_patologia, descricao, concluido_em)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *;
    `;
    const params = [titulo, id_inspecao, id_ambiente, id_tipo_sistema, id_tipo_patologia, descricao, concluido_em];
    
    try {
      const { rows } = await db.query(queryText, params);
      return rows[0];
    } catch (error) {
      console.error('Erro ao criar ocorrência no modelo:', error);
      throw error;
    }
  },
   
  async findAllByAmbienteId(id_ambiente) {
  
    const queryText = `
      SELECT 
        o.*, 
        u.nome AS "criadoPor"
      FROM 
        ocorrencias o
      LEFT JOIN 
        ambientes a ON o.id_ambiente = a.id
      LEFT JOIN 
        usuarios u ON a.id_usuario = u.id
      WHERE 
        o.id_ambiente = $1 
      ORDER BY 
        o.criado_em DESC;
    `;

    try {
      const { rows } = await db.query(queryText, [id_ambiente]);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar ocorrências por ID do ambiente:', error);
      throw error;
    }
  },

  async findById(id) {

    const queryText = `
      SELECT 
        o.*, 
        u.nome AS "criado_por"
      FROM 
        ocorrencias o
      LEFT JOIN 
        ambientes a ON o.id_ambiente = a.id
      LEFT JOIN 
        usuarios u ON a.id_usuario = u.id
      WHERE 
        o.id = $1;
    `;

    try {
      const { rows } = await db.query(queryText, [id]);
      return rows[0]; 
    } catch (error) {
      console.error('Erro ao buscar ocorrência detalhada por ID:', error);
      throw error;
    }
  },

  async update(id, { titulo, id_tipo_sistema, id_tipo_patologia, descricao, concluido_em, dataocm }) {

    const query = `
      UPDATE ocorrencias
      SET titulo = $1, id_tipo_sistema = $2, id_tipo_patologia = $3, descricao = $4, concluido_em = $5, dataocm = $6
      WHERE id = $7
      RETURNING *;
    `;
    try {
      const { rows } = await db.query(query, [titulo, id_tipo_sistema, id_tipo_patologia, descricao, concluido_em, dataocm, id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao atualizar ocorrência:', error);
      throw error;
    }
  },

  async findAllByInspecaoId(id_inspecao) {
    const queryText = `
      SELECT
        o.*,
        u.nome AS "criado_por",
        a.titulo AS "ambiente_titulo"
      FROM
        ocorrencias o
      LEFT JOIN
        ambientes a ON o.id_ambiente = a.id
      LEFT JOIN
        usuarios u ON a.id_usuario = u.id
      WHERE
        o.id_inspecao = $1
      ORDER BY
        o.criado_em DESC;
    `;

    try {
      const { rows } = await db.query(queryText, [id_inspecao]);
      return rows;
    } catch (error) {
      console.error('Erro ao buscar ocorrências por ID da inspeção:', error);
      throw error;
    }
  },

  async remove(id) {

    const query = 'DELETE FROM ocorrencias WHERE id = $1 RETURNING id;';
    try {
      const { rows } = await db.query(query, [id]);
      return rows[0];
    } catch (error) {
      console.error('Erro ao remover ocorrência:', error);
      throw error;
    }
  }
};

module.exports = Ocorrencia;