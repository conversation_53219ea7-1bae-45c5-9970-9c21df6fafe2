{"name": "inspect", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:coverage": "jest --coverage --coverageProvider=v8", "init-db": "node scripts/runSQLScript.js"}, "jest": {"collectCoverageFrom": ["src/models/**/*.js"]}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^10.0.0", "ejs": "^3.1.10", "express": "^4.21.2", "html-pdf": "^3.0.1", "html-pdf-node": "^1.0.8", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "pdfkit": "^0.17.1", "pg": "^8.7.1", "puppeteer": "^24.10.2", "puppeteer-core": "^24.10.2"}, "devDependencies": {"jest": "^27.5.1", "nodemon": "^2.0.22", "supertest": "^6.3.4"}, "directories": {"test": "tests"}, "repository": {"type": "git", "url": "git+https://github.com/Inteli-College/2025-1B-T16-IN02-G02.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Inteli-College/2025-1B-T16-IN02-G02/issues"}, "homepage": "https://github.com/Inteli-College/2025-1B-T16-IN02-G02#readme"}