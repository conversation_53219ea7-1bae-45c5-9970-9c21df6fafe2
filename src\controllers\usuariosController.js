const usuariosService = require('../services/usuariosService');

exports.listarUsuarios = async (req, res, next) => {
    try {
        const usuarios = await usuariosService.listarUsuarios();
        res.status(200).json(usuarios);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarUsuarios:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.obterUsuarioPorId = async (req, res, next) => {
    try {
        const { id_usuario } = req.params;
        const usuario = await usuariosService.obterUsuarioPorId(id_usuario);
        res.status(200).json(usuario);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterUsuarioPorId:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.criarUsuario = async (req, res, next) => {
    try {
        const novoUsuario = await usuariosService.criarUsuario(req.body);
        res.status(201).json(novoUsuario);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] criarUsuario:', error);
        const status = error.message.includes('já está em uso') ? 409 :
                      error.message.includes('obrigatórios') || error.message.includes('inválido') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.atualizarUsuario = async (req, res, next) => {
    try {
        const { id_usuario } = req.params;
        const usuarioAtualizado = await usuariosService.atualizarUsuario(id_usuario, req.body);
        res.status(200).json(usuarioAtualizado);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] atualizarUsuario:', error);
        const status = error.message.includes('não encontrado') ? 404 :
                      error.message.includes('obrigatório') || error.message.includes('inválido') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.deletarUsuario = async (req, res, next) => {
    try {
        const { id_usuario } = req.params;
        await usuariosService.deletarUsuario(id_usuario);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarUsuario:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};