const authService = require('../services/authService');

exports.loginUsuario = async (req, res) => {
    try {
        const resultado = await authService.loginUsuario(req.body);

        if (resultado.success) {
            res.status(200).json(resultado);
        } else {
            const status = resultado.message.includes('obrigatórios') || resultado.message.includes('inválido') ? 400 : 401;
            res.status(status).json(resultado);
        }
    } catch (error) {
        console.error('[CONTROLLER_ERROR] loginUsuario:', error);
        res.status(500).json({
            success: false,
            message: "Erro interno no servidor."
        });
    }
};