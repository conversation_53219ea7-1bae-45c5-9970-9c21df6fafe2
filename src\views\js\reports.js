async function buscarEnderecoPorCep(cep) {
  try {
    if (!cep) {
      return "Endereço não disponível";
    }

    const cepLimpo = cep.replace(/\D/g, '');
    if (cepLimpo.length !== 8) {
      return `CEP: ${cep}`;
    }

    const response = await fetch(`https://viacep.com.br/ws/${cepLimpo}/json/`);
    if (!response.ok) {
      throw new Error('Erro ao buscar CEP');
    }

    const data = await response.json();
    if (data.erro) {
      return `CEP: ${cep} (não encontrado)`;
    }

    let endereco = '';
    if (data.logradouro) {
      endereco += data.logradouro;
    }
    if (data.bairro) {
      endereco += endereco ? `, ${data.bairro}` : data.bairro;
    }
    if (data.localidade && data.uf) {
      endereco += endereco ? ` - ${data.localidade}/${data.uf}` : `${data.localidade}/${data.uf}`;
    }

    return endereco || `CEP: ${cep}`;
  } catch (error) {
    console.error('Erro ao buscar endereço:', error);
    return `CEP: ${cep} (erro ao carregar)`;
  }
}

async function fetchReports() {
  try {
    const token = localStorage.getItem("authToken");
    if (!token) {
      alert("Acesso negado. Por favor, faça o login.");
      window.location.href = "/";
      return;
    }

    const response = await fetch("/api/relatorios", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.status === 401) {
      window.location.href = "/";
      return;
    }

    if (!response.ok) {
      throw new Error(`Erro HTTP! Status: ${response.status}`);
    }

    const reports = await response.json();

    console.log("Dados recebidos do backend:", reports[0]);

    const reportList = document.getElementById("report-list");
    reportList.innerHTML = reports
      .map(
        (report, index) => `
          <div class="report-card" data-report-id="${report.relatorio_id}">
            <div class="top">
              <h3 class="subtitle1">${report.nome_inspecao || "Sem título"}</h3>
              <span class="status">
                <p class="body3 text-muted-foreground">${report.codigo_projeto || "Sem código"}</p>
              </span>
            </div>
            <div class="middle">
              <p class="text-muted-foreground body2" id="endereco-${report.relatorio_id}">
                <span class="loading-address">🔄 Carregando endereço...</span>
              </p>
            </div>
            <div class="bottom">
              <span class="date">
                <p class="body2 text-muted-foreground">${new Date(report.criado_em).toLocaleDateString("pt-BR") || "Sem data"}</p>
              </span>
            </div>
            <div class="card-actions">
              <button class="btn-card btn-preview" onclick="event.stopPropagation(); navigateTo('/report-details/${report.relatorio_id}')">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                Preview
              </button>
              <button class="btn-card btn-download" onclick="event.stopPropagation(); downloadReport(${report.relatorio_id}, event)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7,10 12,15 17,10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Download
              </button>
            </div>
          </div>
        `
      )
      .join("");

    await carregarEnderecos(reports);
  } catch (error) {
    console.error("Erro ao buscar relatórios:", error);
    document.getElementById(
      "report-list"
    ).innerHTML = `<p>Erro ao carregar relatórios. Tente novamente mais tarde.</p>`;
  }
}

async function carregarEnderecos(reports) {
  const promises = reports.map(async (report) => {
    const elementoEndereco = document.getElementById(`endereco-${report.relatorio_id}`);
    if (!elementoEndereco) return;

    try {
      let enderecoCompleto;

      if (report.endereco) {
        const cepLimpo = report.endereco.replace(/\D/g, '');
        if (cepLimpo.length === 8) {
          enderecoCompleto = await buscarEnderecoPorCep(report.endereco);
        } else {
          enderecoCompleto = `Endereço: ${report.endereco}`;
        }
      } else {
        enderecoCompleto = "Endereço não disponível";
      }

      elementoEndereco.innerHTML = enderecoCompleto;
      elementoEndereco.classList.remove('loading-address');
    } catch (error) {
      console.error(`Erro ao carregar endereço para relatório ${report.relatorio_id}:`, error);
      elementoEndereco.innerHTML = report.endereco ? `CEP: ${report.endereco}` : "Erro ao carregar endereço";
      elementoEndereco.classList.remove('loading-address');
    }
  });

  await Promise.allSettled(promises);
}

function navigateTo(url) {
  window.location.href = url;
}

async function downloadReport(reportId, event) {
  try {
    
    const button = event ? event.target.closest('.btn-download') : document.querySelector(`[onclick*="${reportId}"]`);
    const originalText = button.innerHTML;
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="animate-spin">
        <path d="M21 12a9 9 0 11-6.219-8.56"></path>
      </svg>
      Gerando...
    `;
    button.disabled = true;

    const token = localStorage.getItem('authToken');
    if (!token) {
      alert('Acesso negado. Por favor, faça o login.');
      window.location.href = '/';
      return;
    }

    const response = await fetch(`/api/relatorios/${reportId}/download`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error('Erro ao gerar PDF');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `relatorio_${reportId}_${new Date().toISOString().split('T')[0]}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    button.innerHTML = originalText;
    button.disabled = false;

  } catch (error) {
    console.error('Erro ao fazer download:', error);
    alert('Erro ao gerar PDF. Tente novamente.');

    const button = event ? event.target.closest('.btn-download') : document.querySelector(`[onclick*="${reportId}"]`);
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
        <polyline points="7,10 12,15 17,10"></polyline>
        <line x1="12" y1="15" x2="12" y2="3"></line>
      </svg>
      Download
    `;
    button.disabled = false;
  }
}

// Variável global para armazenar todos os relatórios
let allReports = [];

// Função para implementar a busca
function implementarBusca() {
  const searchInput = document.querySelector('.input-wrapper input');

  if (searchInput) {
    searchInput.addEventListener('input', (e) => {
      const searchTerm = e.target.value.toLowerCase().trim();
      filtrarRelatorios(searchTerm);
    });
  }
}

function filtrarRelatorios(searchTerm) {
  const reportList = document.getElementById("report-list");

  if (!searchTerm) {
    // Se não há termo de busca, mostra todos os relatórios
    renderizarRelatorios(allReports);
    return;
  }

  // Filtra os relatórios baseado no termo de busca
  const relatoriosFiltrados = allReports.filter(report => {
    const nomeInspecao = (report.nome_inspecao || '').toLowerCase();
    const endereco = (report.endereco || '').toLowerCase();
    const reportId = (report.relatorio_id || '').toString().toLowerCase();
    const codigoProjeto = (report.codigo_projeto || '').toLowerCase();

    return nomeInspecao.includes(searchTerm) ||
           endereco.includes(searchTerm) ||
           reportId.includes(searchTerm) ||
           codigoProjeto.includes(searchTerm);
  });

  renderizarRelatorios(relatoriosFiltrados);
}

function renderizarRelatorios(reports) {
  const reportList = document.getElementById("report-list");

  if (reports.length === 0) {
    reportList.innerHTML = `
      <div class="no-results">
        <p>Nenhum relatório encontrado.</p>
      </div>
    `;
    return;
  }

  reportList.innerHTML = reports
    .map(
      (report, index) => `
        <div class="report-card" data-report-id="${report.relatorio_id}">
          <div class="top">
            <h3 class="subtitle1">${report.nome_inspecao || "Sem título"}</h3>
            <span class="status">
              <p class="body3 text-muted-foreground">${report.codigo_projeto || "Sem código"}</p>
            </span>
          </div>
          <div class="middle">
            <p class="text-muted-foreground body2" id="endereco-${report.relatorio_id}">
              <span class="loading-address">🔄 Carregando endereço...</span>
            </p>
          </div>
          <div class="bottom">
            <span class="date">
              <p class="body2 text-muted-foreground">${new Date(report.criado_em).toLocaleDateString("pt-BR") || "Sem data"}</p>
            </span>
          </div>
          <div class="card-actions">
            <button class="btn-card btn-preview" onclick="event.stopPropagation(); navigateTo('/report-details/${report.relatorio_id}')">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              Preview
            </button>
            <button class="btn-card btn-download" onclick="event.stopPropagation(); downloadReport(${report.relatorio_id}, event)">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7,10 12,15 17,10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              Download
            </button>
          </div>
        </div>
      `
    )
    .join("");

  // Recarregar endereços para os relatórios filtrados
  carregarEnderecos(reports);
}

// Modificar a função fetchReports para armazenar os dados globalmente
async function fetchReportsWithSearch() {
  try {
    const token = localStorage.getItem("authToken");
    if (!token) {
      alert("Acesso negado. Por favor, faça o login.");
      window.location.href = "/";
      return;
    }

    const response = await fetch("/api/relatorios", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.status === 401) {
      window.location.href = "/";
      return;
    }

    if (!response.ok) {
      throw new Error(`Erro HTTP! Status: ${response.status}`);
    }

    const reports = await response.json();

    // Armazenar globalmente para busca
    allReports = reports;

    console.log("Dados recebidos do backend:", reports[0]);

    // Renderizar todos os relatórios inicialmente
    renderizarRelatorios(reports);

    // Implementar a funcionalidade de busca
    implementarBusca();

  } catch (error) {
    console.error("Erro ao buscar relatórios:", error);
    document.getElementById(
      "report-list"
    ).innerHTML = `<p>Erro ao carregar relatórios. Tente novamente mais tarde.</p>`;
  }
}

// Inicializar
fetchReportsWithSearch();