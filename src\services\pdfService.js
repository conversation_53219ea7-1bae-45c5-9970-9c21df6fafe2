const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;

class PDFService {
    constructor() {
        this.browser = null;
    }

    async initBrowser() {
        if (!this.browser) {
            this.browser = await puppeteer.launch({
                headless: 'new',
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });
        }
        return this.browser;
    }

    async closeBrowser() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
    }

    async generatePDFFromHTML(html, options = {}) {
        const browser = await this.initBrowser();
        const page = await browser.newPage();

        try {
            
            await page.setViewport({ width: 1200, height: 800 });

            await page.setContent(html, { 
                waitUntil: ['networkidle0', 'domcontentloaded'],
                timeout: 30000 
            });

            await page.evaluate(() => {
                return Promise.all(
                    Array.from(document.images, img => {
                        if (img.complete) return Promise.resolve();
                        return new Promise((resolve, reject) => {
                            img.addEventListener('load', resolve);
                            img.addEventListener('error', reject);
                        });
                    })
                );
            });

            const pdfOptions = {
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '20mm',
                    right: '15mm',
                    bottom: '20mm',
                    left: '15mm'
                },
                displayHeaderFooter: true,
                headerTemplate: `
                    <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
                        <span>Relatório de Inspeção - ${new Date().toLocaleDateString('pt-BR')}</span>
                    </div>
                `,
                footerTemplate: `
                    <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
                        <span>Página <span class="pageNumber"></span> de <span class="totalPages"></span></span>
                    </div>
                `,
                ...options
            };

            const pdfBuffer = await page.pdf(pdfOptions);
            
            return pdfBuffer;

        } finally {
            await page.close();
        }
    }

    async generatePDFFromURL(url, options = {}) {
        const browser = await this.initBrowser();
        const page = await browser.newPage();

        try {
            console.log(`🌐 Navegando para: ${url}`);

            await page.setViewport({ width: 1200, height: 800 });

            console.log(`🌐 Navegando para: ${url}`);

            const response = await page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            console.log(`📄 Página carregada, status: ${response.status()}`);

            if (response.status() >= 400) {
                throw new Error(`Falha ao carregar página: ${response.status()}`);
            }

            console.log(`⏳ Aguardando página carregar...`);
            await new Promise(resolve => setTimeout(resolve, 5000));

            console.log(`✅ Página preparada para PDF`);

            console.log(`📄 Gerando PDF...`);

            const pdfOptions = {
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '10mm',
                    right: '10mm',
                    bottom: '10mm',
                    left: '10mm'
                },
                displayHeaderFooter: false,
                ...options
            };

            const pdfBuffer = await page.pdf(pdfOptions);



            if (!pdfBuffer || pdfBuffer.length === 0) {
                throw new Error('PDF buffer está vazio');
            }

            return pdfBuffer;

        } finally {
            await page.close();
        }
    }

    async savePDFToFile(pdfBuffer, filename) {
        const uploadsDir = path.join(__dirname, '..', 'uploads', 'reports');

        try {
            await fs.access(uploadsDir);
        } catch {
            await fs.mkdir(uploadsDir, { recursive: true });
        }

        const filePath = path.join(uploadsDir, filename);
        await fs.writeFile(filePath, pdfBuffer);
        
        return filePath;
    }
}

module.exports = new PDFService();