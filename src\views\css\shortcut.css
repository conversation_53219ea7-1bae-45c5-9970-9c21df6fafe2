.shortcut-container {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: flex-end;

  & .btn-outline {
    background-color: var(--background-color);
  }

  & .shortcut-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 0;
    opacity: 0;
    width: 0;
    pointer-events: none;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  & .shortcut-buttons.active {
    width: 200px;
    max-height: 500px;
    opacity: 1;
    pointer-events: all;
  }

  & .shortcut-buttons button {
    transform: translateY(20px);
    opacity: 0;
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  & .shortcut-buttons.active button {
    transform: translateY(0);
    box-shadow: 0 0 4px var(--secondary-color);
    opacity: 1;
  }

  & .shortcut-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border-radius: 50%;
    aspect-ratio: 1 / 1;
    padding: 1rem;
    transition: transform 200ms ease;

    &:hover {
      transform: scale(1.05);
    }

    & img {
      width: 2rem;
      height: 2rem;
    }
  }
}