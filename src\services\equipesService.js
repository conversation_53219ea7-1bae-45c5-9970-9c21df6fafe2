const EquipeModel = require('../models/equipesModel');
const UsuarioModel = require('../models/usuariosModel');

class EquipesService {
    
    async listarEquipes() {
        try {
            return await EquipeModel.findAll();
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] listarEquipes:', error);
            throw new Error('Erro ao listar equipes.');
        }
    }

    validarDadosCriacao(dadosEquipe) {
        const { nome } = dadosEquipe;
        
        if (!nome) {
            throw new Error('Nome da equipe é obrigatório.');
        }

        if (nome.trim().length === 0) {
            throw new Error('Nome da equipe não pode estar vazio.');
        }

        return {
            nome: nome.trim()
        };
    }

    async criarEquipe(dadosEquipe) {
        try {
            
            const dadosValidados = this.validarDadosCriacao(dadosEquipe);

            const novaEquipe = await EquipeModel.create(dadosValidados);
            return novaEquipe;
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] criarEquipe:', error);
            throw error;
        }
    }

    async obterEquipePorId(id_equipe) {
        try {
            const equipe = await EquipeModel.findById(id_equipe);
            if (!equipe) {
                throw new Error('Equipe não encontrada.');
            }
            return equipe;
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] obterEquipePorId:', error);
            throw error;
        }
    }

    validarDadosAtualizacao(dadosEquipe) {
        const { nome } = dadosEquipe;
        
        if (!nome) {
            throw new Error('Nome da equipe é obrigatório para atualização.');
        }

        if (nome.trim().length === 0) {
            throw new Error('Nome da equipe não pode estar vazio.');
        }

        return {
            nome: nome.trim()
        };
    }

    async atualizarEquipe(id_equipe, dadosEquipe) {
        try {
            
            await this.obterEquipePorId(id_equipe);

            const dadosValidados = this.validarDadosAtualizacao(dadosEquipe);

            const equipeAtualizada = await EquipeModel.update(id_equipe, dadosValidados);
            if (!equipeAtualizada) {
                throw new Error('Equipe não encontrada para atualização.');
            }

            return equipeAtualizada;
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] atualizarEquipe:', error);
            throw error;
        }
    }

    async verificarPodeDeletar(id_equipe) {
        try {

            return true;
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] verificarPodeDeletar:', error);
            throw error;
        }
    }

    async deletarEquipe(id_equipe) {
        try {
            
            await this.obterEquipePorId(id_equipe);

            await this.verificarPodeDeletar(id_equipe);

            const deletada = await EquipeModel.remove(id_equipe);
            if (!deletada) {
                throw new Error('Equipe não encontrada para deleção.');
            }

            return true;
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] deletarEquipe:', error);
            throw error;
        }
    }

    async validarDadosAssociacao(dadosAssociacao) {
        const { id_usuario, cargo_nome } = dadosAssociacao;
        
        if (!id_usuario || !cargo_nome) {
            throw new Error('ID do usuário e nome do cargo são obrigatórios.');
        }

        const usuario = await UsuarioModel.findById(id_usuario);
        if (!usuario) {
            throw new Error('Usuário não encontrado.');
        }

        if (cargo_nome.trim().length === 0) {
            throw new Error('Nome do cargo não pode estar vazio.');
        }

        return {
            id_usuario: parseInt(id_usuario),
            cargo_nome: cargo_nome.trim()
        };
    }

    async adicionarUsuarioNaEquipe(id_equipe, dadosAssociacao) {
        try {
            
            await this.obterEquipePorId(id_equipe);

            const dadosValidados = await this.validarDadosAssociacao(dadosAssociacao);

            const associacao = await EquipeModel.addUserToEquipe(
                id_equipe, 
                dadosValidados.id_usuario, 
                dadosValidados.cargo_nome
            );

            return {
                message: "Usuário adicionado à equipe com sucesso.",
                associacao
            };
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] adicionarUsuarioNaEquipe:', error);
            throw error;
        }
    }

    async removerUsuarioDaEquipe(id_equipe, id_usuario) {
        try {
            
            await this.obterEquipePorId(id_equipe);

            const usuario = await UsuarioModel.findById(id_usuario);
            if (!usuario) {
                throw new Error('Usuário não encontrado.');
            }

            const removido = await EquipeModel.removeUserFromEquipe(id_equipe, id_usuario);
            if (!removido) {
                throw new Error('Associação de usuário e equipe não encontrada.');
            }

            return true;
        } catch (error) {
            console.error('[EQUIPES_SERVICE_ERROR] removerUsuarioDaEquipe:', error);
            throw error;
        }
    }
}

module.exports = new EquipesService();