/* Container principal limpo */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: #ffffff;
  min-height: 100vh;
}

/* Header limpo e moderno */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.header h1 {
  margin: 0;
  color: #00558C;
  font-size: 2.2rem;
  font-weight: 700;
}

/* Ações com melhor espaçamento */
.actions {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

/* Botões modernos com gradientes e sombras */
.btn-outline, .btn-primary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-outline {
  background: #ffffff;
  border: 2px solid #e9ecef;
  color: #495057;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-outline:hover {
  background: #f8f9fa;
  border-color: #00558C;
  color: #00558C;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 85, 140, 0.2);
}

.btn-primary {
  background: #00558C;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 85, 140, 0.3);
  border: 2px solid #00558C;
}

.btn-primary:hover {
  background: #004070;
  border-color: #004070;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 85, 140, 0.4);
}

/* Efeito de ondulação nos botões */
.btn-outline::before, .btn-primary::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-outline:active::before, .btn-primary:active::before {
  width: 300px;
  height: 300px;
}

.btn-outline img, .btn-primary img {
  width: 16px;
  height: 16px;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #00558C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-state h3 {
  color: #dc3545;
  margin-bottom: 0.5rem;
}

.error-state p {
  color: #666;
  margin-bottom: 2rem;
}

/* Report Preview */
.report-preview {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.report-header {
  background: #00558C;
  color: white;
  padding: 2rem;
}

.report-title h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.report-title .subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.report-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.meta-item .label {
  font-size: 0.8rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.meta-item .value {
  font-weight: 500;
}

/* Seções modernas com glassmorphism */
.section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 3rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #00558C;
  border-radius: 20px 20px 0 0;
}

.section:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.section-title::after {
  content: '';
  flex: 1;
  height: 2px;
  background: #00558C;
  border-radius: 1px;
  opacity: 0.3;
}

.ai-badge {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item .label {
  font-weight: 600;
  color: #666;
  font-size: 0.9rem;
}

.info-item .value {
  color: #333;
  font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #00558C;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

/* AI Content */
.ai-content {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border-left: 4px solid #28a745;
}

.content-block {
  margin-bottom: 2rem;
}

.content-block:last-child {
  margin-bottom: 0;
}

.content-block h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.content-block h5 {
  margin: 0 0 0.5rem 0;
  color: #555;
  font-size: 1rem;
  font-weight: 600;
}

.content-block p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.result-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Subsections */
.subsection {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.subsection:last-child {
  margin-bottom: 0;
}

.subsection-title {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Photos Grid */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.photo-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  background: #f8f9fa;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.photo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.photo-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
  background: #e9ecef;
}

.photo-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.75rem;
  font-size: 0.8rem;
  z-index: 2;
}

/* Loading state for photos */
.photo-item::before {
  content: '📸 Carregando...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #6c757d;
  font-size: 0.9rem;
  z-index: 1;
}

.photo-item img[style*="opacity: 1"] + .photo-caption::before {
  display: none;
}

/* Detailed Lists */
.detailed-environments, .detailed-occurrences {
  display: grid;
  gap: 2rem;
}

.detailed-environment-item, .detailed-occurrence-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.detailed-environment-header, .detailed-occurrence-header {
  background: #00558C;
  color: white;
  padding: 1.5rem;
}

.detailed-occurrence-header {
  background: #dc3545;
}

.detailed-environment-title, .detailed-occurrence-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.detailed-environment-meta, .detailed-occurrence-meta {
  display: flex;
  gap: 2rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.detailed-environment-content, .detailed-occurrence-content {
  padding: 1.5rem;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-section p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* Gravity Stats */
.gravity-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.gravity-item {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border: 2px solid;
}

.gravity-item.low {
  border-color: #28a745;
  background: #d4edda;
}

.gravity-item.medium {
  border-color: #ffc107;
  background: #fff3cd;
}

.gravity-item.high {
  border-color: #dc3545;
  background: #f8d7da;
}

.gravity-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.gravity-item.low .gravity-number {
  color: #155724;
}

.gravity-item.medium .gravity-number {
  color: #856404;
}

.gravity-item.high .gravity-number {
  color: #721c24;
}

.gravity-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

/* Photos Stats */
.photos-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.photo-stat-item {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #dee2e6;
}

.photo-stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 0.5rem;
}

.photo-stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

/* Environment/Occurrence Lists (Legacy) */
.environments-list, .occurrences-list {
  display: grid;
  gap: 1rem;
}

.environment-item, .occurrence-item {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #00558C;
}

.environment-item h4, .occurrence-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.environment-item p, .occurrence-item p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.occurrence-item {
  border-left-color: #dc3545;
}

.empty-state {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 2rem;
}

/* Chart Container */
.chart-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Recommendations List */
#ai-recommendations {
  margin: 0;
  padding-left: 1.5rem;
}

#ai-recommendations li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
  color: #555;
}

/* Sub-environments */
.sub-environments {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.sub-environment-tag {
  background: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Mini occurrences */
.environment-occurrences {
  display: grid;
  gap: 0.75rem;
}

.mini-occurrence {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  padding: 0.75rem;
}

.mini-occurrence strong {
  color: #c53030;
  font-size: 0.9rem;
}

.mini-occurrence p {
  margin: 0.25rem 0 0 0;
  font-size: 0.8rem;
  color: #666;
}

/* Classification grid */
.classification-grid {
  display: grid;
  gap: 0.75rem;
}

.classification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #00558C;
}

.classification-item strong {
  color: #495057;
  font-size: 0.9rem;
}

.classification-item span {
  color: #00558C;
  font-weight: 500;
}

/* Chart improvements */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

.chart-bar {
  transition: all 0.2s ease;
}

.chart-bar:hover {
  transform: translateX(4px);
}

/* Photo modal (for future implementation) */
.photo-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.photo-modal.active {
  opacity: 1;
  visibility: visible;
}

.photo-modal img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  border-radius: 8px;
}

/* Loading states */
.loading-photos {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #666;
}

.loading-photos::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #00558C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .actions {
    justify-content: center;
  }

  .report-meta {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .gravity-stats {
    grid-template-columns: 1fr;
  }

  .photos-stats {
    grid-template-columns: 1fr;
  }

  .detailed-environment-meta,
  .detailed-occurrence-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .classification-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
