.profile-container {
  padding: clamp(1rem, 4vw, 2rem);
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  min-height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
  gap: clamp(1.5rem, 4vw, 2rem);
}

#profile-title {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  font-weight: 600;
  color: var(--foreground-color);
  margin: 0;
  align-self: flex-start;
}

.profile-photo {
  position: relative;
  width: clamp(120px, 25vw, 140px);
  height: clamp(120px, 25vw, 140px);
  align-self: center;
  margin: clamp(1rem, 3vw, 1.5rem) 0;
}

.profilePhoto {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid var(--muted-color);
  object-fit: cover;
  background-color: var(--background-color);
}

.edit-photo {
  position: absolute;
  bottom: 0;
  right: 0;
  width: clamp(24px, 5vw, 32px);
  height: clamp(24px, 5vw, 32px);
  background-color: var(--background-color);
  border: 2px solid var(--muted-color);
  border-radius: 6px;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-photo:hover {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.profile-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: clamp(1.25rem, 3vw, 1.5rem);
}

.profile-form label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--foreground-color);
}

.input-container {
  position: relative;
  width: 100%;
}

.input-container input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid var(--muted-color);
  border-radius: 8px;
  font-size: 0.875rem;
  background-color: var(--background-color);
  color: var(--foreground-color);
  transition: all 0.2s ease;
  margin: 0;
}

.input-container input {
  padding-left: 1rem;
}

.input-container input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
}

.input-container input:disabled {
  background-color: #f8f9fa;
  color: var(--muted-foreground-color);
  cursor: not-allowed;
}

.input-iconLock {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  opacity: 0.6;
  pointer-events: none;
}

.input-iconSenha {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  opacity: 0.6;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.input-iconSenha:hover {
  opacity: 1;
}

.edit-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  opacity: 0.6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-icon:hover {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.buttons-edit-mode {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.buttons-edit-mode button {
  flex: 1;
  min-height: 44px;
}

#edit {
  width: 100%;
  margin-top: 1rem;
  min-height: 44px;
}

.btn-destructive {
  width: 100%;
  margin-top: 0.75rem;
  min-height: 44px;
  border: 2px solid var(--destructive-color) !important;
}

.btn-outline,
.btn-primary,
.btn-destructive {
  transition: all 0.2s ease;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--background-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 85, 140, 0.2);
}

.btn-primary:hover {
  background-color: #004070;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 85, 140, 0.3);
}

.btn-destructive:hover {
  background-color: var(--destructive-color);
  color: var(--background-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(229, 57, 53, 0.2);
}

.btn-outline:active,
.btn-primary:active,
.btn-destructive:active {
  transform: translateY(0);
  box-shadow: none;
}

.hidden {
  display: none !important;
}

@media (max-width: 360px) {
  .profile-container {
    padding: 1rem 0.75rem;
  }

  .buttons-edit-mode {
    flex-direction: column;
    gap: 0.5rem;
  }

  .buttons-edit-mode button {
    flex: none;
  }
}

.profile-form.edit-mode .input-group input[type="email"] {
  padding-left: 1rem;
}

.profile-form.edit-mode .edit-icon {
  opacity: 1;
}

.profile-form.edit-mode .input-iconMail {
  opacity: 1;
}

.profile-container * {
  transition: opacity 0.2s ease, transform 0.2s ease, border-color 0.2s ease,
    background-color 0.2s ease;
}

.edit-photo:focus,
.edit-icon:focus,
.input-iconSenha:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
