<link rel="stylesheet" href="../css/my-teams.css" />

<div class="container">
    <h2>Minhas Equipes</h2>

    <% if (typeof equipes !=='undefined' && equipes.length> 0) { %>
        <div class="teams-grid">
            <% equipes.forEach(team=> { %>
                <section class="team-card" id="<%= team.id %>">
                    <div class="team-header">
                        <h3 class="team-name"><%= team.nome %></h3>
                        <span class="team-id"><%= team.codigo %></span>
                    </div>
                <div class="avatars">
                    <% if (team.membros && team.membros.length> 0) { %>
                        <% team.membros.forEach(function(member, idx) { %>
                            <% if(idx===4) { %>
                                <div class="avatar-break"></div>
                            <% } %>
                            <div class="avatar">
                                <img src="/assets/icons/user-primary.svg" alt="<%= member.nome %>">
                                <span class="avatar-name">
                                    <%= member.nome %>
                                </span>
                            </div>
                        <% }); %>
                    <% } %>
                </div>
                <ul class="inspections" style="display:none;"></ul>
                <a class="toggle" href="#" onclick="toggleInspections('<%= team.id %>', event)">
                    <span>Ver inspeções</span>
                    <span>▼</span>
                </a>
            </section>
            <% }); %>
        </div>
    <% } else { %>
        <div class="empty-state">
            <p>Nenhuma equipe encontrada.</p>
        </div>
    <% } %>
</div>

<script src="/js/my-teams.js"></script>