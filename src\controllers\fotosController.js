const fotosService = require('../services/fotosService');
const FotoModel = require('../models/fotosModel');
const AmbienteModel = require('../models/ambientesModel');
const InspecaoModel = require('../models/inspecoesModel');
const supabase = require('../config/supabaseClient');

exports.adicionarFotoInspecao = async (req, res) => {
    try {
        const { id_inspecao } = req.body;

        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ message: 'Pelo menos um arquivo de imagem é obrigatório.' });
        }

        const inspecao = await InspecaoModel.findById(id_inspecao);
        if (!inspecao) {
            return res.status(404).json({ message: `Inspeção com ID ${id_inspecao} não encontrada.` });
        }

        const promessasDeUpload = req.files.map(async (file) => {
            const nomeUnico = `${Date.now()}-${file.originalname}`;
            const { error: uploadError } = await supabase.storage
                .from('images')
                .upload(nomeUnico, file.buffer, { contentType: file.mimetype });

            if (uploadError) {
                throw new Error(`Falha no upload para o Supabase: ${uploadError.message}`);
            }

            const { data: publicUrlData } = supabase.storage
                .from('images')
                .getPublicUrl(nomeUnico);

            return FotoModel.create({
                id_inspecao: parseInt(id_inspecao),
                url: publicUrlData.publicUrl
            });
        });

        const fotosAdicionadas = await Promise.all(promessasDeUpload);
        res.status(201).json(fotosAdicionadas);
    } catch (error) {
        res.status(500).json({ message: "Erro ao adicionar foto(s) à inspeção.", error: error.message });
    }
};

exports.gerarUrlAssinada = async (req, res) => {
    try {
        const { id_foto } = req.params;
        const resultado = await fotosService.gerarUrlAssinada(id_foto);
        res.status(200).json(resultado);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] gerarUrlAssinada:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.listarFotosPorAmbiente = async (req, res) => {
    try {
        const { id_ambiente } = req.params;
        const { id_ocorrencia } = req.query;
        const fotos = await fotosService.listarFotosPorAmbiente(id_ambiente, id_ocorrencia);
        res.status(200).json(fotos);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarFotosPorAmbiente:', error);
        const status = error.message.includes('não encontrado') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.listarFotosPorOcorrencia = async (req, res) => {
    try {
        const { id_ocorrencia } = req.params;
        const fotos = await fotosService.listarFotosPorOcorrencia(id_ocorrencia);
        res.status(200).json(fotos);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] listarFotosPorOcorrencia:', error);
        res.status(500).json({ message: error.message });
    }
};

exports.adicionarFotoAmbienteOcorrencia = async (req, res) => {
    try {
        const fotosAdicionadas = await fotosService.adicionarFotoAmbienteOcorrencia(req.body, req.files);
        res.status(201).json(fotosAdicionadas);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] adicionarFotoAmbienteOcorrencia:', error);
        const status = error.message.includes('não encontrado') ? 404 :
                      error.message.includes('obrigatório') ? 400 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.obterMetadadosFotoPorId = async (req, res) => {
    try {
        const { id_foto } = req.params;
        const foto = await fotosService.obterMetadadosFotoPorId(id_foto);
        res.status(200).json(foto);
    } catch (error) {
        console.error('[CONTROLLER_ERROR] obterMetadadosFotoPorId:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};

exports.deletarFoto = async (req, res) => {
    try {
        const { id_foto } = req.params;
        await fotosService.deletarFoto(id_foto);
        res.status(204).send();
    } catch (error) {
        console.error('[CONTROLLER_ERROR] deletarFoto:', error);
        const status = error.message.includes('não encontrada') ? 404 : 500;
        res.status(status).json({ message: error.message });
    }
};