/* Modern Home Page Styles */
.home-container {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg,
    var(--background) 0%,
    rgba(0, 85, 140, 0.02) 50%,
    var(--background) 100%);
  overflow-x: hidden;
}

/* Hero Section */
.hero-section {
  position: relative;
  padding: 4rem 2rem 6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: linear-gradient(135deg,
    rgba(0, 85, 140, 0.05) 0%,
    transparent 50%,
    rgba(0, 85, 140, 0.03) 100%);
}

.hero-content {
  max-width: 1200px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 4rem;
  align-items: center;
  z-index: 2;
  position: relative;
}

.welcome-text {
  text-align: left;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin: 0 0 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.gradient-text {
  background: linear-gradient(135deg, var(--foreground) 0%, #666 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s ease-out;
}

.brand-text {
  background: linear-gradient(135deg, var(--primary) 0%, #0066cc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s ease-out 0.2s both;
  position: relative;
}

.brand-text::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary) 0%, transparent 100%);
  border-radius: 2px;
  animation: expandWidth 1.5s ease-out 0.8s both;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2vw, 1.3rem);
  color: var(--muted-foreground);
  margin: 0;
  font-weight: 400;
  line-height: 1.6;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-stats {
  display: flex;
  gap: 1.5rem;
  flex-direction: column;
}

.stat-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem 1.5rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 140px;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 85, 140, 0.15);
  border-color: rgba(0, 85, 140, 0.3);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
  animation: bounce 2s infinite;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--muted-foreground);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Floating Background Shapes */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 85, 140, 0.1), rgba(0, 85, 140, 0.05));
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

/* Quick Actions Section */
.quick-actions-section {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  font-weight: 700;
  color: var(--foreground);
  margin: 0 0 3rem 0;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), #0066cc);
  border-radius: 2px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.action-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 2.5rem 2rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-align: center;
  group: hover;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 85, 140, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover::before {
  opacity: 1;
}

.action-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 85, 140, 0.2);
  border-color: rgba(0, 85, 140, 0.3);
}

.action-card.primary {
  background: linear-gradient(135deg, var(--primary), #0066cc);
  color: white;
  border-color: transparent;
}

.action-card.primary::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
}

.action-card.primary:hover {
  box-shadow: 0 25px 50px rgba(0, 85, 140, 0.4);
}

.action-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-card.primary .action-icon {
  background: rgba(255, 255, 255, 0.2);
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotate(5deg);
}

.action-icon svg {
  width: 28px;
  height: 28px;
  color: currentColor;
}

.action-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: inherit;
}

.action-card p {
  font-size: 0.95rem;
  color: inherit;
  opacity: 0.8;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.action-arrow {
  font-size: 1.5rem;
  font-weight: bold;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.action-card:hover .action-arrow {
  opacity: 1;
  transform: translateX(0);
}

/* Inspections Section */
.inspections-section {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.5rem;
}

.toggle-btn {
  background: transparent;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn svg {
  width: 20px;
  height: 20px;
  color: var(--muted-foreground);
}

.toggle-btn.active {
  background: var(--primary);
  color: white;
}

.toggle-btn.active svg {
  color: white;
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.filter-tags {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-tag {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  color: var(--foreground);
  position: relative;
  overflow: hidden;
}

.filter-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.filter-tag:hover::before {
  left: 100%;
}

.filter-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 85, 140, 0.15);
}

.filter-tag.active {
  background: linear-gradient(135deg, var(--primary), #0066cc);
  color: white;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 85, 140, 0.3);
}

.tag-icon {
  font-size: 1rem;
}

.search-container {
  display: flex;
  justify-content: center;
}

.search-input-wrapper {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 1rem 3rem 1rem 3rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: var(--foreground);
  transition: all 0.3s ease;
  font-family: inherit;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.search-input::placeholder {
  color: var(--muted-foreground);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--muted-foreground);
  pointer-events: none;
}

.search-clear {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.search-clear svg {
  width: 16px;
  height: 16px;
  color: var(--muted-foreground);
}

.search-clear:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Inspection List Styles */
.inspection-list {
  transition: all 0.3s ease;
}

.inspection-list.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.inspection-list.list-view {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loading-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 85, 140, 0.1);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-text {
  color: var(--muted-foreground);
  font-size: 1.1rem;
  margin: 0;
}

.inspection-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.inspection-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 85, 140, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.inspection-card:hover::before {
  opacity: 1;
}

.inspection-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 85, 140, 0.15);
  border-color: rgba(0, 85, 140, 0.3);
}

.inspection-card:active {
  transform: translateY(-4px) scale(1.01);
}

.inspection-card .top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.inspection-card .top h3 {
  margin: 0;
  color: var(--foreground);
  font-weight: 600;
  font-size: 1.2rem;
  line-height: 1.3;
  flex: 1;
}

.inspection-card .status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.inspection-card .status[data-status*="EM ANDAMENTO"] {
  background: rgba(255, 193, 7, 0.1);
  color: #f57c00;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.inspection-card .status[data-status*="ABERTA"],
.inspection-card .status[data-status*="NÃO INICIADA"] {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.inspection-card .status[data-status*="CONCLUÍDA"],
.inspection-card .status[data-status*="CONCLUÍDO"] {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.inspection-card .middle {
  margin-bottom: 1.5rem;
}

.inspection-card .middle p {
  margin: 0;
  color: var(--muted-foreground);
  line-height: 1.5;
  font-size: 0.95rem;
}

.inspection-card .bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.inspection-card .bottom .date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--muted-foreground);
  font-size: 0.9rem;
}

.inspection-card .icons {
  display: flex;
  gap: -4px;
  align-items: center;
}

.inspection-card .icons img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid white;
  margin-left: -4px;
  transition: transform 0.3s ease;
}

.inspection-card .icons img:first-child {
  margin-left: 0;
}

.inspection-card:hover .icons img {
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandWidth {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(5deg);
  }
  66% {
    transform: translateY(-10px) rotate(-5deg);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Dark Mode Adaptations */
[data-theme="dark"] .home-container {
  background: linear-gradient(135deg,
    var(--background) 0%,
    rgba(0, 85, 140, 0.05) 50%,
    var(--background) 100%);
}

[data-theme="dark"] .hero-section {
  background: linear-gradient(135deg,
    rgba(0, 85, 140, 0.1) 0%,
    transparent 50%,
    rgba(0, 85, 140, 0.05) 100%);
}

[data-theme="dark"] .stat-card,
[data-theme="dark"] .action-card,
[data-theme="dark"] .filter-tag,
[data-theme="dark"] .search-input,
[data-theme="dark"] .view-toggle,
[data-theme="dark"] .inspection-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .action-card.primary {
  background: linear-gradient(135deg, var(--primary), #0066cc);
}

[data-theme="dark"] .filter-tag.active {
  background: linear-gradient(135deg, var(--primary), #0066cc);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-stats {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .stat-card {
    min-width: 120px;
    padding: 1.5rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .action-card {
    padding: 2rem 1.5rem;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .filter-tags {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .filter-tag {
    flex-shrink: 0;
  }

  .inspection-list.grid-view {
    grid-template-columns: 1fr;
  }

  .quick-actions-section,
  .inspections-section {
    padding: 2rem 1rem;
  }

  .hero-section {
    padding: 2rem 1rem 4rem;
    min-height: 50vh;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .stat-card {
    min-width: 100px;
    padding: 1rem 0.75rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-icon {
    font-size: 2rem;
  }

  .action-card {
    padding: 1.5rem 1rem;
  }

  .inspection-card {
    padding: 1.5rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.action-card:focus,
.filter-tag:focus,
.inspection-card:focus,
.toggle-btn:focus,
.search-input:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Loading States */
.no-results {
  grid-column: 1 / -1;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--muted-foreground);
  font-style: italic;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.inspection-card.hidden {
  display: none;
}

/* Estilos antigos removidos - substituídos pelos novos estilos modernos */

.search-filter {
  display: flex;
  gap: clamp(0.5rem, 2vw, 0.75rem);
  align-items: center;
}

.search-bar {
  position: relative;
  flex: 1;
  max-width: 100%;
}

.search-bar input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  font-size: 0.875rem;
  background-color: var(--background-color);
  color: var(--foreground-color);
  transition: all 0.2s ease;
  font-family: inherit;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
}

.search-bar input::placeholder {
  color: #71717a;
  font-weight: 400;
}

.search-bar img {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  opacity: 0.6;
  pointer-events: none;
}

.search-filter > img {
  width: 40px;
  height: 40px;
  padding: 0.5rem;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--background-color);
}

.search-filter > img:hover {
  border-color: var(--primary-color);
  background-color: #f8f9fa;
  transform: scale(1.05);
}

.inspection-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  background-color: transparent;
}

.inspection-list.empty {
  padding: 3rem 1.5rem;
  text-align: center;
  color: var(--muted-foreground-color);
  font-style: italic;
}

.list-separator {
  width: 100%;
  height: 1px;
  background-color: #e4e4e7;
  margin: 0;
}

.inspection-card {
  cursor: pointer;
  padding: clamp(1rem, 3vw, 1.25rem) 0;
  display: flex;
  flex-direction: column;
  gap: clamp(0.5rem, 1.5vw, 0.75rem);
  background-color: transparent;
  transition: all 0.2s ease;
  position: relative;
}

.inspection-card:hover {
  background-color: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.inspection-card:active {
  transform: scale(0.98);
}

.inspection-card.hidden {
  display: none;
}

.inspection-card .top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.inspection-card .top h3 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
  flex: 1;
  line-height: 1.3;
  font-size: 1rem;
}

.inspection-card .status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.inspection-card .status[data-status*="EM ANDAMENTO"]::before {
  background-color: var(--ongoing-color);
}

.inspection-card .status[data-status*="ABERTA"]::before,
.inspection-card .status[data-status*="NÃO INICIADA"]::before {
  background-color: var(--neutral-color);
}

.inspection-card .status[data-status*="CONCLUÍDA"]::before,
.inspection-card .status[data-status*="CONCLUÍDO"]::before {
  background-color: var(--success-color);
}

.inspection-card .status p {
  margin: 0;
  font-size: 0.75rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  color: #71717a;
}

.inspection-card .middle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inspection-card .middle p {
  margin: 0;
  color: #71717a;
  line-height: 1.4;
  font-size: 0.875rem;
  font-weight: 400;
}

.inspection-card .bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.inspection-card .bottom .date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inspection-card .bottom .date p {
  margin: 0;
  color: #71717a;
  font-size: 0.875rem;
  font-weight: 400;
}

.inspection-card .icons {
  display: flex;
  gap: -2px;
  align-items: center;
}

.inspection-card .icons img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid var(--background-color);
  margin-left: -2px;
}

.inspection-card .icons img:first-child {
  margin-left: 0;
}

.no-results {
  padding: 2rem;
  text-align: center;
  color: var(--muted-foreground-color);
  font-style: italic;
  background-color: transparent;
}

.loading-state {
  padding: 2rem;
  text-align: center;
  color: var(--muted-foreground-color);
  background-color: transparent;
}

.loading-state::after {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--muted-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-left: 0.5rem;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.status span {
  height: 12px;
  aspect-ratio: 1;
  border-radius: 50%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0.75rem;
  }

  .container .actions {
    flex-direction: column;
  }

  .container .actions button {
    flex: none;
    width: 100%;
  }

  .search-filter {
    align-items: stretch;
    gap: 0.75rem;
  }

  .search-filter > img {
    align-self: center;
    width: 40px;
    height: 40px;
  }

  .inspection-card .top {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .inspection-card .status {
    align-self: flex-end;
  }

  .tags {
    justify-content: flex-start;
    padding-bottom: 0.5rem;
  }
}

.inspection-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.search-bar input:focus,
.search-filter > img:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

* {
  transition: opacity 0.2s ease, transform 0.2s ease, border-color 0.2s ease,
    background-color 0.2s ease, box-shadow 0.2s ease;
}
