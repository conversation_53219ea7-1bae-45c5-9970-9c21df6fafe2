/* Enhanced Home Page Styles - Mantendo estrutura original */
.container {
  display: flex;
  flex-direction: column;
  gap: clamp(1.25rem, 3vw, 1.5rem);
  padding: clamp(1rem, 4vw, 1.5rem);
  max-width: 100%;
  margin: 0 auto;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg,
    var(--background) 0%,
    rgba(0, 85, 140, 0.01) 50%,
    var(--background) 100%);
  position: relative;
}

/* Efeito sutil de fundo */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 85, 140, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 85, 140, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.container > * {
  position: relative;
  z-index: 1;
}

/* <PERSON><PERSON> Near - Header e Ações */
.container .near {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 3vw, 1.25rem);
  animation: fadeInUp 0.6s ease-out;
}

.container h1 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
  position: relative;
}

.container h1::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), transparent);
  border-radius: 2px;
  animation: expandWidth 1s ease-out 0.3s both;
}

.container .actions {
  display: flex;
  gap: clamp(0.5rem, 2vw, 0.75rem);
  flex-wrap: wrap;
}

.container .actions button {
  flex: 1;
  min-width: clamp(140px, 30vw, 180px);
  min-height: 44px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.875rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.container .actions button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.container .actions button:hover::before {
  left: 100%;
}

.container .actions .btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--background-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 85, 140, 0.25);
}

.container .actions .btn-primary:hover {
  background-color: #004070;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 85, 140, 0.35);
}

/* Seção Too - Filtros e Busca */
.container h2 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.container .too {
  gap: clamp(0.75rem, 2vw, 1rem);
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.tags {
  display: flex;
  gap: clamp(0.5rem, 2vw, 0.75rem);
  overflow-x: auto;
  padding: 0.25rem 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tags::-webkit-scrollbar {
  display: none;
}

.tags .tag {
  white-space: nowrap;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  font-size: 0.75rem;
  padding: 8px 18px;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
}

.tags .tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.tags .tag:hover::before {
  left: 100%;
}

.tags .tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.tags .tag.active {
  background-color: var(--primary-color);
  color: var(--background-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 85, 140, 0.3);
}

.tags .tag.ongoing {
  border-color: var(--ongoing-color);
  color: var(--ongoing-color);
}

.tags .tag.ongoing.active {
  background-color: var(--ongoing-color);
  color: var(--background-color);
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
}

.tags .tag.neutral {
  border-color: var(--neutral-color);
  color: var(--neutral-color);
}

.tags .tag.neutral.active {
  background-color: var(--neutral-color);
  color: var(--background-color);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
}

.tags .tag.success {
  border-color: var(--success-color);
  color: var(--success-color);
}

.tags .tag.success.active {
  background-color: var(--success-color);
  color: var(--background-color);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

/* Barra de Busca Melhorada */
.search-filter {
  display: flex;
  justify-content: center;
}

.search-bar {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.search-bar input {
  width: 100%;
  padding: 12px 50px 12px 20px;
  border: 2px solid rgba(0, 85, 140, 0.1);
  border-radius: 25px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  color: var(--foreground-color);
  transition: all 0.3s ease;
  font-family: inherit;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.search-bar input::placeholder {
  color: var(--muted-foreground);
}

.search-bar img {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  opacity: 0.6;
  pointer-events: none;
}

/* Lista de Inspeções Melhorada */
.inspection-list {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 2vw, 1.25rem);
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  color: var(--muted-foreground);
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 85, 140, 0.1);
  position: relative;
  overflow: hidden;
}

.loading-state::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 85, 140, 0.1), transparent);
  animation: shimmer 2s infinite;
}

.inspection-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 85, 140, 0.1);
  border-radius: 16px;
  padding: clamp(2rem, 5vw, 2.5rem);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;
}

.inspection-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

/* Remover efeito shimmer */
.inspection-card:hover::before {
  left: -100%;
}

/* Efeito hover sutil - apenas borda */
.inspection-card:hover {
  border-color: var(--primary-color);
  transition: border-color 0.2s ease;
}

.inspection-card:active {
  transform: translateY(-2px);
}

.inspection-card .top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.inspection-card .top h3 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
  font-size: clamp(1.1rem, 2.5vw, 1.25rem);
  line-height: 1.3;
  flex: 1;
}

.inspection-card .status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.inspection-card .status span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.25rem;
}

.inspection-card .middle {
  margin-bottom: 1.25rem;
}

.inspection-card .middle p {
  margin: 0;
  color: var(--muted-foreground);
  line-height: 1.5;
  font-size: 0.9rem;
}

.inspection-card .bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.inspection-card .bottom .date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--muted-foreground);
  font-size: 0.85rem;
}

.inspection-card .icons {
  display: flex;
  gap: -4px;
  align-items: center;
}

.inspection-card .icons img {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid white;
  margin-left: -4px;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.inspection-card .icons img:first-child {
  margin-left: 0;
}

/* Remover efeitos de escala das imagens */
.inspection-card:hover .icons img {
  transform: none;
}

/* Nova estrutura dos cards de inspeção */
.inspection-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: clamp(1.5rem, 3.5vw, 1.75rem);
  gap: 1.5rem;
}

.inspection-card .card-title {
  flex: 1;
  min-width: 0;
}

.inspection-card .card-title h3 {
  font-size: clamp(1rem, 2.5vw, 1.125rem);
  font-weight: 600;
  color: var(--foreground-color);
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.inspection-card .inspection-id {
  font-size: 0.75rem;
  color: var(--muted-foreground-color);
  font-weight: 500;
  opacity: 0.8;
}

.inspection-card .status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.inspection-card .card-content {
  display: flex;
  flex-direction: column;
  gap: clamp(1.25rem, 3vw, 1.5rem);
}

.inspection-card .address-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: rgba(0, 85, 140, 0.05);
  border-radius: 12px;
  border-left: 3px solid var(--primary-color);
}

.inspection-card .location-icon {
  width: 16px;
  height: 16px;
  color: var(--primary-color);
  flex-shrink: 0;
  margin-top: 2px;
}

.inspection-card .address-text {
  color: var(--foreground-color);
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
  flex: 1;
}

.inspection-card .card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1.5rem;
  padding-top: 0.75rem;
}

.inspection-card .date-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--muted-foreground-color);
  font-size: 0.75rem;
  flex: 1;
}

.inspection-card .date-icon {
  width: 14px;
  height: 14px;
  color: var(--muted-foreground-color);
  flex-shrink: 0;
}

.inspection-card .card-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 85, 140, 0.1);
  transition: all 0.3s ease;
}

.inspection-card .arrow-icon {
  width: 16px;
  height: 16px;
  color: var(--primary-color);
  transition: transform 0.3s ease;
}

/* Remover efeitos hover dos novos elementos */
.inspection-card:hover .card-actions {
  background: inherit;
  transform: none;
}

.inspection-card:hover .arrow-icon {
  transform: none;
}

/* Modo escuro para novos elementos */
[data-theme="dark"] .inspection-card .address-info {
  background: rgba(255, 255, 255, 0.05);
  border-left-color: var(--primary-color);
}

[data-theme="dark"] .inspection-card .card-actions {
  background: rgba(255, 255, 255, 0.1);
}

/* Estilos para status-badge */
.inspection-card .status-badge[data-status*="EM ANDAMENTO"] {
  background-color: rgba(255, 178, 70, 0.15);
  color: var(--ongoing-color);
  border: 1px solid rgba(255, 178, 70, 0.3);
}

.inspection-card .status-badge[data-status*="ABERTA"],
.inspection-card .status-badge[data-status*="NÃO INICIADA"] {
  background-color: rgba(76, 113, 139, 0.15);
  color: var(--neutral-color);
  border: 1px solid rgba(76, 113, 139, 0.3);
}

.inspection-card .status-badge[data-status*="CONCLUÍDA"],
.inspection-card .status-badge[data-status*="CONCLUÍDO"] {
  background-color: rgba(86, 167, 87, 0.15);
  color: var(--success-color);
  border: 1px solid rgba(86, 167, 87, 0.3);
}

/* Modo escuro para status-badge */
[data-theme="dark"] .inspection-card .status-badge[data-status*="EM ANDAMENTO"] {
  background-color: rgba(251, 191, 36, 0.2);
  color: var(--ongoing-color);
  border-color: rgba(251, 191, 36, 0.4);
}

[data-theme="dark"] .inspection-card .status-badge[data-status*="ABERTA"],
[data-theme="dark"] .inspection-card .status-badge[data-status*="NÃO INICIADA"] {
  background-color: rgba(156, 163, 175, 0.2);
  color: var(--neutral-color);
  border-color: rgba(156, 163, 175, 0.4);
}

[data-theme="dark"] .inspection-card .status-badge[data-status*="CONCLUÍDA"],
[data-theme="dark"] .inspection-card .status-badge[data-status*="CONCLUÍDO"] {
  background-color: rgba(52, 211, 153, 0.2);
  color: var(--success-color);
  border-color: rgba(52, 211, 153, 0.4);
}

.list-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 85, 140, 0.1), transparent);
  margin: 0.5rem 0;
}

.no-results, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  color: var(--muted-foreground);
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 85, 140, 0.1);
}

.no-results h3, .error h3 {
  margin: 1rem 0 0.5rem 0;
  color: var(--foreground-color);
}

.no-results p, .error p {
  margin: 0;
  font-size: 0.9rem;
}

/* Animações Suaves */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandWidth {
  from {
    width: 0;
  }
  to {
    width: 40px;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Adaptações para Modo Escuro */
[data-theme="dark"] .container {
  background: linear-gradient(135deg,
    var(--background) 0%,
    rgba(0, 85, 140, 0.03) 50%,
    var(--background) 100%);
}

[data-theme="dark"] .container::before {
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 85, 140, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 85, 140, 0.05) 0%, transparent 50%);
}

[data-theme="dark"] .inspection-card,
[data-theme="dark"] .loading-state,
[data-theme="dark"] .no-results,
[data-theme="dark"] .error {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
}

/* Modo escuro - borda hover */
[data-theme="dark"] .inspection-card:hover {
  border-color: var(--primary-color);
  transition: border-color 0.2s ease;
}

[data-theme="dark"] .search-bar input {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--foreground-color);
}

[data-theme="dark"] .search-bar input:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-color);
}

/* Design Responsivo */
@media (max-width: 768px) {
  .container {
    padding: clamp(0.75rem, 3vw, 1rem);
    gap: clamp(1rem, 2.5vw, 1.25rem);
  }

  .container .actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .container .actions button {
    min-width: 100%;
    flex: none;
  }

  .tags {
    gap: 0.5rem;
  }

  .tags .tag {
    font-size: 0.7rem;
    padding: 6px 14px;
  }

  .search-bar {
    max-width: 100%;
  }

  .search-bar input {
    padding: 10px 45px 10px 15px;
    font-size: 0.9rem;
  }

  .inspection-card {
    padding: 2rem;
  }

  .inspection-card .top {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .inspection-card .top h3 {
    font-size: 1.1rem;
  }

  .inspection-card .bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
    gap: 1rem;
  }

  .container h1 {
    font-size: 1.5rem;
  }

  .container h2 {
    font-size: 1.25rem;
  }

  .container .actions button {
    min-height: 40px;
    font-size: 0.8rem;
  }

  .tags .tag {
    font-size: 0.65rem;
    padding: 5px 12px;
  }

  .search-bar input {
    padding: 8px 40px 8px 12px;
    font-size: 0.85rem;
  }

  .search-bar img {
    width: 18px;
    height: 18px;
    right: 12px;
  }

  .inspection-card .icons img {
    width: 24px;
    height: 24px;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Estados de Foco */
.container .actions button:focus,
.tags .tag:focus,
.inspection-card:focus,
.search-bar input:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Estilos para Impressão */
@media print {
  .container .actions,
  .search-filter {
    display: none !important;
  }

  .inspection-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    background: white !important;
  }
}

/* Loading States */
.no-results {
  grid-column: 1 / -1;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--muted-foreground);
  font-style: italic;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.inspection-card.hidden {
  display: none;
}

/* Estilos antigos removidos - substituídos pelos novos estilos modernos */

.search-filter {
  display: flex;
  gap: clamp(0.5rem, 2vw, 0.75rem);
  align-items: center;
}

.search-bar {
  position: relative;
  flex: 1;
  max-width: 100%;
}

.search-bar input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  font-size: 0.875rem;
  background-color: var(--background-color);
  color: var(--foreground-color);
  transition: all 0.2s ease;
  font-family: inherit;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
}

.search-bar input::placeholder {
  color: #71717a;
  font-weight: 400;
}

.search-bar img {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  opacity: 0.6;
  pointer-events: none;
}

.search-filter > img {
  width: 40px;
  height: 40px;
  padding: 0.5rem;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--background-color);
}

.search-filter > img:hover {
  border-color: var(--primary-color);
  background-color: #f8f9fa;
  transform: scale(1.05);
}

.inspection-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  background-color: transparent;
}

.inspection-list.empty {
  padding: 3rem 1.5rem;
  text-align: center;
  color: var(--muted-foreground-color);
  font-style: italic;
}

.list-separator {
  width: 100%;
  height: 1px;
  background-color: #e4e4e7;
  margin: 0;
}

/* Definição duplicada removida - usar apenas a principal */

/* Efeito hover consistente */
.inspection-card:hover {
  border-color: var(--primary-color);
}

.inspection-card:active {
  transform: scale(0.98);
}

.inspection-card.hidden {
  display: none;
}

.inspection-card .top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.inspection-card .top h3 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
  flex: 1;
  line-height: 1.3;
  font-size: 1rem;
}

.inspection-card .status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.inspection-card .status[data-status*="EM ANDAMENTO"]::before {
  background-color: var(--ongoing-color);
}

.inspection-card .status[data-status*="ABERTA"]::before,
.inspection-card .status[data-status*="NÃO INICIADA"]::before {
  background-color: var(--neutral-color);
}

.inspection-card .status[data-status*="CONCLUÍDA"]::before,
.inspection-card .status[data-status*="CONCLUÍDO"]::before {
  background-color: var(--success-color);
}

.inspection-card .status p {
  margin: 0;
  font-size: 0.75rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  color: #71717a;
}

.inspection-card .middle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inspection-card .middle p {
  margin: 0;
  color: #71717a;
  line-height: 1.4;
  font-size: 0.875rem;
  font-weight: 400;
}

.inspection-card .bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.inspection-card .bottom .date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inspection-card .bottom .date p {
  margin: 0;
  color: #71717a;
  font-size: 0.875rem;
  font-weight: 400;
}

.inspection-card .icons {
  display: flex;
  gap: -2px;
  align-items: center;
}

.inspection-card .icons img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid var(--background-color);
  margin-left: -2px;
}

.inspection-card .icons img:first-child {
  margin-left: 0;
}

.no-results {
  padding: 2rem;
  text-align: center;
  color: var(--muted-foreground-color);
  font-style: italic;
  background-color: transparent;
}

.loading-state {
  padding: 2rem;
  text-align: center;
  color: var(--muted-foreground-color);
  background-color: transparent;
}

.loading-state::after {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--muted-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-left: 0.5rem;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.status span {
  height: 12px;
  aspect-ratio: 1;
  border-radius: 50%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0.75rem;
  }

  .container .actions {
    flex-direction: column;
  }

  .container .actions button {
    flex: none;
    width: 100%;
  }

  .search-filter {
    align-items: stretch;
    gap: 0.75rem;
  }

  .search-filter > img {
    align-self: center;
    width: 40px;
    height: 40px;
  }

  .inspection-card .top {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .inspection-card .status {
    align-self: flex-end;
  }

  .tags {
    justify-content: flex-start;
    padding-bottom: 0.5rem;
  }
}

.inspection-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.search-bar input:focus,
.search-filter > img:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

* {
  transition: opacity 0.2s ease, transform 0.2s ease, border-color 0.2s ease,
    background-color 0.2s ease, box-shadow 0.2s ease;
}
