.container {
  display: flex;
  flex-direction: column;
  gap: clamp(1.25rem, 3vw, 1.5rem);
  padding: clamp(1rem, 4vw, 1.5rem);
  max-width: 100%;
  margin: 0 auto;
  min-height: calc(100vh - 64px);
}

.container .near {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 3vw, 1.25rem);
}

.container .too {
  gap: clamp(0.75rem, 2vw, 1rem);
}

.container h1 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
}

.container h2 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
}

.container .actions {
  display: flex;
  gap: clamp(0.5rem, 2vw, 0.75rem);
  flex-wrap: wrap;
}

.container .actions button {
  flex: 1;
  min-width: clamp(140px, 30vw, 180px);
  min-height: 44px;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 600;
}

.container .actions .btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--background-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 85, 140, 0.2);
}

.container .actions .btn-primary:hover {
  background-color: #004070;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 85, 140, 0.3);
}

.tags {
  display: flex;
  gap: clamp(0.5rem, 2vw, 0.75rem);
  overflow-x: auto;
  padding: 0.25rem 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tags::-webkit-scrollbar {
  display: none;
}

.tags .tag {
  white-space: nowrap;
  flex-shrink: 0;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 0.75rem;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.tags .tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tags .tag.active {
  background-color: var(--primary-color);
  color: var(--background-color);
  border-color: var(--primary-color);
}

.tags .tag.ongoing {
  border-color: var(--ongoing-color);
  color: var(--ongoing-color);
}

.tags .tag.ongoing.active {
  background-color: var(--ongoing-color);
  color: var(--background-color);
}

.tags .tag.neutral {
  border-color: var(--neutral-color);
  color: var(--neutral-color);
}

.tags .tag.neutral.active {
  background-color: var(--neutral-color);
  color: var(--background-color);
}

.tags .tag.success {
  border-color: var(--success-color);
  color: var(--success-color);
}

.tags .tag.success.active {
  background-color: var(--success-color);
  color: var(--background-color);
}

.search-filter {
  display: flex;
  gap: clamp(0.5rem, 2vw, 0.75rem);
  align-items: center;
}

.search-bar {
  position: relative;
  flex: 1;
  max-width: 100%;
}

.search-bar input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  font-size: 0.875rem;
  background-color: var(--background-color);
  color: var(--foreground-color);
  transition: all 0.2s ease;
  font-family: inherit;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 85, 140, 0.1);
}

.search-bar input::placeholder {
  color: #71717a;
  font-weight: 400;
}

.search-bar img {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  opacity: 0.6;
  pointer-events: none;
}

.search-filter > img {
  width: 40px;
  height: 40px;
  padding: 0.5rem;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--background-color);
}

.search-filter > img:hover {
  border-color: var(--primary-color);
  background-color: #f8f9fa;
  transform: scale(1.05);
}

.inspection-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  background-color: transparent;
}

.inspection-list.empty {
  padding: 3rem 1.5rem;
  text-align: center;
  color: var(--muted-foreground-color);
  font-style: italic;
}

.list-separator {
  width: 100%;
  height: 1px;
  background-color: #e4e4e7;
  margin: 0;
}

.inspection-card {
  cursor: pointer;
  padding: clamp(1rem, 3vw, 1.25rem) 0;
  display: flex;
  flex-direction: column;
  gap: clamp(0.5rem, 1.5vw, 0.75rem);
  background-color: transparent;
  transition: all 0.2s ease;
  position: relative;
}

.inspection-card:hover {
  background-color: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.inspection-card:active {
  transform: scale(0.98);
}

.inspection-card.hidden {
  display: none;
}

.inspection-card .top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.inspection-card .top h3 {
  margin: 0;
  color: var(--foreground-color);
  font-weight: 600;
  flex: 1;
  line-height: 1.3;
  font-size: 1rem;
}

.inspection-card .status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.inspection-card .status[data-status*="EM ANDAMENTO"]::before {
  background-color: var(--ongoing-color);
}

.inspection-card .status[data-status*="ABERTA"]::before,
.inspection-card .status[data-status*="NÃO INICIADA"]::before {
  background-color: var(--neutral-color);
}

.inspection-card .status[data-status*="CONCLUÍDA"]::before,
.inspection-card .status[data-status*="CONCLUÍDO"]::before {
  background-color: var(--success-color);
}

.inspection-card .status p {
  margin: 0;
  font-size: 0.75rem;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  color: #71717a;
}

.inspection-card .middle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inspection-card .middle p {
  margin: 0;
  color: #71717a;
  line-height: 1.4;
  font-size: 0.875rem;
  font-weight: 400;
}

.inspection-card .bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.inspection-card .bottom .date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.inspection-card .bottom .date p {
  margin: 0;
  color: #71717a;
  font-size: 0.875rem;
  font-weight: 400;
}

.inspection-card .icons {
  display: flex;
  gap: -2px;
  align-items: center;
}

.inspection-card .icons img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid var(--background-color);
  margin-left: -2px;
}

.inspection-card .icons img:first-child {
  margin-left: 0;
}

.no-results {
  padding: 2rem;
  text-align: center;
  color: var(--muted-foreground-color);
  font-style: italic;
  background-color: transparent;
}

.loading-state {
  padding: 2rem;
  text-align: center;
  color: var(--muted-foreground-color);
  background-color: transparent;
}

.loading-state::after {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--muted-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-left: 0.5rem;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.status span {
  height: 12px;
  aspect-ratio: 1;
  border-radius: 50%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0.75rem;
  }

  .container .actions {
    flex-direction: column;
  }

  .container .actions button {
    flex: none;
    width: 100%;
  }

  .search-filter {
    align-items: stretch;
    gap: 0.75rem;
  }

  .search-filter > img {
    align-self: center;
    width: 40px;
    height: 40px;
  }

  .inspection-card .top {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .inspection-card .status {
    align-self: flex-end;
  }

  .tags {
    justify-content: flex-start;
    padding-bottom: 0.5rem;
  }
}

.inspection-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.search-bar input:focus,
.search-filter > img:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

* {
  transition: opacity 0.2s ease, transform 0.2s ease, border-color 0.2s ease,
    background-color 0.2s ease, box-shadow 0.2s ease;
}
