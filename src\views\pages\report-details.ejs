<link rel="stylesheet" href="/css/report-details.css" />

<!-- CSS específico para PDF -->
<script>
  
  const urlParams = new URLSearchParams(window.location.search);
  const isPDF = urlParams.get('pdf') === 'true';

  if (isPDF) {
    
    document.documentElement.classList.add('pdf-mode');

    const pdfStyles = document.createElement('style');
    pdfStyles.textContent = `
      /* Remover elementos que não devem aparecer no PDF */
      .pdf-mode .actions {
        display: none !important;
      }

      .pdf-mode .shortcut-container {
        display: none !important;
      }

      .pdf-mode nav {
        display: none !important;
      }

      .pdf-mode header {
        display: none !important;
      }

      /* Manter layout original mas otimizar para PDF */
      .pdf-mode body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f5f5f5;
        margin: 0;
        padding: 20px;
      }

      .pdf-mode .container {
        max-width: none;
        margin: 0;
        padding: 0;
      }

      /* Header do relatório */
      .pdf-mode .report-header {
        background: #00558C;
        color: white;
        padding: 30px;
        margin-bottom: 30px;
        border-radius: 8px;
      }

      .pdf-mode .report-title h2 {
        color: white;
        font-size: 28px;
        margin-bottom: 15px;
        margin-top: 0;
      }

      .pdf-mode .subtitle {
        color: rgba(255,255,255,0.9);
        font-size: 16px;
        margin-bottom: 25px;
      }

      .pdf-mode .meta-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .pdf-mode .meta-item .label {
        color: rgba(255,255,255,0.8) !important;
        font-size: 14px;
        margin-bottom: 5px;
      }

      .pdf-mode .meta-item .value {
        color: white !important;
        font-weight: 600;
        font-size: 16px;
      }

      /* Seções do relatório */
      .pdf-mode .section {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 25px;
        padding: 25px;
        break-inside: avoid;
        page-break-inside: avoid;
      }

      .pdf-mode .section-title {
        color: #00558C;
        font-size: 20px;
        font-weight: 600;
        border-bottom: 2px solid #00558C;
        padding-bottom: 10px;
        margin-bottom: 20px;
        margin-top: 0;
      }

      /* Grid de informações */
      .pdf-mode .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .pdf-mode .info-item {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #00558C;
      }

      .pdf-mode .info-item .label {
        color: #666;
        font-size: 14px;
        margin-bottom: 5px;
        font-weight: 500;
      }

      .pdf-mode .info-item .value {
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }

      /* Grid de fotos */
      .pdf-mode .photo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 25px 0;
        break-inside: avoid;
        page-break-inside: avoid;
      }

      .pdf-mode .photo-item {
        break-inside: avoid;
        page-break-inside: avoid;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .pdf-mode .photo-item img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        display: block;
      }

      /* Garantir que cores sejam preservadas */
      .pdf-mode * {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }

    `;
    document.head.appendChild(pdfStyles);
  }
</script>

<div class="container">
  <div class="header">
    <h1 class="h4">Preview do Relatório</h1>
    <div class="actions">
      <button class="btn-primary" id="download-btn">
        <img src="/assets/icons/download.svg" alt="Download">
        Baixar PDF
      </button>
    </div>
  </div>

  <div class="loading-state" id="loading">
    <div class="spinner"></div>
    <p>Carregando preview do relatório...</p>
  </div>

  <div class="report-preview" id="report-preview" style="display: none;">
    <!-- Cabeçalho do Relatório -->
    <div class="report-header">
      <div class="report-title">
        <h2 id="report-title">Relatório de Inspeção Predial</h2>
        <p class="subtitle" id="report-subtitle">Gerado automaticamente</p>
      </div>
      <div class="report-meta">
        <div class="meta-item">
          <span class="label">Data de Geração:</span>
          <span class="value" id="generation-date">-</span>
        </div>
        <div class="meta-item">
          <span class="label">Inspeção:</span>
          <span class="value" id="inspection-name">-</span>
        </div>
        <div class="meta-item">
          <span class="label">Equipe:</span>
          <span class="value" id="team-name">-</span>
        </div>
        <div class="meta-item">
          <span class="label">Código do Projeto:</span>
          <span class="value" id="project-code">-</span>
        </div>
      </div>
    </div>

    <!-- Informações Básicas -->
    <div class="section">
      <h3 class="section-title">Informações da Inspeção</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">Endereço:</span>
          <span class="value" id="address">-</span>
        </div>
        <div class="info-item">
          <span class="label">Tipo de Edificação:</span>
          <span class="value" id="building-type">-</span>
        </div>
        <div class="info-item">
          <span class="label">Status:</span>
          <span class="value" id="status">-</span>
        </div>
        <div class="info-item">
          <span class="label">Data de Criação:</span>
          <span class="value" id="creation-date">-</span>
        </div>
      </div>
    </div>

    <!-- Estatísticas -->
    <div class="section">
      <h3 class="section-title">Estatísticas</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number" id="total-environments">0</div>
          <div class="stat-label">Ambientes Inspecionados</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="total-occurrences">0</div>
          <div class="stat-label">Ocorrências Encontradas</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="affected-environments">0</div>
          <div class="stat-label">Ambientes Afetados</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="affected-percentage">0%</div>
          <div class="stat-label">Percentual Afetado</div>
        </div>
      </div>
    </div>



    <!-- Ambientes Detalhados -->
    <div class="section">
      <h3 class="section-title">🏠 Ambientes Inspecionados - Detalhado</h3>
      <div class="detailed-environments" id="detailed-environments">
        <p class="empty-state">Nenhum ambiente encontrado.</p>
      </div>
    </div>

    <!-- Ocorrências Detalhadas -->
    <div class="section">
      <h3 class="section-title">⚠️ Ocorrências Registradas - Detalhado</h3>
      <div class="detailed-occurrences" id="detailed-occurrences">
        <p class="empty-state">Nenhuma ocorrência encontrada.</p>
      </div>
    </div>

  </div>

  <div class="error-state" id="error-state" style="display: none;">
    <div class="error-icon">⚠️</div>
    <h3>Erro ao carregar relatório</h3>
    <p id="error-message">Não foi possível carregar os dados do relatório.</p>
    <button class="btn-primary" onclick="location.reload()">Tentar Novamente</button>
  </div>
</div>

<script src="/js/report-details.js"></script>